import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../presentation/widgets/dashboard_stats_section.dart';

// Dashboard stats provider
final dashboardStatsProvider = FutureProvider<DashboardStats>((ref) async {
  // This would typically fetch from an API service
  // For now, return mock data
  await Future.delayed(const Duration(seconds: 1));
  
  return DashboardStats(
    totalProperties: 25,
    activeIssues: 8,
    attendanceRate: 87.5,
    fuelEfficiency: 3.2,
    propertiesTrend: 2.5,
    issuesTrend: -1.2,
    attendanceTrend: 1.8,
    fuelTrend: -0.5,
  );
});

// Dashboard summary provider
final dashboardSummaryProvider = FutureProvider<DashboardSummary>((ref) async {
  await Future.delayed(const Duration(milliseconds: 800));
  
  return DashboardSummary(
    totalRevenue: 125000.0,
    monthlyGrowth: 8.5,
    activeContracts: 18,
    pendingTasks: 12,
  );
});

// Recent activities provider
final recentActivitiesProvider = FutureProvider<List<DashboardActivity>>((ref) async {
  await Future.delayed(const Duration(milliseconds: 600));
  
  return [
    DashboardActivity(
      id: '1',
      title: 'New maintenance request',
      description: 'Generator issue at Property A',
      timestamp: DateTime.now().subtract(const Duration(minutes: 15)),
      type: ActivityType.maintenance,
    ),
    DashboardActivity(
      id: '2',
      title: 'Attendance marked',
      description: 'John Doe checked in at Property B',
      timestamp: DateTime.now().subtract(const Duration(hours: 1)),
      type: ActivityType.attendance,
    ),
    DashboardActivity(
      id: '3',
      title: 'Fuel log updated',
      description: 'Fuel added to Generator #3',
      timestamp: DateTime.now().subtract(const Duration(hours: 2)),
      type: ActivityType.fuel,
    ),
  ];
});

// Dashboard models
class DashboardSummary {
  final double totalRevenue;
  final double monthlyGrowth;
  final int activeContracts;
  final int pendingTasks;

  DashboardSummary({
    required this.totalRevenue,
    required this.monthlyGrowth,
    required this.activeContracts,
    required this.pendingTasks,
  });

  factory DashboardSummary.fromJson(Map<String, dynamic> json) {
    return DashboardSummary(
      totalRevenue: (json['totalRevenue'] ?? 0.0).toDouble(),
      monthlyGrowth: (json['monthlyGrowth'] ?? 0.0).toDouble(),
      activeContracts: json['activeContracts'] ?? 0,
      pendingTasks: json['pendingTasks'] ?? 0,
    );
  }
}

class DashboardActivity {
  final String id;
  final String title;
  final String description;
  final DateTime timestamp;
  final ActivityType type;

  DashboardActivity({
    required this.id,
    required this.title,
    required this.description,
    required this.timestamp,
    required this.type,
  });

  factory DashboardActivity.fromJson(Map<String, dynamic> json) {
    return DashboardActivity(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      timestamp: DateTime.parse(json['timestamp']),
      type: ActivityType.values.firstWhere(
        (type) => type.name == json['type'],
        orElse: () => ActivityType.general,
      ),
    );
  }
}

enum ActivityType {
  maintenance,
  attendance,
  fuel,
  property,
  user,
  general,
}
