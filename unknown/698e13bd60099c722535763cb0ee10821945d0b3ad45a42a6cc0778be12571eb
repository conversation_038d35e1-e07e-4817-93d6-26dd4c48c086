import 'package:json_annotation/json_annotation.dart';

part 'dashboard_models.g.dart';

@JsonSerializable()
class DashboardStatus {
  final bool success;
  final DashboardData data;

  const DashboardStatus({
    required this.success,
    required this.data,
  });

  factory DashboardStatus.fromJson(Map<String, dynamic> json) => _$DashboardStatusFromJson(json);
  Map<String, dynamic> toJson() => _$DashboardStatusToJson(this);
}

@JsonSerializable()
class DashboardData {
  final PropertyStats properties;
  @JsonKey(name: 'maintenance_issues')
  final MaintenanceStats maintenanceIssues;
  @JsonKey(name: 'recent_alerts')
  final List<RecentAlert> recentAlerts;

  const DashboardData({
    required this.properties,
    required this.maintenanceIssues,
    required this.recentAlerts,
  });

  factory DashboardData.fromJson(Map<String, dynamic> json) => _$DashboardDataFromJson(json);
  Map<String, dynamic> toJson() => _$DashboardDataToJson(this);
}

@JsonSerializable()
class PropertyStats {
  final int total;
  final int operational;
  final int warning;
  final int critical;

  const PropertyStats({
    required this.total,
    required this.operational,
    required this.warning,
    required this.critical,
  });

  factory PropertyStats.fromJson(Map<String, dynamic> json) => _$PropertyStatsFromJson(json);
  Map<String, dynamic> toJson() => _$PropertyStatsToJson(this);
}

@JsonSerializable()
class MaintenanceStats {
  final int total;
  final int open;
  @JsonKey(name: 'in_progress')
  final int inProgress;
  final int critical;

  const MaintenanceStats({
    required this.total,
    required this.open,
    required this.inProgress,
    required this.critical,
  });

  factory MaintenanceStats.fromJson(Map<String, dynamic> json) => _$MaintenanceStatsFromJson(json);
  Map<String, dynamic> toJson() => _$MaintenanceStatsToJson(this);
}

@JsonSerializable()
class RecentAlert {
  final String id;
  final String type;
  final String severity;
  final String message;
  @JsonKey(name: 'property_name')
  final String propertyName;
  final DateTime timestamp;

  const RecentAlert({
    required this.id,
    required this.type,
    required this.severity,
    required this.message,
    required this.propertyName,
    required this.timestamp,
  });

  factory RecentAlert.fromJson(Map<String, dynamic> json) => _$RecentAlertFromJson(json);
  Map<String, dynamic> toJson() => _$RecentAlertToJson(this);
}
