import 'package:flutter/material.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/utils/app_utils.dart';
import '../../domain/dashboard_models.dart';

class RecentAlertsWidget extends StatelessWidget {
  final List<RecentAlert> alerts;

  const RecentAlertsWidget({
    super.key,
    required this.alerts,
  });

  @override
  Widget build(BuildContext context) {
    if (alerts.isEmpty) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            children: [
              Icon(
                Icons.check_circle,
                size: 48,
                color: Colors.green.shade300,
              ),
              const SizedBox(height: AppConstants.smallPadding),
              Text(
                'No recent alerts',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              Text(
                'All systems are running smoothly',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
          ),
        ),
      );
    }

    return Card(
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Recent Alerts (${alerts.length})',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                if (alerts.length > 3)
                  TextButton(
                    onPressed: () {
                      // Navigate to full alerts page
                    },
                    child: const Text('View All'),
                  ),
              ],
            ),
          ),
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: alerts.length > 3 ? 3 : alerts.length,
            separatorBuilder: (context, index) => const Divider(height: 1),
            itemBuilder: (context, index) {
              final alert = alerts[index];
              return ListTile(
                leading: CircleAvatar(
                  backgroundColor: _getAlertColor(alert.severity).withValues(alpha: 0.1),
                  child: Icon(
                    _getAlertIcon(alert.type),
                    color: _getAlertColor(alert.severity),
                    size: 20,
                  ),
                ),
                title: Text(
                  alert.message,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      alert.propertyName,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Text(
                      AppUtils.formatRelativeTime(alert.timestamp),
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ],
                ),
                trailing: Chip(
                  label: Text(
                    AppUtils.capitalize(alert.severity),
                    style: TextStyle(
                      color: _getAlertColor(alert.severity),
                      fontSize: 12,
                    ),
                  ),
                  backgroundColor: _getAlertColor(alert.severity).withValues(alpha: 0.1),
                  materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Color _getAlertColor(String severity) {
    switch (severity.toLowerCase()) {
      case 'critical':
        return Colors.red;
      case 'high':
        return Colors.red.shade700;
      case 'medium':
        return Colors.orange;
      case 'low':
        return Colors.blue;
      case 'info':
        return Colors.blue;
      case 'success':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  IconData _getAlertIcon(String type) {
    switch (type.toLowerCase()) {
      case 'maintenance':
        return Icons.build;
      case 'fuel':
        return Icons.local_gas_station;
      case 'payment':
        return Icons.payment;
      case 'service':
        return Icons.settings;
      case 'security':
        return Icons.security;
      case 'critical':
        return Icons.error;
      case 'warning':
        return Icons.warning;
      case 'info':
        return Icons.info;
      case 'success':
        return Icons.check_circle;
      default:
        return Icons.notifications;
    }
  }
}
