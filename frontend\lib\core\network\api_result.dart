/// Generic result wrapper for API responses
class ApiResult<T> {
  final T? _data;
  final String? _error;
  final bool _isSuccess;

  const ApiResult._(this._data, this._error, this._isSuccess);

  /// Create a successful result
  factory ApiResult.success(T data) {
    return ApiResult._(data, null, true);
  }

  /// Create an error result
  factory ApiResult.error(String error) {
    return ApiResult._(null, error, false);
  }

  /// Check if the result is successful
  bool get isSuccess => _isSuccess;

  /// Check if the result is an error
  bool get isError => !_isSuccess;

  /// Get the data (only available if successful)
  T? get data => _data;

  /// Get the error message (only available if error)
  String? get error => _error;

  /// Transform the result using a callback
  R when<R>({
    required R Function(T data) success,
    required R Function(String error) error,
  }) {
    if (_isSuccess && _data != null) {
      return success(_data as T);
    } else {
      return error(_error ?? 'Unknown error');
    }
  }

  /// Map the data to a different type
  ApiResult<R> map<R>(R Function(T data) mapper) {
    if (_isSuccess && _data != null) {
      try {
        return ApiResult.success(mapper(_data as T));
      } catch (e) {
        return ApiResult.error('Mapping error: $e');
      }
    } else {
      return ApiResult.error(_error ?? 'Unknown error');
    }
  }

  /// Chain another operation that returns an ApiResult
  ApiResult<R> flatMap<R>(ApiResult<R> Function(T data) mapper) {
    if (_isSuccess && _data != null) {
      try {
        return mapper(_data as T);
      } catch (e) {
        return ApiResult.error('FlatMap error: $e');
      }
    } else {
      return ApiResult.error(_error ?? 'Unknown error');
    }
  }

  /// Get data or throw an exception
  T getOrThrow() {
    if (_isSuccess && _data != null) {
      return _data as T;
    } else {
      throw Exception(_error ?? 'Unknown error');
    }
  }

  /// Get data or return a default value
  T getOrElse(T defaultValue) {
    if (_isSuccess && _data != null) {
      return _data as T;
    } else {
      return defaultValue;
    }
  }

  @override
  String toString() {
    if (_isSuccess) {
      return 'ApiResult.success($_data)';
    } else {
      return 'ApiResult.error($_error)';
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ApiResult<T> &&
        other._data == _data &&
        other._error == _error &&
        other._isSuccess == _isSuccess;
  }

  @override
  int get hashCode {
    return _data.hashCode ^ _error.hashCode ^ _isSuccess.hashCode;
  }
}
