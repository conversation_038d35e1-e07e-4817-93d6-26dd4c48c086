import { NextRequest } from 'next/server';
import { requireAuth } from '@/lib/auth';
import { createApiResponse, getRequestBody, handleError, corsHeaders } from '@/lib/utils';
import { prisma } from '@/lib/prisma';
import { validateRequest } from '@/lib/validation';
import Jo<PERSON> from 'joi';

const updateScreenPermissionSchema = Joi.object({
  required_permissions: Joi.array().items(Joi.string()).optional(),
  allowed_roles: Joi.array().items(Joi.string()).optional(),
  is_enabled: Joi.boolean().optional(),
});

async function getScreenPermissionHandler(
  request: NextRequest,
  context: { params: { screenName: string } },
  currentUser: any
) {
  try {
    const { screenName } = context.params;

    const screenPermission = await prisma.screenPermission.findUnique({
      where: { screenName: decodeURIComponent(screenName) },
    });

    if (!screenPermission) {
      return Response.json(
        createApiResponse(null, 'Screen permission not found', 'NOT_FOUND'),
        { status: 404 }
      );
    }

    return Response.json(
      createApiResponse({
        permission: {
          id: screenPermission.id,
          screen_name: screenPermission.screenName,
          required_permissions: screenPermission.requiredPermissions,
          allowed_roles: screenPermission.allowedRoles,
          is_enabled: screenPermission.isEnabled,
          created_at: screenPermission.createdAt,
          updated_at: screenPermission.updatedAt,
        },
      }),
      {
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to get screen permission');
  }
}

async function updateScreenPermissionHandler(
  request: NextRequest,
  context: { params: { screenName: string } },
  currentUser: any
) {
  try {
    // Check if user has admin permissions
    const hasAdminRole = currentUser.userRoles?.some((ur: any) => ur.role.name === 'admin');
    if (!hasAdminRole) {
      return Response.json(
        createApiResponse(null, 'Insufficient permissions', 'FORBIDDEN'),
        { status: 403 }
      );
    }

    const { screenName } = context.params;
    const body = await getRequestBody(request);
    
    // Validate request body
    const validation = validateRequest(updateScreenPermissionSchema, body);
    if (!validation.isValid) {
      return Response.json(
        createApiResponse(null, 'Validation failed', 'VALIDATION_ERROR'),
        { status: 400 }
      );
    }

    const updateData: any = {};
    if (validation.data.required_permissions !== undefined) {
      updateData.requiredPermissions = validation.data.required_permissions;
    }
    if (validation.data.allowed_roles !== undefined) {
      updateData.allowedRoles = validation.data.allowed_roles;
    }
    if (validation.data.is_enabled !== undefined) {
      updateData.isEnabled = validation.data.is_enabled;
    }

    const screenPermission = await prisma.screenPermission.update({
      where: { screenName: decodeURIComponent(screenName) },
      data: updateData,
    });

    return Response.json(
      createApiResponse({
        permission: {
          id: screenPermission.id,
          screen_name: screenPermission.screenName,
          required_permissions: screenPermission.requiredPermissions,
          allowed_roles: screenPermission.allowedRoles,
          is_enabled: screenPermission.isEnabled,
          created_at: screenPermission.createdAt,
          updated_at: screenPermission.updatedAt,
        },
      }),
      {
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to update screen permission');
  }
}

async function deleteScreenPermissionHandler(
  request: NextRequest,
  context: { params: { screenName: string } },
  currentUser: any
) {
  try {
    // Check if user has admin permissions
    const hasAdminRole = currentUser.userRoles?.some((ur: any) => ur.role.name === 'admin');
    if (!hasAdminRole) {
      return Response.json(
        createApiResponse(null, 'Insufficient permissions', 'FORBIDDEN'),
        { status: 403 }
      );
    }

    const { screenName } = context.params;

    await prisma.screenPermission.delete({
      where: { screenName: decodeURIComponent(screenName) },
    });

    return Response.json(
      createApiResponse({ message: 'Screen permission deleted successfully' }),
      {
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to delete screen permission');
  }
}

// GET /api/permissions/screens/[screenName] - Get specific screen permission
export const GET = requireAuth(getScreenPermissionHandler);

// PUT /api/permissions/screens/[screenName] - Update screen permission
export const PUT = requireAuth(updateScreenPermissionHandler);

// DELETE /api/permissions/screens/[screenName] - Delete screen permission
export const DELETE = requireAuth(deleteScreenPermissionHandler);

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: corsHeaders(),
  });
}
