import { NextRequest } from 'next/server';
import { requireAuth } from '@/lib/auth';
import { createApiResponse, handleError, corsHeaders } from '@/lib/utils';
import { NotificationService } from '@/lib/monitoring/notification-service';

const notificationService = new NotificationService();

async function markAsReadHandler(
  request: NextRequest, 
  context: { params: { id: string } }, 
  currentUser: any
) {
  try {
    const { id } = context.params;

    await notificationService.markAsRead(id, currentUser.id);

    return Response.json(
      createApiResponse({ message: 'Notification marked as read' }),
      {
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to mark notification as read');
  }
}

// POST /api/notifications/[id]/read - Mark notification as read
export const POST = requireAuth(markAsReadHandler);

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: corsHeaders(),
  });
}
