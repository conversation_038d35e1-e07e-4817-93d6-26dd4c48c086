import { NextRequest } from 'next/server';

export interface SSEClient {
  id: string;
  userId: string;
  controller: ReadableStreamDefaultController;
  lastHeartbeat: number;
}

export interface SSEMessage {
  type: string;
  data: any;
  id?: string;
  retry?: number;
}

class SSEManager {
  private clients: Map<string, SSEClient> = new Map();
  private heartbeatInterval: NodeJS.Timeout | null = null;

  constructor() {
    this.startHeartbeat();
  }

  addClient(clientId: string, userId: string, controller: ReadableStreamDefaultController): void {
    const client: SSEClient = {
      id: clientId,
      userId,
      controller,
      lastHeartbeat: Date.now(),
    };

    this.clients.set(clientId, client);
    console.log(`SSE client connected: ${clientId} for user: ${userId}`);

    // Send initial connection message
    this.sendToClient(clientId, {
      type: 'connection',
      data: { status: 'connected', clientId, timestamp: new Date().toISOString() }
    });
  }

  removeClient(clientId: string): void {
    const client = this.clients.get(clientId);
    if (client) {
      try {
        client.controller.close();
      } catch (error) {
        console.error('Error closing SSE client controller:', error);
      }
      this.clients.delete(clientId);
      console.log(`SSE client disconnected: ${clientId}`);
    }
  }

  sendToClient(clientId: string, message: SSEMessage): boolean {
    const client = this.clients.get(clientId);
    if (!client) {
      return false;
    }

    try {
      const sseData = this.formatSSEMessage(message);
      client.controller.enqueue(new TextEncoder().encode(sseData));
      client.lastHeartbeat = Date.now();
      return true;
    } catch (error) {
      console.error(`Error sending message to client ${clientId}:`, error);
      this.removeClient(clientId);
      return false;
    }
  }

  sendToUser(userId: string, message: SSEMessage): number {
    let sentCount = 0;
    for (const [clientId, client] of this.clients) {
      if (client.userId === userId) {
        if (this.sendToClient(clientId, message)) {
          sentCount++;
        }
      }
    }
    return sentCount;
  }

  sendToAllUsers(message: SSEMessage): number {
    let sentCount = 0;
    for (const [clientId] of this.clients) {
      if (this.sendToClient(clientId, message)) {
        sentCount++;
      }
    }
    return sentCount;
  }

  broadcast(message: SSEMessage, excludeUserId?: string): number {
    let sentCount = 0;
    for (const [clientId, client] of this.clients) {
      if (!excludeUserId || client.userId !== excludeUserId) {
        if (this.sendToClient(clientId, message)) {
          sentCount++;
        }
      }
    }
    return sentCount;
  }

  private formatSSEMessage(message: SSEMessage): string {
    let sseData = '';
    
    if (message.id) {
      sseData += `id: ${message.id}\n`;
    }
    
    if (message.retry) {
      sseData += `retry: ${message.retry}\n`;
    }
    
    sseData += `event: ${message.type}\n`;
    sseData += `data: ${JSON.stringify(message.data)}\n\n`;
    
    return sseData;
  }

  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      const now = Date.now();
      const staleClients: string[] = [];

      // Check for stale clients (no heartbeat for 60 seconds)
      for (const [clientId, client] of this.clients) {
        if (now - client.lastHeartbeat > 60000) {
          staleClients.push(clientId);
        } else {
          // Send heartbeat to active clients
          this.sendToClient(clientId, {
            type: 'heartbeat',
            data: { timestamp: new Date().toISOString() }
          });
        }
      }

      // Remove stale clients
      staleClients.forEach(clientId => this.removeClient(clientId));
    }, 30000); // Send heartbeat every 30 seconds
  }

  getClientCount(): number {
    return this.clients.size;
  }

  getUserClients(userId: string): SSEClient[] {
    return Array.from(this.clients.values()).filter(client => client.userId === userId);
  }

  cleanup(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
    }
    
    for (const [clientId] of this.clients) {
      this.removeClient(clientId);
    }
  }
}

// Singleton instance
export const sseManager = new SSEManager();

// Cleanup on process exit
process.on('SIGINT', () => {
  sseManager.cleanup();
  process.exit(0);
});

process.on('SIGTERM', () => {
  sseManager.cleanup();
  process.exit(0);
});
