import Joi from 'joi';

// Auth validation schemas
export const loginSchema = Joi.object({
  identifier: Joi.string().required(),
  password: Joi.string().min(6).required(),
  login_type: Joi.string().valid('email', 'username', 'phone').optional(),
});

export const legacyLoginSchema = Joi.object({
  email: Joi.string().email().required(),
  password: Joi.string().min(6).required(),
});

export const registerSchema = Joi.object({
  email: Joi.string().email().required(),
  username: Joi.string().min(3).max(50).pattern(/^[a-zA-Z0-9_-]+$/).optional(),
  password: Joi.string().min(6).required(),
  full_name: Joi.string().min(2).required(),
  phone: Joi.string().pattern(/^[+]?[1-9]\d{1,14}$/).optional(),
});

// Property validation schemas
export const createPropertySchema = Joi.object({
  name: Joi.string().min(2).required(),
  type: Joi.string().valid('residential', 'office', 'construction_site').required(),
  parent_property_id: Joi.string().uuid().optional(),
  address: Joi.string().optional(),
  description: Joi.string().optional(),

  // Office-specific fields
  capacity: Joi.number().integer().min(1).when('type', { is: 'office', then: Joi.optional(), otherwise: Joi.forbidden() }),
  department: Joi.string().when('type', { is: 'office', then: Joi.optional(), otherwise: Joi.forbidden() }),

  // Site-specific fields
  project_type: Joi.string().when('type', { is: 'construction_site', then: Joi.required(), otherwise: Joi.forbidden() }),
  start_date: Joi.date().when('type', { is: 'construction_site', then: Joi.required(), otherwise: Joi.forbidden() }),
  expected_end_date: Joi.date().when('type', { is: 'construction_site', then: Joi.required(), otherwise: Joi.forbidden() }),
  hourly_rate_standard: Joi.number().positive().when('type', { is: 'construction_site', then: Joi.optional(), otherwise: Joi.forbidden() }),

  // Common fields
  location: Joi.string().optional(),
});

// Maintenance validation schemas
export const createMaintenanceIssueSchema = Joi.object({
  property_id: Joi.string().uuid().required(),
  title: Joi.string().min(2).required(),
  description: Joi.string().min(5).required(),
  priority: Joi.string().valid('low', 'medium', 'high', 'critical').required(),
  service_type: Joi.string().optional(),
  department: Joi.string().optional(),
  due_date: Joi.date().optional(),
});

// Attendance validation schemas
export const submitAttendanceSchema = Joi.object({
  date: Joi.date().required(),
  attendance: Joi.array().items(
    Joi.object({
      worker_id: Joi.string().uuid().required(),
      status: Joi.string().valid('present', 'absent', 'late', 'half_day').required(),
      check_in_time: Joi.string().optional(),
      check_out_time: Joi.string().optional(),
      hours_worked: Joi.number().min(0).max(24).optional(),
      notes: Joi.string().optional(),
    })
  ).required(),
});

// Generator fuel validation schemas
export const createFuelLogSchema = Joi.object({
  fuel_level_liters: Joi.number().min(0).required(),
  consumption_rate: Joi.number().min(0).optional(),
  runtime_hours: Joi.number().min(0).optional(),
  efficiency_percentage: Joi.number().min(0).max(100).optional(),
  notes: Joi.string().optional(),
});

// Pagination validation
export const paginationSchema = Joi.object({
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(10),
});

// Validation helper function
export function validateRequest(schema: Joi.ObjectSchema, data: any) {
  const { error, value } = schema.validate(data, { abortEarly: false });

  if (error) {
    const errors = error.details.map(detail => ({
      field: detail.path.join('.'),
      message: detail.message,
    }));

    return {
      isValid: false,
      errors,
      data: null,
    };
  }

  return {
    isValid: true,
    errors: null,
    data: value,
  };
}
