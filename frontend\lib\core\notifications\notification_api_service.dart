import 'package:dio/dio.dart';
import '../network/api_client.dart';
import '../network/api_result.dart';
import 'sse_notification_service.dart';

class NotificationApiService {
  final ApiClient _apiClient;

  NotificationApiService(this._apiClient);

  /// Get unread notifications
  Future<ApiResult<List<ServerNotification>>> getUnreadNotifications() async {
    try {
      final response = await _apiClient.get('/notifications?unread_only=true');
      
      if (response.data['success'] == true) {
        final List<dynamic> notificationsJson = response.data['data']['notifications'] ?? [];
        final notifications = notificationsJson
            .map((json) => ServerNotification.fromJson(json))
            .toList();
        return ApiResult.success(notifications);
      } else {
        return ApiResult.error(response.data['error'] ?? 'Failed to fetch notifications');
      }
    } on DioException catch (e) {
      return ApiResult.error(e.message ?? 'Network error occurred');
    } catch (e) {
      return ApiResult.error('An unexpected error occurred: $e');
    }
  }

  /// Get all notifications
  Future<ApiResult<List<ServerNotification>>> getAllNotifications() async {
    try {
      final response = await _apiClient.get('/notifications');
      
      if (response.data['success'] == true) {
        final List<dynamic> notificationsJson = response.data['data']['notifications'] ?? [];
        final notifications = notificationsJson
            .map((json) => ServerNotification.fromJson(json))
            .toList();
        return ApiResult.success(notifications);
      } else {
        return ApiResult.error(response.data['error'] ?? 'Failed to fetch notifications');
      }
    } on DioException catch (e) {
      return ApiResult.error(e.message ?? 'Network error occurred');
    } catch (e) {
      return ApiResult.error('An unexpected error occurred: $e');
    }
  }

  /// Mark notification as read
  Future<ApiResult<bool>> markAsRead(String notificationId) async {
    try {
      final response = await _apiClient.post('/notifications/$notificationId/read');
      
      if (response.data['success'] == true) {
        return ApiResult.success(true);
      } else {
        return ApiResult.error(response.data['error'] ?? 'Failed to mark notification as read');
      }
    } on DioException catch (e) {
      return ApiResult.error(e.message ?? 'Network error occurred');
    } catch (e) {
      return ApiResult.error('An unexpected error occurred: $e');
    }
  }

  /// Send monitoring data to backend
  Future<ApiResult<List<ThresholdAlert>>> monitorMetric({
    required String propertyId,
    required String serviceType,
    required String metricName,
    required double value,
    String? unit,
    DateTime? timestamp,
  }) async {
    try {
      final data = {
        'property_id': propertyId,
        'service_type': serviceType,
        'metric_name': metricName,
        'value': value,
        if (unit != null) 'unit': unit,
        if (timestamp != null) 'timestamp': timestamp.toIso8601String(),
      };

      final response = await _apiClient.post('/monitoring', data: data);
      
      if (response.data['success'] == true) {
        final List<dynamic> alertsJson = response.data['data']['alerts'] ?? [];
        final alerts = alertsJson
            .map((json) => ThresholdAlert.fromJson(json))
            .toList();
        return ApiResult.success(alerts);
      } else {
        return ApiResult.error(response.data['error'] ?? 'Failed to monitor metric');
      }
    } on DioException catch (e) {
      return ApiResult.error(e.message ?? 'Network error occurred');
    } catch (e) {
      return ApiResult.error('An unexpected error occurred: $e');
    }
  }

  /// Send multiple monitoring data points
  Future<ApiResult<List<ThresholdAlert>>> monitorMultipleMetrics(
    List<MonitoringDataPoint> metrics,
  ) async {
    try {
      final data = {
        'metrics': metrics.map((m) => m.toJson()).toList(),
      };

      final response = await _apiClient.post('/monitoring/multiple', data: data);
      
      if (response.data['success'] == true) {
        final List<dynamic> alertsJson = response.data['data']['alerts'] ?? [];
        final alerts = alertsJson
            .map((json) => ThresholdAlert.fromJson(json))
            .toList();
        return ApiResult.success(alerts);
      } else {
        return ApiResult.error(response.data['error'] ?? 'Failed to monitor metrics');
      }
    } on DioException catch (e) {
      return ApiResult.error(e.message ?? 'Network error occurred');
    } catch (e) {
      return ApiResult.error('An unexpected error occurred: $e');
    }
  }

  /// Get active alerts
  Future<ApiResult<List<ThresholdAlert>>> getActiveAlerts({String? propertyId}) async {
    try {
      final queryParams = propertyId != null ? '?property_id=$propertyId' : '';
      final response = await _apiClient.get('/monitoring$queryParams');
      
      if (response.data['success'] == true) {
        final List<dynamic> alertsJson = response.data['data']['alerts'] ?? [];
        final alerts = alertsJson
            .map((json) => ThresholdAlert.fromJson(json))
            .toList();
        return ApiResult.success(alerts);
      } else {
        return ApiResult.error(response.data['error'] ?? 'Failed to fetch alerts');
      }
    } on DioException catch (e) {
      return ApiResult.error(e.message ?? 'Network error occurred');
    } catch (e) {
      return ApiResult.error('An unexpected error occurred: $e');
    }
  }
}

/// Monitoring data point model
class MonitoringDataPoint {
  final String propertyId;
  final String serviceType;
  final String metricName;
  final double value;
  final String? unit;
  final DateTime? timestamp;

  const MonitoringDataPoint({
    required this.propertyId,
    required this.serviceType,
    required this.metricName,
    required this.value,
    this.unit,
    this.timestamp,
  });

  Map<String, dynamic> toJson() {
    return {
      'property_id': propertyId,
      'service_type': serviceType,
      'metric_name': metricName,
      'value': value,
      if (unit != null) 'unit': unit,
      if (timestamp != null) 'timestamp': timestamp!.toIso8601String(),
    };
  }

  factory MonitoringDataPoint.fromJson(Map<String, dynamic> json) {
    return MonitoringDataPoint(
      propertyId: json['property_id'] as String,
      serviceType: json['service_type'] as String,
      metricName: json['metric_name'] as String,
      value: (json['value'] as num).toDouble(),
      unit: json['unit'] as String?,
      timestamp: json['timestamp'] != null 
          ? DateTime.parse(json['timestamp'] as String)
          : null,
    );
  }
}

/// Threshold alert model
class ThresholdAlert {
  final String id;
  final String propertyId;
  final String propertyName;
  final String serviceType;
  final String metricName;
  final double currentValue;
  final String thresholdType;
  final double thresholdValue;
  final String message;
  final String severity;
  final DateTime timestamp;
  final bool isResolved;

  const ThresholdAlert({
    required this.id,
    required this.propertyId,
    required this.propertyName,
    required this.serviceType,
    required this.metricName,
    required this.currentValue,
    required this.thresholdType,
    required this.thresholdValue,
    required this.message,
    required this.severity,
    required this.timestamp,
    required this.isResolved,
  });

  factory ThresholdAlert.fromJson(Map<String, dynamic> json) {
    return ThresholdAlert(
      id: json['id'] as String,
      propertyId: json['property_id'] as String,
      propertyName: json['property_name'] as String,
      serviceType: json['service_type'] as String,
      metricName: json['metric_name'] as String,
      currentValue: (json['current_value'] as num).toDouble(),
      thresholdType: json['threshold_type'] as String,
      thresholdValue: (json['threshold_value'] as num).toDouble(),
      message: json['message'] as String,
      severity: json['severity'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      isResolved: json['is_resolved'] as bool,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'property_id': propertyId,
      'property_name': propertyName,
      'service_type': serviceType,
      'metric_name': metricName,
      'current_value': currentValue,
      'threshold_type': thresholdType,
      'threshold_value': thresholdValue,
      'message': message,
      'severity': severity,
      'timestamp': timestamp.toIso8601String(),
      'is_resolved': isResolved,
    };
  }
}
