import { NextRequest } from 'next/server';
import { createApiResponse, corsHeaders } from '@/lib/utils';

export async function GET(request: NextRequest) {
  const apiInfo = {
    name: 'SRSR Property Management API',
    version: '1.0.0',
    description: 'Backend API server for SRSR Property Management System',
    status: 'operational',
    timestamp: new Date().toISOString(),
    endpoints: {
      auth: {
        login: 'POST /api/auth/login',
        register: 'POST /api/auth/register',
        me: 'GET /api/auth/me',
      },
      properties: {
        list: 'GET /api/properties?type=residential|office|construction_site',
        get: 'GET /api/properties/[id]',
        create: 'POST /api/properties',
        update: 'PUT /api/properties/[id]',
        services: 'GET /api/properties/[id]/services',
        members: 'GET /api/properties/[id]/members',
        addMember: 'POST /api/properties/[id]/members',
        attendance: 'GET /api/properties/[id]/attendance',
        recordAttendance: 'POST /api/properties/[id]/attendance',
      },
      maintenance: {
        list: 'GET /api/maintenance',
        get: 'GET /api/maintenance/[id]',
        create: 'POST /api/maintenance',
        update: 'PUT /api/maintenance/[id]',
        assign: 'POST /api/maintenance/[id]/assign',
        escalate: 'POST /api/maintenance/[id]/escalate',
      },
      users: {
        list: 'GET /api/users',
        get: 'GET /api/users/[id]',
        create: 'POST /api/users',
        update: 'PUT /api/users/[id]',
        delete: 'DELETE /api/users/[id]',
        roles: 'GET /api/users/[id]/roles',
      },
      roles: {
        list: 'GET /api/roles',
        get: 'GET /api/roles/[id]',
        create: 'POST /api/roles',
        update: 'PUT /api/roles/[id]',
        delete: 'DELETE /api/roles/[id]',
      },
      permissions: {
        list: 'GET /api/permissions',
      },
      dashboard: {
        status: 'GET /api/dashboard/status',
      },
      'generator-fuel': {
        list: 'GET /api/generator-fuel/[propertyId]',
        create: 'POST /api/generator-fuel/[propertyId]',
        get: 'GET /api/generator-fuel/logs/[logId]',
        update: 'PUT /api/generator-fuel/logs/[logId]',
        delete: 'DELETE /api/generator-fuel/logs/[logId]',
      },
      'diesel-additions': {
        list: 'GET /api/diesel-additions/[propertyId]',
        create: 'POST /api/diesel-additions/[propertyId]',
      },
      'ott-services': {
        list: 'GET /api/ott-services/[propertyId]',
        create: 'POST /api/ott-services/[propertyId]',
      },
      'uptime-reports': {
        list: 'GET /api/uptime-reports/[propertyId]',
        create: 'POST /api/uptime-reports/[propertyId]',
      },

      'function-processes': {
        list: 'GET /api/function-processes',
        create: 'POST /api/function-processes',
        logs: 'GET /api/function-processes/[id]/logs',
        createLog: 'POST /api/function-processes/[id]/logs',
      },
      thresholds: {
        list: 'GET /api/thresholds',
        create: 'POST /api/thresholds',
        get: 'GET /api/thresholds/[id]',
        update: 'PUT /api/thresholds/[id]',
        delete: 'DELETE /api/thresholds/[id]',
      },
      admin: {
        widgets: {
          list: 'GET /admin/widgets',
          create: 'POST /admin/widgets',
          get: 'GET /admin/widgets/[id]',
          update: 'PUT /admin/widgets/[id]',
          delete: 'DELETE /admin/widgets/[id]',
        },
        'widget-types': 'GET /admin/widget-types',
        screens: {
          list: 'GET /admin/screens',
          create: 'POST /admin/screens',
          get: 'GET /admin/screens/[id]',
          update: 'PUT /admin/screens/[id]',
          delete: 'DELETE /admin/screens/[id]',
          'validate-route': 'GET /admin/screens/validate-route',
        },
      },
    },
    database: {
      provider: 'PostgreSQL',
      orm: 'Prisma',
    },
    tech_stack: [
      'NextJS 14',
      'TypeScript',
      'PostgreSQL',
      'Prisma ORM',
      'JWT Authentication',
      'Joi Validation',
    ],
  };

  return Response.json(
    createApiResponse(apiInfo),
    {
      status: 200,
      headers: corsHeaders(),
    }
  );
}

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: corsHeaders(),
  });
}
