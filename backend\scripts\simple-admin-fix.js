const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function main() {
  try {
    console.log('🔧 Fixing admin permissions...');

    // Find admin user
    const adminUser = await prisma.user.findFirst({
      where: { email: '<EMAIL>' }
    });

    if (!adminUser) {
      console.log('❌ Admin user not found');
      return;
    }

    console.log(`✅ Found admin user: ${adminUser.id}`);

    // Get all permissions
    const allPermissions = await prisma.permission.findMany();
    console.log(`📚 Found ${allPermissions.length} permissions`);

    // Update user with all permission names
    const permissionNames = allPermissions.map(p => p.name);

    await prisma.user.update({
      where: { id: adminUser.id },
      data: {
        permissions: permissionNames
      }
    });

    console.log(`✅ Added ${permissionNames.length} permissions to admin user`);

    // Verify
    const updatedUser = await prisma.user.findFirst({
      where: { id: adminUser.id }
    });

    console.log(`🎉 Admin user now has ${updatedUser.permissions.length} permissions`);

    // Show some key permissions
    const keyPermissions = updatedUser.permissions
      .filter(name => name.includes('view_') || name.includes('dashboard'))
      .slice(0, 10);

    console.log('📊 Key permissions:');
    keyPermissions.forEach(perm => {
      console.log(`  ✅ ${perm}`);
    });

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
