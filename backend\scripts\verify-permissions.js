const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function verifyPermissions() {
  try {
    console.log('🔍 Verifying permission configurations...\n');

    // Get all roles from database
    const roles = await prisma.role.findMany({
      include: {
        rolePermissions: {
          include: {
            permission: true
          }
        }
      }
    });

    console.log('📋 Existing Roles in Database:');
    roles.forEach(role => {
      console.log(`  • ${role.name} - ${role.description}`);
    });
    console.log();

    // Get all screen permissions
    const screenPermissions = await prisma.screenPermission.findMany();
    console.log(`📱 Screen Permissions: ${screenPermissions.length} screens configured`);
    
    // Group by allowed roles
    const screensByRole = {};
    screenPermissions.forEach(screen => {
      screen.allowedRoles.forEach(role => {
        if (!screensByRole[role]) screensByRole[role] = [];
        screensByRole[role].push(screen.screenName);
      });
    });

    console.log('\n📱 Screens accessible by each role:');
    Object.entries(screensByRole).forEach(([role, screens]) => {
      console.log(`  ${role}: ${screens.length} screens`);
      screens.forEach(screen => console.log(`    - ${screen}`));
    });

    // Get all widget permissions
    const widgetPermissions = await prisma.widgetPermission.findMany();
    console.log(`\n🧩 Widget Permissions: ${widgetPermissions.length} widgets configured`);

    // Group widgets by screen
    const widgetsByScreen = {};
    widgetPermissions.forEach(widget => {
      if (!widgetsByScreen[widget.screenName]) widgetsByScreen[widget.screenName] = [];
      widgetsByScreen[widget.screenName].push(widget.widgetName);
    });

    console.log('\n🧩 Widgets per screen:');
    Object.entries(widgetsByScreen).forEach(([screen, widgets]) => {
      console.log(`  ${screen}: ${widgets.length} widgets`);
      widgets.forEach(widget => console.log(`    - ${widget}`));
    });

    // Check for role mismatches
    console.log('\n🔍 Checking for role mismatches...');
    const dbRoleNames = roles.map(r => r.name);
    const usedRoleNames = new Set();
    
    screenPermissions.forEach(screen => {
      screen.allowedRoles.forEach(role => usedRoleNames.add(role));
    });
    
    widgetPermissions.forEach(widget => {
      widget.allowedRoles.forEach(role => usedRoleNames.add(role));
    });

    const missingRoles = Array.from(usedRoleNames).filter(role => !dbRoleNames.includes(role));
    const unusedRoles = dbRoleNames.filter(role => !usedRoleNames.has(role));

    if (missingRoles.length > 0) {
      console.log('❌ Roles used in permissions but not in database:');
      missingRoles.forEach(role => console.log(`  - ${role}`));
    }

    if (unusedRoles.length > 0) {
      console.log('⚠️  Roles in database but not used in permissions:');
      unusedRoles.forEach(role => console.log(`  - ${role}`));
    }

    if (missingRoles.length === 0 && unusedRoles.length === 0) {
      console.log('✅ All roles are properly mapped!');
    }

    // Check permission requirements
    console.log('\n🔑 Checking permission requirements...');
    const allRequiredPermissions = new Set();
    
    screenPermissions.forEach(screen => {
      screen.requiredPermissions.forEach(perm => allRequiredPermissions.add(perm));
    });
    
    widgetPermissions.forEach(widget => {
      widget.requiredPermissions.forEach(perm => allRequiredPermissions.add(perm));
    });

    const dbPermissions = await prisma.permission.findMany();
    const dbPermissionNames = dbPermissions.map(p => p.name);
    
    const missingPermissions = Array.from(allRequiredPermissions).filter(perm => !dbPermissionNames.includes(perm));
    
    if (missingPermissions.length > 0) {
      console.log('❌ Permissions used in configurations but not in database:');
      missingPermissions.forEach(perm => console.log(`  - ${perm}`));
    } else {
      console.log('✅ All required permissions exist in database!');
    }

    // Summary statistics
    console.log('\n📊 Summary Statistics:');
    console.log(`  • Total Roles: ${roles.length}`);
    console.log(`  • Total Screens: ${screenPermissions.length}`);
    console.log(`  • Total Widgets: ${widgetPermissions.length}`);
    console.log(`  • Total Permissions Used: ${allRequiredPermissions.size}`);
    console.log(`  • Total DB Permissions: ${dbPermissions.length}`);

    // Role coverage analysis
    console.log('\n📈 Role Coverage Analysis:');
    dbRoleNames.forEach(roleName => {
      const screenCount = screenPermissions.filter(s => s.allowedRoles.includes(roleName)).length;
      const widgetCount = widgetPermissions.filter(w => w.allowedRoles.includes(roleName)).length;
      console.log(`  ${roleName}: ${screenCount} screens, ${widgetCount} widgets`);
    });

    console.log('\n✅ Permission verification completed!');

  } catch (error) {
    console.error('❌ Error verifying permissions:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the verification
if (require.main === module) {
  verifyPermissions()
    .then(() => {
      console.log('\n🎉 Verification completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Verification failed:', error);
      process.exit(1);
    });
}

module.exports = { verifyPermissions };
