// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

User _$UserFromJson(Map<String, dynamic> json) => User(
      id: json['id'] as String,
      email: json['email'] as String,
      fullName: json['full_name'] as String,
      username: json['username'] as String?,
      mobileNumber: json['mobile_number'] as String?,
      phone: json['phone'] as String?,
      primaryRole: json['primary_role'] as String?,
      roles: const RolesConverter().fromJson(json['roles']),
      permissions: (json['permissions'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      isActive: json['is_active'] as bool,
      isApproved: json['is_approved'] as bool? ?? false,
      approvedBy: json['approved_by'] as String?,
      approvedAt: json['approved_at'] == null
          ? null
          : DateTime.parse(json['approved_at'] as String),
      lastLogin: json['last_login'] == null
          ? null
          : DateTime.parse(json['last_login'] as String),
      createdAt: DateTime.parse(json['created_at'] as String),
      approvalStatus: json['approval_status'] as String?,
    );

Map<String, dynamic> _$UserToJson(User instance) => <String, dynamic>{
      'id': instance.id,
      'email': instance.email,
      'full_name': instance.fullName,
      'username': instance.username,
      'mobile_number': instance.mobileNumber,
      'phone': instance.phone,
      'primary_role': instance.primaryRole,
      'roles': const RolesConverter().toJson(instance.roles),
      'permissions': instance.permissions,
      'is_active': instance.isActive,
      'is_approved': instance.isApproved,
      'approved_by': instance.approvedBy,
      'approved_at': instance.approvedAt?.toIso8601String(),
      'last_login': instance.lastLogin?.toIso8601String(),
      'created_at': instance.createdAt.toIso8601String(),
      'approval_status': instance.approvalStatus,
    };
