// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'properties_api_service.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CreatePropertyRequest _$CreatePropertyRequestFromJson(
        Map<String, dynamic> json) =>
    CreatePropertyRequest(
      name: json['name'] as String,
      type: json['type'] as String,
      parentPropertyId: json['parent_property_id'] as String?,
      address: json['address'] as String?,
      description: json['description'] as String?,
      imageUrl: json['image_url'] as String?,
      location: json['location'] as String?,
      capacity: (json['capacity'] as num?)?.toInt(),
      department: json['department'] as String?,
      projectType: json['project_type'] as String?,
      startDate: json['start_date'] == null
          ? null
          : DateTime.parse(json['start_date'] as String),
      expectedEndDate: json['expected_end_date'] == null
          ? null
          : DateTime.parse(json['expected_end_date'] as String),
      hourlyRateStandard: (json['hourly_rate_standard'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$CreatePropertyRequestToJson(
        CreatePropertyRequest instance) =>
    <String, dynamic>{
      'name': instance.name,
      'type': instance.type,
      'parent_property_id': instance.parentPropertyId,
      'address': instance.address,
      'description': instance.description,
      'image_url': instance.imageUrl,
      'location': instance.location,
      'capacity': instance.capacity,
      'department': instance.department,
      'project_type': instance.projectType,
      'start_date': instance.startDate?.toIso8601String(),
      'expected_end_date': instance.expectedEndDate?.toIso8601String(),
      'hourly_rate_standard': instance.hourlyRateStandard,
    };

UpdatePropertyRequest _$UpdatePropertyRequestFromJson(
        Map<String, dynamic> json) =>
    UpdatePropertyRequest(
      name: json['name'] as String?,
      type: json['type'] as String?,
      address: json['address'] as String?,
      description: json['description'] as String?,
      imageUrl: json['image_url'] as String?,
      isActive: json['is_active'] as bool?,
    );

Map<String, dynamic> _$UpdatePropertyRequestToJson(
        UpdatePropertyRequest instance) =>
    <String, dynamic>{
      'name': instance.name,
      'type': instance.type,
      'address': instance.address,
      'description': instance.description,
      'image_url': instance.imageUrl,
      'is_active': instance.isActive,
    };

CreatePropertyServiceRequest _$CreatePropertyServiceRequestFromJson(
        Map<String, dynamic> json) =>
    CreatePropertyServiceRequest(
      serviceType: json['service_type'] as String,
      status: json['status'] as String,
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$CreatePropertyServiceRequestToJson(
        CreatePropertyServiceRequest instance) =>
    <String, dynamic>{
      'service_type': instance.serviceType,
      'status': instance.status,
      'notes': instance.notes,
    };

UpdatePropertyServiceRequest _$UpdatePropertyServiceRequestFromJson(
        Map<String, dynamic> json) =>
    UpdatePropertyServiceRequest(
      status: json['status'] as String?,
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$UpdatePropertyServiceRequestToJson(
        UpdatePropertyServiceRequest instance) =>
    <String, dynamic>{
      'status': instance.status,
      'notes': instance.notes,
    };

// **************************************************************************
// RetrofitGenerator
// **************************************************************************

// ignore_for_file: unnecessary_brace_in_string_interps,no_leading_underscores_for_local_identifiers,unused_element,unnecessary_string_interpolations

class _PropertiesApiService implements PropertiesApiService {
  _PropertiesApiService(
    this._dio, {
    this.baseUrl,
    this.errorLogger,
  });

  final Dio _dio;

  String? baseUrl;

  final ParseErrorLogger? errorLogger;

  @override
  Future<ApiResponse<List<Property>>> getProperties() async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<ApiResponse<List<Property>>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/api/properties',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<List<Property>> _value;
    try {
      _value = ApiResponse<List<Property>>.fromJson(
        _result.data!,
        (json) => json is List<dynamic>
            ? json
                .map<Property>(
                    (i) => Property.fromJson(i as Map<String, dynamic>))
                .toList()
            : List.empty(),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<Property>> getPropertyById(String id) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<ApiResponse<Property>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/api/properties/${id}',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<Property> _value;
    try {
      _value = ApiResponse<Property>.fromJson(
        _result.data!,
        (json) => Property.fromJson(json as Map<String, dynamic>),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<HttpResponse<dynamic>> createProperty(
      CreatePropertyRequest request) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(request.toJson());
    final _options = _setStreamType<HttpResponse<dynamic>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/api/properties',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch(_options);
    final _value = _result.data;
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<ApiResponse<Property>> updateProperty(
    String id,
    UpdatePropertyRequest request,
  ) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(request.toJson());
    final _options = _setStreamType<ApiResponse<Property>>(Options(
      method: 'PUT',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/api/properties/${id}',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<Property> _value;
    try {
      _value = ApiResponse<Property>.fromJson(
        _result.data!,
        (json) => Property.fromJson(json as Map<String, dynamic>),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<VoidResponse>> deleteProperty(String id) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<ApiResponse<VoidResponse>>(Options(
      method: 'DELETE',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/api/properties/${id}',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<VoidResponse> _value;
    try {
      _value = ApiResponse<VoidResponse>.fromJson(
        _result.data!,
        (json) => VoidResponse.fromJson(json as Map<String, dynamic>),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<List<PropertyService>>> getPropertyServices(
      String id) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<ApiResponse<List<PropertyService>>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/api/properties/${id}/services',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<List<PropertyService>> _value;
    try {
      _value = ApiResponse<List<PropertyService>>.fromJson(
        _result.data!,
        (json) => json is List<dynamic>
            ? json
                .map<PropertyService>(
                    (i) => PropertyService.fromJson(i as Map<String, dynamic>))
                .toList()
            : List.empty(),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<PropertyService>> addPropertyService(
    String id,
    CreatePropertyServiceRequest request,
  ) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(request.toJson());
    final _options = _setStreamType<ApiResponse<PropertyService>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/api/properties/${id}/services',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<PropertyService> _value;
    try {
      _value = ApiResponse<PropertyService>.fromJson(
        _result.data!,
        (json) => PropertyService.fromJson(json as Map<String, dynamic>),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<PropertyService>> updatePropertyService(
    String propertyId,
    String serviceId,
    UpdatePropertyServiceRequest request,
  ) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(request.toJson());
    final _options = _setStreamType<ApiResponse<PropertyService>>(Options(
      method: 'PUT',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/api/properties/${propertyId}/services/${serviceId}',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<PropertyService> _value;
    try {
      _value = ApiResponse<PropertyService>.fromJson(
        _result.data!,
        (json) => PropertyService.fromJson(json as Map<String, dynamic>),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<VoidResponse>> deletePropertyService(
    String propertyId,
    String serviceId,
  ) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<ApiResponse<VoidResponse>>(Options(
      method: 'DELETE',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/api/properties/${propertyId}/services/${serviceId}',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<VoidResponse> _value;
    try {
      _value = ApiResponse<VoidResponse>.fromJson(
        _result.data!,
        (json) => VoidResponse.fromJson(json as Map<String, dynamic>),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  RequestOptions _setStreamType<T>(RequestOptions requestOptions) {
    if (T != dynamic &&
        !(requestOptions.responseType == ResponseType.bytes ||
            requestOptions.responseType == ResponseType.stream)) {
      if (T == String) {
        requestOptions.responseType = ResponseType.plain;
      } else {
        requestOptions.responseType = ResponseType.json;
      }
    }
    return requestOptions;
  }

  String _combineBaseUrls(
    String dioBaseUrl,
    String? baseUrl,
  ) {
    if (baseUrl == null || baseUrl.trim().isEmpty) {
      return dioBaseUrl;
    }

    final url = Uri.parse(baseUrl);

    if (url.isAbsolute) {
      return url.toString();
    }

    return Uri.parse(dioBaseUrl).resolveUri(url).toString();
  }
}
