import { NextRequest } from 'next/server';
import { requireAuth } from '@/lib/auth';
import { createApiResponse, getQueryParams, handleError, corsHeaders } from '@/lib/utils';
import { NotificationService } from '@/lib/monitoring/notification-service';

const notificationService = new NotificationService();

async function getNotificationsHandler(request: NextRequest, context: any, currentUser: any) {
  try {
    const queryParams = getQueryParams(request);
    const unreadOnly = queryParams.unread_only === 'true';

    if (unreadOnly) {
      // Get unread notifications
      const notifications = await notificationService.getUnreadNotifications(currentUser.id);
      
      return Response.json(
        createApiResponse({
          notifications,
          count: notifications.length,
          unread_count: notifications.length,
        }),
        {
          status: 200,
          headers: corsHeaders(),
        }
      );
    } else {
      // Get all notifications (implement pagination if needed)
      const notifications = await notificationService.getUnreadNotifications(currentUser.id);
      
      return Response.json(
        createApiResponse({
          notifications,
          count: notifications.length,
        }),
        {
          status: 200,
          headers: corsHeaders(),
        }
      );
    }
  } catch (error) {
    return handleError(error, 'Failed to get notifications');
  }
}

// GET /api/notifications - Get user notifications
export const GET = requireAuth(getNotificationsHandler);

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: corsHeaders(),
  });
}
