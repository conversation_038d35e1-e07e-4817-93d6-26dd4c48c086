import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:reactive_forms/reactive_forms.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/utils/app_utils.dart';
import '../providers/screen_management_providers.dart';
import '../providers/role_management_providers.dart';
import '../../data/models/custom_screen.dart';

class CreateScreenDialog extends ConsumerStatefulWidget {
  final CustomScreen? screen;

  const CreateScreenDialog({super.key, this.screen});

  @override
  ConsumerState<CreateScreenDialog> createState() => _CreateScreenDialogState();
}

class _CreateScreenDialogState extends ConsumerState<CreateScreenDialog> {
  late FormGroup form;
  bool isLoading = false;
  List<String> selectedPermissions = [];
  List<String> selectedRoles = [];

  @override
  void initState() {
    super.initState();
    _initializeForm();
  }

  void _initializeForm() {
    final screen = widget.screen;

    form = FormGroup({
      'name': FormControl<String>(
        value: screen?.name ?? '',
        validators: [
          Validators.required,
          Validators.minLength(3),
          Validators.maxLength(50),
          Validators.pattern(r'^[a-zA-Z0-9_-]+$'),
        ],
      ),
      'title': FormControl<String>(
        value: screen?.title ?? '',
        validators: [
          Validators.required,
          Validators.minLength(3),
          Validators.maxLength(100),
        ],
      ),
      'route': FormControl<String>(
        value: screen?.route ?? '',
        validators: [
          Validators.required,
          Validators.pattern(r'^\/[a-zA-Z0-9\/_-]*$'),
        ],
      ),
      'description': FormControl<String>(
        value: screen?.description ?? '',
        validators: [
          Validators.maxLength(500),
        ],
      ),
      'icon': FormControl<String>(
        value: screen?.icon ?? 'web',
      ),
      'isActive': FormControl<bool>(
        value: screen?.isActive ?? true,
      ),
    });

    if (screen != null) {
      selectedPermissions = List.from(screen.requiredPermissions);
      selectedRoles = List.from(screen.allowedRoles);
    }
  }

  @override
  Widget build(BuildContext context) {
    final rolesAsync = ref.watch(rolesProvider);
    final permissionsAsync = ref.watch(permissionsProvider);

    return AlertDialog(
      title: Row(
        children: [
          Icon(
            widget.screen == null ? Icons.add : Icons.edit,
            color: Theme.of(context).primaryColor,
          ),
          const SizedBox(width: 8),
          Text(widget.screen == null ? 'Create Screen' : 'Edit Screen'),
          const Spacer(),
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ],
      ),
      content: SizedBox(
        width: MediaQuery.of(context).size.width * 0.8,
        height: MediaQuery.of(context).size.height * 0.8,
        child: ReactiveForm(
          formGroup: form,
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Basic Information Section
                _buildSectionHeader('Basic Information'),
                const SizedBox(height: AppConstants.smallPadding),

                Row(
                  children: [
                    Expanded(
                      child: ReactiveTextField<String>(
                        formControlName: 'name',
                        decoration: const InputDecoration(
                          labelText: 'Screen Name *',
                          hintText: 'e.g., custom_dashboard',
                          prefixIcon: Icon(Icons.label),
                          border: OutlineInputBorder(),
                        ),
                        validationMessages: {
                          ValidationMessage.required: (_) => 'Screen name is required',
                          ValidationMessage.minLength: (_) => 'Name must be at least 3 characters',
                          ValidationMessage.maxLength: (_) => 'Name cannot exceed 50 characters',
                          ValidationMessage.pattern: (_) => 'Only letters, numbers, hyphens and underscores allowed',
                        },
                      ),
                    ),
                    const SizedBox(width: AppConstants.defaultPadding),
                    Expanded(
                      child: ReactiveTextField<String>(
                        formControlName: 'title',
                        decoration: const InputDecoration(
                          labelText: 'Display Title *',
                          hintText: 'e.g., Custom Dashboard',
                          prefixIcon: Icon(Icons.title),
                          border: OutlineInputBorder(),
                        ),
                        validationMessages: {
                          ValidationMessage.required: (_) => 'Title is required',
                          ValidationMessage.minLength: (_) => 'Title must be at least 3 characters',
                          ValidationMessage.maxLength: (_) => 'Title cannot exceed 100 characters',
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: AppConstants.defaultPadding),

                Row(
                  children: [
                    Expanded(
                      child: ReactiveTextField<String>(
                        formControlName: 'route',
                        decoration: InputDecoration(
                          labelText: 'Route Path *',
                          hintText: '/custom/dashboard',
                          prefixIcon: const Icon(Icons.route),
                          border: const OutlineInputBorder(),
                          suffixIcon: IconButton(
                            icon: const Icon(Icons.check_circle),
                            onPressed: _validateRoute,
                            tooltip: 'Validate route',
                          ),
                        ),
                        validationMessages: {
                          ValidationMessage.required: (_) => 'Route is required',
                          ValidationMessage.pattern: (_) => 'Route must start with / and contain only valid characters',
                        },
                      ),
                    ),
                    const SizedBox(width: AppConstants.defaultPadding),
                    Expanded(
                      child: ReactiveDropdownField<String>(
                        formControlName: 'icon',
                        decoration: const InputDecoration(
                          labelText: 'Icon',
                          prefixIcon: const Icon(Icons.star),
                          border: OutlineInputBorder(),
                        ),
                        items: _getIconOptions(),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: AppConstants.defaultPadding),

                ReactiveTextField<String>(
                  formControlName: 'description',
                  maxLines: 3,
                  decoration: const InputDecoration(
                    labelText: 'Description',
                    hintText: 'Brief description of the screen purpose...',
                    prefixIcon: Icon(Icons.description),
                    border: OutlineInputBorder(),
                  ),
                  validationMessages: {
                    ValidationMessage.maxLength: (_) => 'Description cannot exceed 500 characters',
                  },
                ),
                const SizedBox(height: AppConstants.defaultPadding),

                ReactiveCheckboxListTile(
                  formControlName: 'isActive',
                  title: const Text('Active'),
                  subtitle: const Text('Screen is active and accessible to users'),
                ),

                const SizedBox(height: AppConstants.largePadding),

                // Permissions Section
                _buildSectionHeader('Required Permissions'),
                const SizedBox(height: AppConstants.smallPadding),
                _buildPermissionsSection(permissionsAsync.when(
                  data: (permissionResponse) => AsyncValue.data(permissionResponse.permissions.map((p) => p.toJson()).toList()),
                  loading: () => const AsyncValue.loading(),
                  error: (error, stack) => AsyncValue.error(error, stack),
                )),

                const SizedBox(height: AppConstants.largePadding),

                // Roles Section
                _buildSectionHeader('Allowed Roles'),
                const SizedBox(height: AppConstants.smallPadding),
                _buildRolesSection(rolesAsync),
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: isLoading ? null : _handleSubmit,
          child: isLoading
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : Text(widget.screen == null ? 'Create' : 'Update'),
        ),
      ],
    );
  }

  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: Theme.of(context).textTheme.titleMedium?.copyWith(
        fontWeight: FontWeight.bold,
        color: Theme.of(context).primaryColor,
      ),
    );
  }

  Widget _buildPermissionsSection(AsyncValue<List<dynamic>> permissionsAsync) {
    return permissionsAsync.when(
      data: (permissions) {
        return Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey[300]!),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            children: [
              Container(
                padding: const EdgeInsets.all(AppConstants.smallPadding),
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(8),
                    topRight: Radius.circular(8),
                  ),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.security, size: 16),
                    const SizedBox(width: 8),
                    Text(
                      'Select required permissions for this screen',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ],
                ),
              ),
              Container(
                constraints: const BoxConstraints(maxHeight: 200),
                child: ListView.builder(
                  shrinkWrap: true,
                  itemCount: permissions.length,
                  itemBuilder: (context, index) {
                    final permission = permissions[index];
                    final permissionName = permission['name'] ?? permission.toString();
                    final isSelected = selectedPermissions.contains(permissionName);

                    return CheckboxListTile(
                      dense: true,
                      title: Text(permissionName),
                      subtitle: permission is Map ? Text(permission['description'] ?? '') : null,
                      value: isSelected,
                      onChanged: (value) {
                        setState(() {
                          if (value == true) {
                            selectedPermissions.add(permissionName);
                          } else {
                            selectedPermissions.remove(permissionName);
                          }
                        });
                      },
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Text('Error loading permissions: $error'),
    );
  }

  Widget _buildRolesSection(AsyncValue<List<dynamic>> rolesAsync) {
    return rolesAsync.when(
      data: (roles) {
        return Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey[300]!),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            children: [
              Container(
                padding: const EdgeInsets.all(AppConstants.smallPadding),
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(8),
                    topRight: Radius.circular(8),
                  ),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.group, size: 16),
                    const SizedBox(width: 8),
                    Text(
                      'Select roles that can access this screen',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ],
                ),
              ),
              Container(
                constraints: const BoxConstraints(maxHeight: 150),
                child: ListView.builder(
                  shrinkWrap: true,
                  itemCount: roles.length,
                  itemBuilder: (context, index) {
                    final role = roles[index];
                    final roleName = role['name'] ?? role.toString();
                    final isSelected = selectedRoles.contains(roleName);

                    return CheckboxListTile(
                      dense: true,
                      title: Text(roleName),
                      subtitle: role is Map ? Text(role['description'] ?? '') : null,
                      value: isSelected,
                      onChanged: (value) {
                        setState(() {
                          if (value == true) {
                            selectedRoles.add(roleName);
                          } else {
                            selectedRoles.remove(roleName);
                          }
                        });
                      },
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Text('Error loading roles: $error'),
    );
  }

  List<DropdownMenuItem<String>> _getIconOptions() {
    final icons = [
      {'value': 'web', 'label': 'Web'},
      {'value': 'dashboard', 'label': 'Dashboard'},
      {'value': 'analytics', 'label': 'Analytics'},
      {'value': 'settings', 'label': 'Settings'},
      {'value': 'people', 'label': 'People'},
      {'value': 'business', 'label': 'Business'},
      {'value': 'report', 'label': 'Report'},
      {'value': 'chart', 'label': 'Chart'},
      {'value': 'table', 'label': 'Table'},
      {'value': 'form', 'label': 'Form'},
    ];

    return icons.map((icon) => DropdownMenuItem<String>(
      value: icon['value'],
      child: Row(
        children: [
          Icon(_getIconData(icon['value']!), size: 16),
          const SizedBox(width: 8),
          Text(icon['label']!),
        ],
      ),
    )).toList();
  }

  IconData _getIconData(String iconName) {
    switch (iconName) {
      case 'dashboard':
        return Icons.dashboard;
      case 'analytics':
        return Icons.analytics;
      case 'settings':
        return Icons.settings;
      case 'people':
        return Icons.people;
      case 'business':
        return Icons.business;
      case 'report':
        return Icons.assessment;
      case 'chart':
        return Icons.bar_chart;
      case 'table':
        return Icons.table_chart;
      case 'form':
        return Icons.check_box;
      default:
        return Icons.web;
    }
  }

  Future<void> _validateRoute() async {
    final route = form.control('route').value as String?;
    if (route == null || route.isEmpty) return;

    final isValid = await ref.read(screenManagementProvider.notifier).validateRoute(route);

    if (mounted) {
      if (isValid) {
        AppUtils.showSuccessSnackBar(context, 'Route is valid and available');
      } else {
        AppUtils.showErrorSnackBar(context, 'Route is invalid or already in use');
      }
    }
  }

  Future<void> _handleSubmit() async {
    if (form.invalid) {
      form.markAllAsTouched();
      return;
    }

    setState(() {
      isLoading = true;
    });

    try {
      final formValue = form.value;

      final screenData = {
        'name': formValue['name'],
        'title': formValue['title'],
        'route': formValue['route'],
        'description': formValue['description'],
        'icon': formValue['icon'],
        'requiredPermissions': selectedPermissions,
        'allowedRoles': selectedRoles,
        'isActive': formValue['isActive'],
        'widgets': widget.screen?.widgets.map((w) => w.toJson()).toList() ?? [],
        'layout': widget.screen?.layout,
      };

      bool success;
      if (widget.screen == null) {
        success = await ref.read(screenManagementProvider.notifier).createScreen(screenData);
      } else {
        success = await ref.read(screenManagementProvider.notifier).updateScreen(widget.screen!.id, screenData);
      }

      if (success && mounted) {
        Navigator.of(context).pop();
        AppUtils.showSuccessSnackBar(
          context,
          widget.screen == null ? 'Screen created successfully' : 'Screen updated successfully',
        );
      }
    } catch (e) {
      if (mounted) {
        AppUtils.showErrorSnackBar(context, 'Failed to save screen: $e');
      }
    } finally {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    }
  }
}
