import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';
import { createApiResponse, getQueryParams, getRequestBody, handleError, corsHeaders } from '@/lib/utils';
import { validateRequest } from '@/lib/validation';
import Joi from 'joi';

const createAttendanceSchema = Joi.object({
  user_id: Joi.string().uuid().required(),
  date: Joi.date().required(),
  check_in_time: Joi.string().optional(),
  check_out_time: Joi.string().optional(),
  hours_worked: Joi.number().min(0).max(24).optional(),
  notes: Joi.string().optional(),
});

async function getPropertyAttendanceHandler(
  request: NextRequest,
  context: { params: { id: string } },
  currentUser: any
) {
  try {
    const { id } = context.params;
    const params = getQueryParams(request);
    const { date, start_date, end_date, user_id } = params;

    // Verify property exists
    const property = await prisma.property.findUnique({
      where: { id },
      select: { id: true, name: true, type: true },
    });

    if (!property) {
      return Response.json(
        createApiResponse(null, 'Property not found', 'NOT_FOUND'),
        { status: 404 }
      );
    }

    // Build where clause for attendance
    const where: any = {
      propertyId: id,
    };

    if (date) {
      where.date = new Date(date);
    } else if (start_date && end_date) {
      where.date = {
        gte: new Date(start_date),
        lte: new Date(end_date),
      };
    }

    if (user_id) {
      where.userId = user_id;
    }

    // Get attendance records
    const attendanceRecords = await prisma.propertyAttendance.findMany({
      where,
      include: {
        user: {
          select: {
            id: true,
            email: true,
            fullName: true,
          },
        },
        recorder: {
          select: {
            id: true,
            fullName: true,
          },
        },
      },
      orderBy: [
        { date: 'desc' },
        { checkInTime: 'asc' },
      ],
    });

    // Transform data to match API response format
    const transformedRecords = attendanceRecords.map(record => ({
      id: record.id,
      property_id: record.propertyId,
      user_id: record.userId,
      date: record.date,
      check_in_time: record.checkInTime,
      check_out_time: record.checkOutTime,
      hours_worked: record.hoursWorked,
      notes: record.notes,
      recorded_by: record.recordedBy,
      created_at: record.createdAt,
      updated_at: record.updatedAt,
      user: {
        id: record.user.id,
        email: record.user.email,
        full_name: record.user.fullName,
      },
      recorder: record.recorder ? {
        id: record.recorder.id,
        full_name: record.recorder.fullName,
      } : null,
    }));

    return Response.json(
      createApiResponse({
        attendance: transformedRecords,
        property: {
          id: property.id,
          name: property.name,
          type: property.type.toLowerCase(),
        },
      }),
      { 
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to fetch property attendance');
  }
}

async function createPropertyAttendanceHandler(
  request: NextRequest,
  context: { params: { id: string } },
  currentUser: any
) {
  try {
    const { id } = context.params;
    const body = await getRequestBody(request);
    
    // Validate request body
    const validation = validateRequest(createAttendanceSchema, body);
    if (!validation.isValid) {
      return Response.json(
        createApiResponse(null, 'Validation failed', 'VALIDATION_ERROR'),
        { status: 400 }
      );
    }

    // Verify property exists
    const property = await prisma.property.findUnique({
      where: { id },
    });

    if (!property) {
      return Response.json(
        createApiResponse(null, 'Property not found', 'NOT_FOUND'),
        { status: 404 }
      );
    }

    // Verify user is a member of this property
    const member = await prisma.propertyMember.findUnique({
      where: {
        propertyId_userId: {
          propertyId: id,
          userId: validation.data.user_id,
        },
      },
    });

    if (!member) {
      return Response.json(
        createApiResponse(null, 'User is not a member of this property', 'NOT_MEMBER'),
        { status: 400 }
      );
    }

    const { 
      user_id, 
      date, 
      check_in_time, 
      check_out_time, 
      hours_worked, 
      notes 
    } = validation.data;

    // Check if attendance already exists for this user and date
    const existingAttendance = await prisma.propertyAttendance.findUnique({
      where: {
        propertyId_userId_date: {
          propertyId: id,
          userId: user_id,
          date: new Date(date),
        },
      },
    });

    if (existingAttendance) {
      return Response.json(
        createApiResponse(null, 'Attendance already recorded for this user and date', 'DUPLICATE_ATTENDANCE'),
        { status: 409 }
      );
    }

    // Create attendance record
    const attendance = await prisma.propertyAttendance.create({
      data: {
        propertyId: id,
        userId: user_id,
        date: new Date(date),
        checkInTime: check_in_time,
        checkOutTime: check_out_time,
        hoursWorked: hours_worked,
        notes,
        recordedBy: currentUser.id,
      },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            fullName: true,
          },
        },
      },
    });

    return Response.json(
      createApiResponse({
        message: 'Attendance recorded successfully',
        attendance: {
          id: attendance.id,
          property_id: attendance.propertyId,
          user_id: attendance.userId,
          date: attendance.date,
          check_in_time: attendance.checkInTime,
          check_out_time: attendance.checkOutTime,
          hours_worked: attendance.hoursWorked,
          notes: attendance.notes,
          recorded_by: attendance.recordedBy,
          created_at: attendance.createdAt,
          updated_at: attendance.updatedAt,
          user: {
            id: attendance.user.id,
            email: attendance.user.email,
            full_name: attendance.user.fullName,
          },
        },
      }),
      { 
        status: 201,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to create attendance record');
  }
}

export const GET = requireAuth(getPropertyAttendanceHandler);
export const POST = requireAuth(createPropertyAttendanceHandler);

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: corsHeaders(),
  });
}
