import '../data/threshold_api_service.dart';

abstract class ThresholdRepository {
  Future<List<ThresholdConfig>> getThresholds({
    String? functionalArea,
    String? propertyType,
    String? metricName,
  });

  Future<ThresholdConfig> getThresholdById(String id);

  Future<ThresholdConfig> createThreshold(CreateThresholdRequest request);

  Future<ThresholdConfig> updateThreshold(String id, UpdateThresholdRequest request);

  Future<void> deleteThreshold(String id);

  Future<List<ThresholdConfig>> bulkCreateThresholds(BulkCreateThresholdsRequest request);

  Future<List<ThresholdConfig>> bulkUpdateThresholds(BulkUpdateThresholdsRequest request);

  // Additional methods needed by providers
  Future<void> updateThresholds(List<ThresholdConfig> thresholds);
  Future<void> resetThresholds();
}
