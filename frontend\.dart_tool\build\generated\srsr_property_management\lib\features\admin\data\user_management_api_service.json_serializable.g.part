// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CreateUserRequest _$CreateUserRequestFromJson(Map<String, dynamic> json) =>
    CreateUserRequest(
      email: json['email'] as String,
      fullName: json['full_name'] as String,
      username: json['username'] as String?,
      mobileNumber: json['mobile_number'] as String?,
      phone: json['phone'] as String?,
      password: json['password'] as String,
      primaryRole: json['primary_role'] as String?,
      roles:
          (json['roles'] as List<dynamic>?)?.map((e) => e as String).toList(),
      isActive: json['is_active'] as bool?,
    );

Map<String, dynamic> _$CreateUserRequestToJson(CreateUserRequest instance) =>
    <String, dynamic>{
      'email': instance.email,
      'full_name': instance.fullName,
      'username': instance.username,
      'mobile_number': instance.mobileNumber,
      'phone': instance.phone,
      'password': instance.password,
      'primary_role': instance.primaryRole,
      'roles': instance.roles,
      'is_active': instance.isActive,
    };

UpdateUserRequest _$UpdateUserRequestFromJson(Map<String, dynamic> json) =>
    UpdateUserRequest(
      email: json['email'] as String?,
      fullName: json['full_name'] as String?,
      username: json['username'] as String?,
      mobileNumber: json['mobile_number'] as String?,
      phone: json['phone'] as String?,
      primaryRole: json['primary_role'] as String?,
      roles:
          (json['roles'] as List<dynamic>?)?.map((e) => e as String).toList(),
      isActive: json['is_active'] as bool?,
    );

Map<String, dynamic> _$UpdateUserRequestToJson(UpdateUserRequest instance) =>
    <String, dynamic>{
      'email': instance.email,
      'full_name': instance.fullName,
      'username': instance.username,
      'mobile_number': instance.mobileNumber,
      'phone': instance.phone,
      'primary_role': instance.primaryRole,
      'roles': instance.roles,
      'is_active': instance.isActive,
    };

AssignRolesRequest _$AssignRolesRequestFromJson(Map<String, dynamic> json) =>
    AssignRolesRequest(
      roles: (json['roles'] as List<dynamic>).map((e) => e as String).toList(),
    );

Map<String, dynamic> _$AssignRolesRequestToJson(AssignRolesRequest instance) =>
    <String, dynamic>{
      'roles': instance.roles,
    };

BulkApproveRequest _$BulkApproveRequestFromJson(Map<String, dynamic> json) =>
    BulkApproveRequest(
      userIds:
          (json['user_ids'] as List<dynamic>).map((e) => e as String).toList(),
    );

Map<String, dynamic> _$BulkApproveRequestToJson(BulkApproveRequest instance) =>
    <String, dynamic>{
      'user_ids': instance.userIds,
    };

VoidResponse _$VoidResponseFromJson(Map<String, dynamic> json) => VoidResponse(
      message: json['message'] as String,
    );

Map<String, dynamic> _$VoidResponseToJson(VoidResponse instance) =>
    <String, dynamic>{
      'message': instance.message,
    };
