import 'package:flutter/material.dart';
import '../../../../shared/models/generator_fuel.dart';

class FuelInsightsCard extends StatelessWidget {
  final List<GeneratorFuelLog> fuelLogs;

  const FuelInsightsCard({
    super.key,
    required this.fuelLogs,
  });

  @override
  Widget build(BuildContext context) {
    final insights = _calculateInsights();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Fuel Insights',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            _buildInsightRow(
              'Average Consumption',
              '${insights.averageConsumption.toStringAsFixed(1)} L/day',
              Icons.local_gas_station,
              Colors.blue,
            ),
            const SizedBox(height: 12),
            _buildInsightRow(
              'Efficiency',
              '${insights.efficiency.toStringAsFixed(2)} L/hour',
              Icons.speed,
              Colors.green,
            ),
            const SizedBox(height: 12),
            _buildInsightRow(
              'Days Until Empty',
              '${insights.daysUntilEmpty.toStringAsFixed(0)} days',
              Icons.warning,
              insights.daysUntilEmpty < 7 ? Colors.red : Colors.orange,
            ),
            const SizedBox(height: 12),
            _buildInsightRow(
              'Total Runtime',
              '${insights.totalRuntime.toStringAsFixed(1)} hours',
              Icons.timer,
              Colors.purple,
            ),
            if (insights.recommendations.isNotEmpty) ...[
              const SizedBox(height: 16),
              Text(
                'Recommendations',
                style: Theme.of(context).textTheme.titleSmall,
              ),
              const SizedBox(height: 8),
              ...insights.recommendations.map((recommendation) =>
                _buildRecommendation(recommendation)),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildInsightRow(String label, String value, IconData icon, Color color) {
    return Row(
      children: [
        Icon(icon, color: color, size: 20),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: const TextStyle(
                  fontSize: 12,
                  color: Colors.grey,
                ),
              ),
              Text(
                value,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildRecommendation(String recommendation) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Icon(
            Icons.lightbulb_outline,
            size: 16,
            color: Colors.amber,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              recommendation,
              style: const TextStyle(fontSize: 12),
            ),
          ),
        ],
      ),
    );
  }

  FuelInsights _calculateInsights() {
    if (fuelLogs.isEmpty) {
      return FuelInsights(
        averageConsumption: 0,
        efficiency: 0,
        daysUntilEmpty: 0,
        totalRuntime: 0,
        recommendations: [],
      );
    }

    // Sort logs by timestamp
    final sortedLogs = List<GeneratorFuelLog>.from(fuelLogs)
      ..sort((a, b) => a.recordedAt.compareTo(b.recordedAt));

    // Calculate average consumption per day
    final totalConsumption = sortedLogs.fold<double>(
      0, (sum, log) => sum + (log.consumptionRate ?? 0));
    final daySpan = sortedLogs.last.recordedAt
        .difference(sortedLogs.first.recordedAt).inDays + 1;
    final averageConsumption = daySpan > 0 ? totalConsumption / daySpan : 0;

    // Calculate efficiency (liters per hour)
    final totalRuntime = sortedLogs.fold<double>(
      0, (sum, log) => sum + (log.runtimeHours ?? 0));
    final efficiency = totalRuntime > 0 ? totalConsumption / totalRuntime : 0;

    // Estimate days until empty based on current level and consumption rate
    final currentLevel = sortedLogs.isNotEmpty ? sortedLogs.last.fuelLevelLiters : 0;
    final daysUntilEmpty = averageConsumption > 0 ? currentLevel / averageConsumption : 0;

    // Generate recommendations
    final recommendations = <String>[];

    if (daysUntilEmpty < 7) {
      recommendations.add('Fuel level is low. Consider refueling soon.');
    }

    if (efficiency > 5) {
      recommendations.add('High fuel consumption detected. Check generator efficiency.');
    }

    if (sortedLogs.length > 7) {
      final recentLogs = sortedLogs.take(7).toList();
      final recentConsumption = recentLogs.fold<double>(
        0, (sum, log) => sum + (log.consumptionRate ?? 0)) / 7;

      if (recentConsumption > averageConsumption * 1.2) {
        recommendations.add('Recent consumption is higher than average.');
      }
    }

    return FuelInsights(
      averageConsumption: averageConsumption.toDouble(),
      efficiency: efficiency.toDouble(),
      daysUntilEmpty: daysUntilEmpty.toDouble(),
      totalRuntime: totalRuntime.toDouble(),
      recommendations: recommendations,
    );
  }
}

class FuelInsights {
  final double averageConsumption;
  final double efficiency;
  final double daysUntilEmpty;
  final double totalRuntime;
  final List<String> recommendations;

  FuelInsights({
    required this.averageConsumption,
    required this.efficiency,
    required this.daysUntilEmpty,
    required this.totalRuntime,
    required this.recommendations,
  });
}
