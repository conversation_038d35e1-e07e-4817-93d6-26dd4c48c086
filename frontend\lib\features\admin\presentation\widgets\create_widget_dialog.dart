import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:reactive_forms/reactive_forms.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/utils/app_utils.dart';
import '../providers/screen_management_providers.dart';
import '../providers/role_management_providers.dart';
import '../../data/models/custom_screen.dart';

class CreateWidgetDialog extends ConsumerStatefulWidget {
  final CustomWidget? widget;
  final String? preselectedType;

  const CreateWidgetDialog({super.key, this.widget, this.preselectedType});

  @override
  ConsumerState<CreateWidgetDialog> createState() => _CreateWidgetDialogState();
}

class _CreateWidgetDialogState extends ConsumerState<CreateWidgetDialog> {
  late FormGroup form;
  bool isLoading = false;
  List<String> selectedPermissions = [];
  List<String> selectedRoles = [];
  Map<String, dynamic> widgetProperties = {};
  String selectedType = 'statCard';

  @override
  void initState() {
    super.initState();
    _initializeForm();
  }

  void _initializeForm() {
    final widgetData = widget.widget;
    selectedType = widgetData?.type ?? widget.preselectedType ?? 'statCard';

    form = FormGroup({
      'name': FormControl<String>(
        value: widgetData?.name ?? '',
        validators: [
          Validators.required,
          Validators.minLength(3),
          Validators.maxLength(50),
          Validators.pattern(r'^[a-zA-Z0-9_-]+$'),
        ],
      ),
      'title': FormControl<String>(
        value: widgetData?.title ?? '',
        validators: [
          Validators.required,
          Validators.minLength(3),
          Validators.maxLength(100),
        ],
      ),
      'type': FormControl<String>(
        value: selectedType,
        validators: [Validators.required],
      ),
      'order': FormControl<int>(
        value: widgetData?.order ?? 0,
        validators: [
          Validators.required,
          Validators.min(0),
          Validators.max(999),
        ],
      ),
      'isVisible': FormControl<bool>(
        value: widgetData?.isVisible ?? true,
      ),
    });

    if (widgetData != null) {
      selectedPermissions = List.from(widgetData.requiredPermissions);
      selectedRoles = List.from(widgetData.allowedRoles);
      widgetProperties = Map.from(widgetData.properties);
    } else {
      _setDefaultProperties();
    }
  }

  void _setDefaultProperties() {
    switch (selectedType) {
      case 'statCard':
        widgetProperties = {
          'title': 'Stat Title',
          'value': '0',
          'icon': 'analytics',
          'color': 'blue',
        };
        break;
      case 'chart':
        widgetProperties = {
          'chartType': 'bar',
          'dataSource': '',
          'xAxis': '',
          'yAxis': '',
        };
        break;
      case 'table':
        widgetProperties = {
          'dataSource': '',
          'columns': [],
          'sortable': true,
          'filterable': true,
        };
        break;
      case 'form':
        widgetProperties = {
          'fields': [],
          'submitAction': '',
          'validation': true,
        };
        break;
      case 'button':
        widgetProperties = {
          'text': 'Button',
          'action': '',
          'style': 'elevated',
          'color': 'primary',
        };
        break;
      case 'text':
        widgetProperties = {
          'content': 'Text content',
          'style': 'body',
          'alignment': 'left',
        };
        break;
      default:
        widgetProperties = {};
    }
  }

  @override
  Widget build(BuildContext context) {
    final rolesAsync = ref.watch(rolesProvider);
    final permissionsAsync = ref.watch(permissionsProvider);

    return AlertDialog(
      title: Row(
        children: [
          Icon(
            widget.widget == null ? Icons.add : Icons.edit,
            color: Theme.of(context).primaryColor,
          ),
          const SizedBox(width: 8),
          Text(widget.widget == null ? 'Create Widget' : 'Edit Widget'),
          const Spacer(),
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ],
      ),
      content: SizedBox(
        width: MediaQuery.of(context).size.width * 0.8,
        height: MediaQuery.of(context).size.height * 0.8,
        child: ReactiveForm(
          formGroup: form,
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Basic Information Section
                _buildSectionHeader('Basic Information'),
                const SizedBox(height: AppConstants.smallPadding),

                Row(
                  children: [
                    Expanded(
                      child: ReactiveTextField<String>(
                        formControlName: 'name',
                        decoration: const InputDecoration(
                          labelText: 'Widget Name *',
                          hintText: 'e.g., user_stats_card',
                          prefixIcon: Icon(Icons.label),
                          border: OutlineInputBorder(),
                        ),
                        validationMessages: {
                          ValidationMessage.required: (_) => 'Widget name is required',
                          ValidationMessage.minLength: (_) => 'Name must be at least 3 characters',
                          ValidationMessage.maxLength: (_) => 'Name cannot exceed 50 characters',
                          ValidationMessage.pattern: (_) => 'Only letters, numbers, hyphens and underscores allowed',
                        },
                      ),
                    ),
                    const SizedBox(width: AppConstants.defaultPadding),
                    Expanded(
                      child: ReactiveTextField<String>(
                        formControlName: 'title',
                        decoration: const InputDecoration(
                          labelText: 'Display Title *',
                          hintText: 'e.g., User Statistics',
                          prefixIcon: Icon(Icons.title),
                          border: OutlineInputBorder(),
                        ),
                        validationMessages: {
                          ValidationMessage.required: (_) => 'Title is required',
                          ValidationMessage.minLength: (_) => 'Title must be at least 3 characters',
                          ValidationMessage.maxLength: (_) => 'Title cannot exceed 100 characters',
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: AppConstants.defaultPadding),

                Row(
                  children: [
                    Expanded(
                      child: ReactiveDropdownField<String>(
                        formControlName: 'type',
                        decoration: const InputDecoration(
                          labelText: 'Widget Type *',
                          prefixIcon: Icon(Icons.widgets),
                          border: OutlineInputBorder(),
                        ),
                        items: WidgetType.values.map((type) => DropdownMenuItem<String>(
                          value: type.name,
                          child: Row(
                            children: [
                              Icon(_getWidgetTypeIcon(type.name), size: 16),
                              const SizedBox(width: 8),
                              Text(type.displayName),
                            ],
                          ),
                        )).toList(),
                        onChanged: (control) {
                          final value = control.value;
                          if (value != null) {
                            setState(() {
                              selectedType = value;
                              _setDefaultProperties();
                            });
                          }
                        },
                      ),
                    ),
                    const SizedBox(width: AppConstants.defaultPadding),
                    Expanded(
                      child: ReactiveTextField<int>(
                        formControlName: 'order',
                        decoration: const InputDecoration(
                          labelText: 'Display Order *',
                          hintText: '0',
                          prefixIcon: Icon(Icons.sort),
                          border: OutlineInputBorder(),
                        ),
                        validationMessages: {
                          ValidationMessage.required: (_) => 'Order is required',
                          ValidationMessage.min: (_) => 'Order must be 0 or greater',
                          ValidationMessage.max: (_) => 'Order cannot exceed 999',
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: AppConstants.defaultPadding),

                ReactiveCheckboxListTile(
                  formControlName: 'isVisible',
                  title: const Text('Visible'),
                  subtitle: const Text('Widget is visible to users'),
                ),

                const SizedBox(height: AppConstants.largePadding),

                // Widget Properties Section
                _buildSectionHeader('Widget Properties'),
                const SizedBox(height: AppConstants.smallPadding),
                _buildPropertiesSection(),

                const SizedBox(height: AppConstants.largePadding),

                // Permissions Section
                _buildSectionHeader('Required Permissions'),
                const SizedBox(height: AppConstants.smallPadding),
                _buildPermissionsSection(permissionsAsync.when(
                  data: (permissionResponse) => AsyncValue.data(permissionResponse.permissions.map((p) => p.toJson()).toList()),
                  loading: () => const AsyncValue.loading(),
                  error: (error, stack) => AsyncValue.error(error, stack),
                )),

                const SizedBox(height: AppConstants.largePadding),

                // Roles Section
                _buildSectionHeader('Allowed Roles'),
                const SizedBox(height: AppConstants.smallPadding),
                _buildRolesSection(rolesAsync),
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: isLoading ? null : _handleSubmit,
          child: isLoading
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : Text(widget.widget == null ? 'Create' : 'Update'),
        ),
      ],
    );
  }

  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: Theme.of(context).textTheme.titleMedium?.copyWith(
        fontWeight: FontWeight.bold,
        color: Theme.of(context).primaryColor,
      ),
    );
  }

  Widget _buildPropertiesSection() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(AppConstants.smallPadding),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: Row(
              children: [
                Icon(_getWidgetTypeIcon(selectedType), size: 16),
                const SizedBox(width: 8),
                Text(
                  'Configure ${_getWidgetTypeDisplayName(selectedType)} properties',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: _buildTypeSpecificProperties(),
          ),
        ],
      ),
    );
  }

  Widget _buildTypeSpecificProperties() {
    switch (selectedType) {
      case 'statCard':
        return _buildStatCardProperties();
      case 'chart':
        return _buildChartProperties();
      case 'table':
        return _buildTableProperties();
      case 'button':
        return _buildButtonProperties();
      case 'text':
        return _buildTextProperties();
      default:
        return _buildGenericProperties();
    }
  }

  Widget _buildStatCardProperties() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: TextFormField(
                initialValue: widgetProperties['title'] ?? '',
                decoration: const InputDecoration(
                  labelText: 'Card Title',
                  border: OutlineInputBorder(),
                ),
                onChanged: (value) => widgetProperties['title'] = value,
              ),
            ),
            const SizedBox(width: AppConstants.defaultPadding),
            Expanded(
              child: TextFormField(
                initialValue: widgetProperties['value'] ?? '',
                decoration: const InputDecoration(
                  labelText: 'Default Value',
                  border: OutlineInputBorder(),
                ),
                onChanged: (value) => widgetProperties['value'] = value,
              ),
            ),
          ],
        ),
        const SizedBox(height: AppConstants.defaultPadding),
        Row(
          children: [
            Expanded(
              child: DropdownButtonFormField<String>(
                value: widgetProperties['icon'] ?? 'analytics',
                decoration: const InputDecoration(
                  labelText: 'Icon',
                  border: OutlineInputBorder(),
                ),
                items: ['analytics', 'people', 'business', 'trending_up', 'assessment']
                    .map((icon) => DropdownMenuItem(value: icon, child: Text(icon)))
                    .toList(),
                onChanged: (value) => setState(() => widgetProperties['icon'] = value),
              ),
            ),
            const SizedBox(width: AppConstants.defaultPadding),
            Expanded(
              child: DropdownButtonFormField<String>(
                value: widgetProperties['color'] ?? 'blue',
                decoration: const InputDecoration(
                  labelText: 'Color',
                  border: OutlineInputBorder(),
                ),
                items: ['blue', 'green', 'red', 'orange', 'purple']
                    .map((color) => DropdownMenuItem(value: color, child: Text(color)))
                    .toList(),
                onChanged: (value) => setState(() => widgetProperties['color'] = value),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildChartProperties() {
    return Column(
      children: [
        DropdownButtonFormField<String>(
          value: widgetProperties['chartType'] ?? 'bar',
          decoration: const InputDecoration(
            labelText: 'Chart Type',
            border: OutlineInputBorder(),
          ),
          items: ['bar', 'line', 'pie', 'area']
              .map((type) => DropdownMenuItem(value: type, child: Text(type)))
              .toList(),
          onChanged: (value) => setState(() => widgetProperties['chartType'] = value),
        ),
        const SizedBox(height: AppConstants.defaultPadding),
        TextFormField(
          initialValue: widgetProperties['dataSource'] ?? '',
          decoration: const InputDecoration(
            labelText: 'Data Source',
            hintText: 'API endpoint or data source',
            border: OutlineInputBorder(),
          ),
          onChanged: (value) => widgetProperties['dataSource'] = value,
        ),
      ],
    );
  }

  Widget _buildTableProperties() {
    return Column(
      children: [
        TextFormField(
          initialValue: widgetProperties['dataSource'] ?? '',
          decoration: const InputDecoration(
            labelText: 'Data Source',
            hintText: 'API endpoint or data source',
            border: OutlineInputBorder(),
          ),
          onChanged: (value) => widgetProperties['dataSource'] = value,
        ),
        const SizedBox(height: AppConstants.defaultPadding),
        Row(
          children: [
            Expanded(
              child: CheckboxListTile(
                title: const Text('Sortable'),
                value: widgetProperties['sortable'] ?? true,
                onChanged: (value) => setState(() => widgetProperties['sortable'] = value),
              ),
            ),
            Expanded(
              child: CheckboxListTile(
                title: const Text('Filterable'),
                value: widgetProperties['filterable'] ?? true,
                onChanged: (value) => setState(() => widgetProperties['filterable'] = value),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildButtonProperties() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: TextFormField(
                initialValue: widgetProperties['text'] ?? '',
                decoration: const InputDecoration(
                  labelText: 'Button Text',
                  border: OutlineInputBorder(),
                ),
                onChanged: (value) => widgetProperties['text'] = value,
              ),
            ),
            const SizedBox(width: AppConstants.defaultPadding),
            Expanded(
              child: TextFormField(
                initialValue: widgetProperties['action'] ?? '',
                decoration: const InputDecoration(
                  labelText: 'Action',
                  hintText: 'Function or route',
                  border: OutlineInputBorder(),
                ),
                onChanged: (value) => widgetProperties['action'] = value,
              ),
            ),
          ],
        ),
        const SizedBox(height: AppConstants.defaultPadding),
        Row(
          children: [
            Expanded(
              child: DropdownButtonFormField<String>(
                value: widgetProperties['style'] ?? 'elevated',
                decoration: const InputDecoration(
                  labelText: 'Button Style',
                  border: OutlineInputBorder(),
                ),
                items: ['elevated', 'outlined', 'text']
                    .map((style) => DropdownMenuItem(value: style, child: Text(style)))
                    .toList(),
                onChanged: (value) => setState(() => widgetProperties['style'] = value),
              ),
            ),
            const SizedBox(width: AppConstants.defaultPadding),
            Expanded(
              child: DropdownButtonFormField<String>(
                value: widgetProperties['color'] ?? 'primary',
                decoration: const InputDecoration(
                  labelText: 'Color',
                  border: OutlineInputBorder(),
                ),
                items: ['primary', 'secondary', 'success', 'warning', 'error']
                    .map((color) => DropdownMenuItem(value: color, child: Text(color)))
                    .toList(),
                onChanged: (value) => setState(() => widgetProperties['color'] = value),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildTextProperties() {
    return Column(
      children: [
        TextFormField(
          initialValue: widgetProperties['content'] ?? '',
          maxLines: 3,
          decoration: const InputDecoration(
            labelText: 'Text Content',
            border: OutlineInputBorder(),
          ),
          onChanged: (value) => widgetProperties['content'] = value,
        ),
        const SizedBox(height: AppConstants.defaultPadding),
        Row(
          children: [
            Expanded(
              child: DropdownButtonFormField<String>(
                value: widgetProperties['style'] ?? 'body',
                decoration: const InputDecoration(
                  labelText: 'Text Style',
                  border: OutlineInputBorder(),
                ),
                items: ['headline', 'title', 'body', 'caption']
                    .map((style) => DropdownMenuItem(value: style, child: Text(style)))
                    .toList(),
                onChanged: (value) => setState(() => widgetProperties['style'] = value),
              ),
            ),
            const SizedBox(width: AppConstants.defaultPadding),
            Expanded(
              child: DropdownButtonFormField<String>(
                value: widgetProperties['alignment'] ?? 'left',
                decoration: const InputDecoration(
                  labelText: 'Alignment',
                  border: OutlineInputBorder(),
                ),
                items: ['left', 'center', 'right', 'justify']
                    .map((align) => DropdownMenuItem(value: align, child: Text(align)))
                    .toList(),
                onChanged: (value) => setState(() => widgetProperties['alignment'] = value),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildGenericProperties() {
    return const Text('No specific properties for this widget type.');
  }

  Widget _buildPermissionsSection(AsyncValue<List<dynamic>> permissionsAsync) {
    return permissionsAsync.when(
      data: (permissions) {
        return Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey[300]!),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            children: [
              Container(
                padding: const EdgeInsets.all(AppConstants.smallPadding),
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(8),
                    topRight: Radius.circular(8),
                  ),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.security, size: 16),
                    const SizedBox(width: 8),
                    Text(
                      'Select required permissions for this widget',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ],
                ),
              ),
              Container(
                constraints: const BoxConstraints(maxHeight: 150),
                child: ListView.builder(
                  shrinkWrap: true,
                  itemCount: permissions.length,
                  itemBuilder: (context, index) {
                    final permission = permissions[index];
                    final permissionName = permission['name'] ?? permission.toString();
                    final isSelected = selectedPermissions.contains(permissionName);

                    return CheckboxListTile(
                      dense: true,
                      title: Text(permissionName),
                      subtitle: permission is Map ? Text(permission['description'] ?? '') : null,
                      value: isSelected,
                      onChanged: (value) {
                        setState(() {
                          if (value == true) {
                            selectedPermissions.add(permissionName);
                          } else {
                            selectedPermissions.remove(permissionName);
                          }
                        });
                      },
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Text('Error loading permissions: $error'),
    );
  }

  Widget _buildRolesSection(AsyncValue<List<dynamic>> rolesAsync) {
    return rolesAsync.when(
      data: (roles) {
        return Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey[300]!),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            children: [
              Container(
                padding: const EdgeInsets.all(AppConstants.smallPadding),
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(8),
                    topRight: Radius.circular(8),
                  ),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.group, size: 16),
                    const SizedBox(width: 8),
                    Text(
                      'Select roles that can see this widget',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ],
                ),
              ),
              Container(
                constraints: const BoxConstraints(maxHeight: 120),
                child: ListView.builder(
                  shrinkWrap: true,
                  itemCount: roles.length,
                  itemBuilder: (context, index) {
                    final role = roles[index];
                    final roleName = role['name'] ?? role.toString();
                    final isSelected = selectedRoles.contains(roleName);

                    return CheckboxListTile(
                      dense: true,
                      title: Text(roleName),
                      subtitle: role is Map ? Text(role['description'] ?? '') : null,
                      value: isSelected,
                      onChanged: (value) {
                        setState(() {
                          if (value == true) {
                            selectedRoles.add(roleName);
                          } else {
                            selectedRoles.remove(roleName);
                          }
                        });
                      },
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Text('Error loading roles: $error'),
    );
  }

  IconData _getWidgetTypeIcon(String type) {
    switch (type) {
      case 'statCard':
        return Icons.analytics;
      case 'chart':
        return Icons.bar_chart;
      case 'table':
        return Icons.table_chart;
      case 'form':
        return Icons.check_box;
      case 'button':
        return Icons.smart_button;
      case 'text':
        return Icons.text_fields;
      case 'image':
        return Icons.image;
      case 'list':
        return Icons.list;
      case 'grid':
        return Icons.grid_view;
      default:
        return Icons.widgets;
    }
  }

  String _getWidgetTypeDisplayName(String type) {
    switch (type) {
      case 'statCard':
        return 'Stat Card';
      case 'chart':
        return 'Chart';
      case 'table':
        return 'Table';
      case 'form':
        return 'Form';
      case 'button':
        return 'Button';
      case 'text':
        return 'Text';
      case 'image':
        return 'Image';
      case 'list':
        return 'List';
      case 'grid':
        return 'Grid';
      default:
        return 'Custom';
    }
  }

  Future<void> _handleSubmit() async {
    if (form.invalid) {
      form.markAllAsTouched();
      return;
    }

    setState(() {
      isLoading = true;
    });

    try {
      final formValue = form.value;

      final widgetData = {
        'name': formValue['name'],
        'title': formValue['title'],
        'type': formValue['type'],
        'properties': widgetProperties,
        'requiredPermissions': selectedPermissions,
        'allowedRoles': selectedRoles,
        'isVisible': formValue['isVisible'],
        'order': formValue['order'],
      };

      bool success;
      if (widget.widget == null) {
        success = await ref.read(widgetManagementProvider.notifier).createWidget(widgetData);
      } else {
        success = await ref.read(widgetManagementProvider.notifier).updateWidget(widget.widget!.id, widgetData);
      }

      if (success && mounted) {
        Navigator.of(context).pop();
        AppUtils.showSuccessSnackBar(
          context,
          widget.widget == null ? 'Widget created successfully' : 'Widget updated successfully',
        );
      }
    } catch (e) {
      if (mounted) {
        AppUtils.showErrorSnackBar(context, 'Failed to save widget: $e');
      }
    } finally {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    }
  }
}
