import 'package:dio/dio.dart';
import '../../../shared/models/attendance.dart';
import '../domain/attendance_repository.dart';
import 'attendance_api_service.dart';

class AttendanceRepositoryImpl implements AttendanceRepository {
  final AttendanceApiService _apiService;

  AttendanceRepositoryImpl(this._apiService);

  @override
  Future<List<AttendanceRecord>> getSiteAttendance({
    String? propertyId,
    String? date,
    String? startDate,
    String? endDate,
    String? userId,
    int? page,
    int? limit,
  }) async {
    try {
      final response = await _apiService.getPropertyAttendance(
        propertyId: propertyId,
        date: date,
        startDate: startDate,
        endDate: endDate,
        userId: userId,
        page: page,
        limit: limit,
      );

      if (response.success && response.data != null) {
        return response.data!;
      } else {
        throw Exception(response.error ?? 'Failed to fetch property attendance');
      }
    } on DioException catch (e) {
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      throw Exception('Failed to fetch property attendance: $e');
    }
  }

  @override
  Future<List<AttendanceRecord>> submitSiteAttendance(SubmitSiteAttendanceRequest request) async {
    try {
      final response = await _apiService.submitSiteAttendance(request);

      if (response.success && response.data != null) {
        return response.data!;
      } else {
        throw Exception(response.error ?? 'Failed to submit site attendance');
      }
    } on DioException catch (e) {
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      throw Exception('Failed to submit site attendance: $e');
    }
  }

  @override
  Future<AttendanceRecord> getSiteAttendanceById(String id) async {
    try {
      final response = await _apiService.getSiteAttendanceById(id);

      if (response.success && response.data != null) {
        return response.data!;
      } else {
        throw Exception(response.error ?? 'Failed to fetch attendance record');
      }
    } on DioException catch (e) {
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      throw Exception('Failed to fetch attendance record: $e');
    }
  }

  @override
  Future<AttendanceRecord> updateSiteAttendance(String id, UpdateSiteAttendanceRequest request) async {
    try {
      final response = await _apiService.updateSiteAttendance(id, request);

      if (response.success && response.data != null) {
        return response.data!;
      } else {
        throw Exception(response.error ?? 'Failed to update attendance record');
      }
    } on DioException catch (e) {
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      throw Exception('Failed to update attendance record: $e');
    }
  }

  @override
  Future<void> deleteSiteAttendance(String id) async {
    try {
      final response = await _apiService.deleteSiteAttendance(id);

      if (!response.success) {
        throw Exception(response.error ?? 'Failed to delete attendance record');
      }
    } on DioException catch (e) {
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      throw Exception('Failed to delete attendance record: $e');
    }
  }

  @override
  Future<List<AttendanceRecord>> getOfficeAttendance({
    String? officeId,
    String? date,
    String? startDate,
    String? endDate,
    String? userId,
    int? page,
    int? limit,
  }) async {
    try {
      final response = await _apiService.getAttendanceByProperty(
        propertyId: officeId, // Map officeId to propertyId
        date: date,
        startDate: startDate,
        endDate: endDate,
        userId: userId,
        page: page,
        limit: limit,
      );

      if (response.success && response.data != null) {
        return response.data!;
      } else {
        throw Exception(response.error ?? 'Failed to fetch office attendance');
      }
    } on DioException catch (e) {
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      throw Exception('Failed to fetch office attendance: $e');
    }
  }

  @override
  Future<List<AttendanceRecord>> submitOfficeAttendance(SubmitOfficeAttendanceRequest request) async {
    try {
      // Convert SubmitOfficeAttendanceRequest to SubmitPropertyAttendanceRequest
      final propertyRequest = SubmitPropertyAttendanceRequest(
        propertyId: request.officeId,
        date: request.date,
        attendance: request.attendance,
      );
      final response = await _apiService.submitAttendance(propertyRequest);

      if (response.success && response.data != null) {
        return response.data!;
      } else {
        throw Exception(response.error ?? 'Failed to submit office attendance');
      }
    } on DioException catch (e) {
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      throw Exception('Failed to submit office attendance: $e');
    }
  }

  @override
  Future<AttendanceRecord> getOfficeAttendanceById(String id) async {
    try {
      final response = await _apiService.getAttendanceById(id);

      if (response.success && response.data != null) {
        return response.data!;
      } else {
        throw Exception(response.error ?? 'Failed to fetch office attendance record');
      }
    } on DioException catch (e) {
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      throw Exception('Failed to fetch office attendance record: $e');
    }
  }

  @override
  Future<AttendanceRecord> updateOfficeAttendance(String id, UpdateOfficeAttendanceRequest request) async {
    try {
      // Convert UpdateOfficeAttendanceRequest to UpdatePropertyAttendanceRequest
      final propertyRequest = UpdatePropertyAttendanceRequest(
        status: request.status,
        checkInTime: request.checkInTime,
        checkOutTime: request.checkOutTime,
        notes: request.notes,
      );
      final response = await _apiService.updateAttendance(id, propertyRequest);

      if (response.success && response.data != null) {
        return response.data!;
      } else {
        throw Exception(response.error ?? 'Failed to update office attendance record');
      }
    } on DioException catch (e) {
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      throw Exception('Failed to update office attendance record: $e');
    }
  }

  @override
  Future<void> deleteOfficeAttendance(String id) async {
    try {
      final response = await _apiService.deleteAttendance(id);

      if (!response.success) {
        throw Exception(response.error ?? 'Failed to delete office attendance record');
      }
    } on DioException catch (e) {
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      throw Exception('Failed to delete office attendance record: $e');
    }
  }

  @override
  Future<AttendanceSummary> getAttendanceSummary({
    String? startDate,
    String? endDate,
    String? propertyId,
    String? officeId,
  }) async {
    try {
      final response = await _apiService.getAttendanceSummary(
        startDate: startDate,
        endDate: endDate,
        propertyId: propertyId ?? officeId, // Use propertyId or fallback to officeId
      );

      if (response.success && response.data != null) {
        return response.data!;
      } else {
        throw Exception(response.error ?? 'Failed to fetch attendance summary');
      }
    } on DioException catch (e) {
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      throw Exception('Failed to fetch attendance summary: $e');
    }
  }

  @override
  Future<UserAttendanceReport> getUserAttendanceReport(
    String userId, {
    String? startDate,
    String? endDate,
  }) async {
    try {
      final response = await _apiService.getUserAttendanceReport(
        userId,
        startDate,
        endDate,
      );

      if (response.success && response.data != null) {
        return response.data!;
      } else {
        throw Exception(response.error ?? 'Failed to fetch user attendance report');
      }
    } on DioException catch (e) {
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      throw Exception('Failed to fetch user attendance report: $e');
    }
  }

  // Additional methods implementation
  @override
  Future<List<AttendanceRecord>> getAllAttendanceRecords() async {
    try {
      // Get both site and office attendance
      final siteAttendance = await getSiteAttendance();
      final officeAttendance = await getOfficeAttendance();

      return [...siteAttendance, ...officeAttendance];
    } catch (e) {
      throw Exception('Failed to fetch all attendance records: $e');
    }
  }

  @override
  Future<List<AttendanceRecord>> getAttendanceByDateRange(DateTime startDate, DateTime endDate) async {
    try {
      final startDateStr = startDate.toIso8601String().split('T')[0];
      final endDateStr = endDate.toIso8601String().split('T')[0];

      final siteAttendance = await getSiteAttendance(
        startDate: startDateStr,
        endDate: endDateStr,
      );

      final officeAttendance = await getOfficeAttendance(
        startDate: startDateStr,
        endDate: endDateStr,
      );

      return [...siteAttendance, ...officeAttendance];
    } catch (e) {
      throw Exception('Failed to fetch attendance by date range: $e');
    }
  }

  @override
  Future<AttendanceRecord> createAttendanceRecord(AttendanceRecord record) async {
    try {
      if (record.isSiteAttendance) {
        final attendanceEntry = AttendanceEntry(
          userId: record.userId,
          status: record.status,
          checkInTime: record.checkInTime,
          checkOutTime: record.checkOutTime,
          notes: record.notes,
        );

        final request = SubmitSiteAttendanceRequest(
          propertyId: record.propertyId!,
          date: record.date.toIso8601String().split('T')[0],
          attendance: [attendanceEntry],
        );

        final results = await submitSiteAttendance(request);
        return results.first; // Return the first created record
      } else {
        final attendanceEntry = AttendanceEntry(
          userId: record.userId,
          status: record.status,
          checkInTime: record.checkInTime,
          checkOutTime: record.checkOutTime,
          notes: record.notes,
        );

        final request = SubmitOfficeAttendanceRequest(
          officeId: record.officeId!,
          date: record.date.toIso8601String().split('T')[0],
          attendance: [attendanceEntry],
        );

        final results = await submitOfficeAttendance(request);
        return results.first; // Return the first created record
      }
    } catch (e) {
      throw Exception('Failed to create attendance record: $e');
    }
  }

  @override
  Future<AttendanceRecord> updateAttendanceRecord(AttendanceRecord record) async {
    try {
      if (record.isSiteAttendance) {
        final request = UpdateSiteAttendanceRequest(
          checkInTime: record.checkInTime,
          checkOutTime: record.checkOutTime,
          status: record.status,
          notes: record.notes,
        );

        return await updateSiteAttendance(record.id, request);
      } else {
        final request = UpdateOfficeAttendanceRequest(
          checkInTime: record.checkInTime,
          checkOutTime: record.checkOutTime,
          status: record.status,
          notes: record.notes,
        );

        return await updateOfficeAttendance(record.id, request);
      }
    } catch (e) {
      throw Exception('Failed to update attendance record: $e');
    }
  }

  @override
  Future<void> deleteAttendanceRecord(String recordId) async {
    try {
      // Try to delete as site attendance first, then office attendance
      try {
        await deleteSiteAttendance(recordId);
      } catch (e) {
        await deleteOfficeAttendance(recordId);
      }
    } catch (e) {
      throw Exception('Failed to delete attendance record: $e');
    }
  }

  @override
  Future<void> markPresent(String workerId, DateTime date, {
    DateTime? checkInTime,
    String? notes,
    String attendanceType = 'site',
  }) async {
    try {
      final record = AttendanceRecord.createSiteAttendance(
        propertyId: '', // This should be provided
        userId: workerId,
        workerName: '', // This should be provided
        date: date,
        checkInTime: (checkInTime ?? DateTime.now()).toIso8601String(),
        status: 'present',
        notes: notes,
      );

      await createAttendanceRecord(record);
    } catch (e) {
      throw Exception('Failed to mark present: $e');
    }
  }

  @override
  Future<void> markAbsent(String workerId, DateTime date, {String? reason}) async {
    try {
      final record = AttendanceRecord.createSiteAttendance(
        propertyId: '', // This should be provided
        userId: workerId,
        workerName: '', // This should be provided
        date: date,
        status: 'absent',
        notes: reason,
      );

      await createAttendanceRecord(record);
    } catch (e) {
      throw Exception('Failed to mark absent: $e');
    }
  }

  @override
  Future<void> checkOut(String recordId, DateTime checkOutTime) async {
    try {
      // Find the record first
      AttendanceRecord? record;

      try {
        record = await getSiteAttendanceById(recordId);
      } catch (e) {
        try {
          record = await getOfficeAttendanceById(recordId);
        } catch (e2) {
          throw Exception('Attendance record not found');
        }
      }

      final updatedRecord = record.copyWith(
        checkOutTime: checkOutTime.toIso8601String(),
      );

      await updateAttendanceRecord(updatedRecord);
    } catch (e) {
      throw Exception('Failed to check out: $e');
    }
  }
}
