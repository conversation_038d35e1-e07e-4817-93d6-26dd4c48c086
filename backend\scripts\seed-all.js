const { seedPermissions } = require('./seed-permissions');
const { seedThresholds } = require('./seed-thresholds');

async function seedAll() {
  console.log('🚀 Starting comprehensive database seeding...\n');

  try {
    // Seed permissions
    console.log('1️⃣ Seeding permission configurations...');
    await seedPermissions();
    console.log('✅ Permission seeding completed!\n');

    // Seed thresholds
    console.log('2️⃣ Seeding threshold configurations...');
    await seedThresholds();
    console.log('✅ Threshold seeding completed!\n');

    console.log('🎉 All seeding operations completed successfully!');
    console.log('\n📋 Summary:');
    console.log('  ✓ Permission configurations');
    console.log('  ✓ Threshold configurations');
    console.log('\n🔧 Your backend is now ready with:');
    console.log('  • Multi-field login support (email/username/phone)');
    console.log('  • Server-sent events for real-time notifications');
    console.log('  • Threshold monitoring with automatic alerts');
    console.log('  • Permission-based access control');
    console.log('  • Configurable screen and widget permissions');
    console.log('\n🚀 You can now start the backend server and test the new features!');

  } catch (error) {
    console.error('💥 Seeding failed:', error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  seedAll()
    .then(() => {
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Comprehensive seeding failed:', error);
      process.exit(1);
    });
}

module.exports = { seedAll };
