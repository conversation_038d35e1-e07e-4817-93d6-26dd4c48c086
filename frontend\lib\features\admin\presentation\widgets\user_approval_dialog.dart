import 'package:flutter/material.dart';
import '../../../../shared/models/user.dart';

class UserApprovalDialog extends StatefulWidget {
  final User user;
  final Function(String, String?)? onApprove;
  final Function(String?)? onReject;

  const UserApprovalDialog({
    super.key,
    required this.user,
    this.onApprove,
    this.onReject,
  });

  @override
  State<UserApprovalDialog> createState() => _UserApprovalDialogState();
}

class _UserApprovalDialogState extends State<UserApprovalDialog> {
  String? selectedRole;
  String? comments;
  bool isLoading = false;

  final List<String> availableRoles = [
    'admin',
    'manager',
    'security',
    'maintenance',
    'viewer',
  ];

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('User Approval - ${widget.user.fullName}'),
      content: SizedBox(
        width: 400,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // User Info
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        CircleAvatar(
                          backgroundColor: Colors.blue.withValues(alpha: 0.1),
                          child: Text(
                            _getInitials(),
                            style: const TextStyle(
                              color: Colors.blue,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                widget.user.fullName,
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                ),
                              ),
                              Text(
                                widget.user.email,
                                style: TextStyle(
                                  color: Colors.grey[600],
                                  fontSize: 14,
                                ),
                              ),
                              if (widget.user.phone != null)
                                Text(
                                  widget.user.phone!,
                                  style: TextStyle(
                                    color: Colors.grey[600],
                                    fontSize: 14,
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    Text(
                      'Requested Role: ${widget.user.primaryRole ?? 'Not specified'}',
                      style: const TextStyle(fontWeight: FontWeight.w500),
                    ),
                    Text(
                      'Registration Date: ${_formatDate(widget.user.createdAt)}',
                      style: TextStyle(color: Colors.grey[600]),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Role Assignment
            const Text(
              'Assign Role',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 8),
            DropdownButtonFormField<String>(
              value: selectedRole,
              decoration: const InputDecoration(
                labelText: 'Select Role',
                border: OutlineInputBorder(),
              ),
              items: availableRoles.map((role) {
                return DropdownMenuItem(
                  value: role,
                  child: Row(
                    children: [
                      Icon(_getRoleIcon(role), size: 20),
                      const SizedBox(width: 8),
                      Text(_formatRoleName(role)),
                    ],
                  ),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  selectedRole = value;
                });
              },
            ),
            
            const SizedBox(height: 16),
            
            // Comments
            const Text(
              'Comments (Optional)',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 8),
            TextField(
              decoration: const InputDecoration(
                hintText: 'Add any comments about this approval/rejection',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
              onChanged: (value) {
                comments = value.isEmpty ? null : value;
              },
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        TextButton(
          onPressed: isLoading ? null : _rejectUser,
          style: TextButton.styleFrom(foregroundColor: Colors.red),
          child: isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('Reject'),
        ),
        ElevatedButton(
          onPressed: isLoading || selectedRole == null ? null : _approveUser,
          style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
          child: isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('Approve'),
        ),
      ],
    );
  }

  String _getInitials() {
    final names = widget.user.fullName.split(' ');
    if (names.length >= 2) {
      return '${names[0][0]}${names[1][0]}'.toUpperCase();
    } else if (names.isNotEmpty) {
      return names[0][0].toUpperCase();
    }
    return 'U';
  }

  IconData _getRoleIcon(String role) {
    switch (role.toLowerCase()) {
      case 'admin':
        return Icons.admin_panel_settings;
      case 'manager':
        return Icons.manage_accounts;
      case 'security':
        return Icons.security;
      case 'maintenance':
        return Icons.build;
      case 'viewer':
        return Icons.visibility;
      default:
        return Icons.person;
    }
  }

  String _formatRoleName(String role) {
    return role[0].toUpperCase() + role.substring(1);
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _approveUser() async {
    if (selectedRole == null) return;

    setState(() {
      isLoading = true;
    });

    try {
      widget.onApprove?.call(selectedRole!, comments);
      
      if (mounted) {
        Navigator.of(context).pop('approved');
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('User approved successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to approve user: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    }
  }

  void _rejectUser() async {
    setState(() {
      isLoading = true;
    });

    try {
      widget.onReject?.call(comments);
      
      if (mounted) {
        Navigator.of(context).pop('rejected');
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('User rejected'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to reject user: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    }
  }
}

// Show dialog helper function
Future<String?> showUserApprovalDialog(
  BuildContext context, {
  required User user,
  Function(String, String?)? onApprove,
  Function(String?)? onReject,
}) {
  return showDialog<String>(
    context: context,
    builder: (context) => UserApprovalDialog(
      user: user,
      onApprove: onApprove,
      onReject: onReject,
    ),
  );
}
