import 'package:flutter/foundation.dart';

/// Configuration for screen-level permissions
class ScreenPermissionConfig {
  final String screenName;
  final List<String> requiredPermissions;
  final List<String> allowedRoles;
  final bool isEnabled;
  final Map<String, WidgetPermissionConfig> widgetPermissions;

  const ScreenPermissionConfig({
    required this.screenName,
    required this.requiredPermissions,
    required this.allowedRoles,
    this.isEnabled = true,
    this.widgetPermissions = const {},
  });

  ScreenPermissionConfig copyWith({
    String? screenName,
    List<String>? requiredPermissions,
    List<String>? allowedRoles,
    bool? isEnabled,
    Map<String, WidgetPermissionConfig>? widgetPermissions,
  }) {
    return ScreenPermissionConfig(
      screenName: screenName ?? this.screenName,
      requiredPermissions: requiredPermissions ?? this.requiredPermissions,
      allowedRoles: allowedRoles ?? this.allowedRoles,
      isEnabled: isEnabled ?? this.isEnabled,
      widgetPermissions: widgetPermissions ?? this.widgetPermissions,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'screen_name': screenName,
      'required_permissions': requiredPermissions,
      'allowed_roles': allowedRoles,
      'is_enabled': isEnabled,
      'widget_permissions': widgetPermissions.map(
        (key, value) => MapEntry(key, value.toJson()),
      ),
    };
  }

  factory ScreenPermissionConfig.fromJson(Map<String, dynamic> json) {
    return ScreenPermissionConfig(
      screenName: json['screen_name'] as String,
      requiredPermissions: (json['required_permissions'] as List<dynamic>).cast<String>(),
      allowedRoles: (json['allowed_roles'] as List<dynamic>).cast<String>(),
      isEnabled: json['is_enabled'] as bool? ?? true,
      widgetPermissions: (json['widget_permissions'] as Map<String, dynamic>?)
              ?.map((key, value) => MapEntry(
                    key,
                    WidgetPermissionConfig.fromJson(value as Map<String, dynamic>),
                  )) ??
          {},
    );
  }
}

/// Configuration for widget-level permissions
class WidgetPermissionConfig {
  final String widgetName;
  final List<String> requiredPermissions;
  final List<String> allowedRoles;
  final bool isVisible;
  final bool isEnabled;
  final Map<String, dynamic>? customProperties;

  const WidgetPermissionConfig({
    required this.widgetName,
    required this.requiredPermissions,
    required this.allowedRoles,
    this.isVisible = true,
    this.isEnabled = true,
    this.customProperties,
  });

  WidgetPermissionConfig copyWith({
    String? widgetName,
    List<String>? requiredPermissions,
    List<String>? allowedRoles,
    bool? isVisible,
    bool? isEnabled,
    Map<String, dynamic>? customProperties,
  }) {
    return WidgetPermissionConfig(
      widgetName: widgetName ?? this.widgetName,
      requiredPermissions: requiredPermissions ?? this.requiredPermissions,
      allowedRoles: allowedRoles ?? this.allowedRoles,
      isVisible: isVisible ?? this.isVisible,
      isEnabled: isEnabled ?? this.isEnabled,
      customProperties: customProperties ?? this.customProperties,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'widget_name': widgetName,
      'required_permissions': requiredPermissions,
      'allowed_roles': allowedRoles,
      'is_visible': isVisible,
      'is_enabled': isEnabled,
      'custom_properties': customProperties,
    };
  }

  factory WidgetPermissionConfig.fromJson(Map<String, dynamic> json) {
    return WidgetPermissionConfig(
      widgetName: json['widget_name'] as String,
      requiredPermissions: (json['required_permissions'] as List<dynamic>).cast<String>(),
      allowedRoles: (json['allowed_roles'] as List<dynamic>).cast<String>(),
      isVisible: json['is_visible'] as bool? ?? true,
      isEnabled: json['is_enabled'] as bool? ?? true,
      customProperties: json['custom_properties'] as Map<String, dynamic>?,
    );
  }
}

/// Default permission configurations for existing screens
class DefaultScreenPermissions {
  static const Map<String, ScreenPermissionConfig> _defaultConfigs = {
    // Dashboard Screens
    'dashboard': ScreenPermissionConfig(
      screenName: 'dashboard',
      requiredPermissions: ['dashboard.read'],
      allowedRoles: ['admin', 'manager', 'user'],
      widgetPermissions: {
        'property_stats': WidgetPermissionConfig(
          widgetName: 'property_stats',
          requiredPermissions: ['properties.read'],
          allowedRoles: ['admin', 'manager', 'user'],
        ),
        'recent_activities': WidgetPermissionConfig(
          widgetName: 'recent_activities',
          requiredPermissions: ['activities.read'],
          allowedRoles: ['admin', 'manager', 'user'],
        ),
        'alerts_summary': WidgetPermissionConfig(
          widgetName: 'alerts_summary',
          requiredPermissions: ['alerts.read'],
          allowedRoles: ['admin', 'manager'],
        ),
      },
    ),

    // Property Management
    'properties': ScreenPermissionConfig(
      screenName: 'properties',
      requiredPermissions: ['properties.read'],
      allowedRoles: ['admin', 'manager', 'user'],
      widgetPermissions: {
        'add_property': WidgetPermissionConfig(
          widgetName: 'add_property',
          requiredPermissions: ['properties.create'],
          allowedRoles: ['admin', 'manager'],
        ),
        'edit_property': WidgetPermissionConfig(
          widgetName: 'edit_property',
          requiredPermissions: ['properties.update'],
          allowedRoles: ['admin', 'manager'],
        ),
        'delete_property': WidgetPermissionConfig(
          widgetName: 'delete_property',
          requiredPermissions: ['properties.delete'],
          allowedRoles: ['admin'],
        ),
      },
    ),

    // Maintenance Management
    'maintenance': ScreenPermissionConfig(
      screenName: 'maintenance',
      requiredPermissions: ['maintenance.read'],
      allowedRoles: ['admin', 'manager', 'maintenance'],
      widgetPermissions: {
        'create_issue': WidgetPermissionConfig(
          widgetName: 'create_issue',
          requiredPermissions: ['maintenance.create'],
          allowedRoles: ['admin', 'manager', 'maintenance'],
        ),
        'assign_issue': WidgetPermissionConfig(
          widgetName: 'assign_issue',
          requiredPermissions: ['maintenance.assign'],
          allowedRoles: ['admin', 'manager'],
        ),
        'close_issue': WidgetPermissionConfig(
          widgetName: 'close_issue',
          requiredPermissions: ['maintenance.update'],
          allowedRoles: ['admin', 'manager', 'maintenance'],
        ),
      },
    ),

    // Attendance Management
    'attendance': ScreenPermissionConfig(
      screenName: 'attendance',
      requiredPermissions: ['attendance.read'],
      allowedRoles: ['admin', 'manager', 'user'],
      widgetPermissions: {
        'mark_attendance': WidgetPermissionConfig(
          widgetName: 'mark_attendance',
          requiredPermissions: ['attendance.create'],
          allowedRoles: ['admin', 'manager', 'user'],
        ),
        'view_reports': WidgetPermissionConfig(
          widgetName: 'view_reports',
          requiredPermissions: ['attendance.reports'],
          allowedRoles: ['admin', 'manager'],
        ),
        'export_data': WidgetPermissionConfig(
          widgetName: 'export_data',
          requiredPermissions: ['attendance.export'],
          allowedRoles: ['admin', 'manager'],
        ),
      },
    ),

    // Generator Fuel Management
    'generator_fuel': ScreenPermissionConfig(
      screenName: 'generator_fuel',
      requiredPermissions: ['fuel.read'],
      allowedRoles: ['admin', 'manager', 'fuel_operator'],
      widgetPermissions: {
        'add_fuel': WidgetPermissionConfig(
          widgetName: 'add_fuel',
          requiredPermissions: ['fuel.create'],
          allowedRoles: ['admin', 'manager', 'fuel_operator'],
        ),
        'view_consumption': WidgetPermissionConfig(
          widgetName: 'view_consumption',
          requiredPermissions: ['fuel.reports'],
          allowedRoles: ['admin', 'manager'],
        ),
        'fuel_alerts': WidgetPermissionConfig(
          widgetName: 'fuel_alerts',
          requiredPermissions: ['fuel.alerts'],
          allowedRoles: ['admin', 'manager'],
        ),
      },
    ),

    // Admin Screens
    'admin_dashboard': ScreenPermissionConfig(
      screenName: 'admin_dashboard',
      requiredPermissions: ['admin.access'],
      allowedRoles: ['admin'],
    ),

    'user_management': ScreenPermissionConfig(
      screenName: 'user_management',
      requiredPermissions: ['users.manage'],
      allowedRoles: ['admin'],
      widgetPermissions: {
        'create_user': WidgetPermissionConfig(
          widgetName: 'create_user',
          requiredPermissions: ['users.create'],
          allowedRoles: ['admin'],
        ),
        'edit_user': WidgetPermissionConfig(
          widgetName: 'edit_user',
          requiredPermissions: ['users.update'],
          allowedRoles: ['admin'],
        ),
        'delete_user': WidgetPermissionConfig(
          widgetName: 'delete_user',
          requiredPermissions: ['users.delete'],
          allowedRoles: ['admin'],
        ),
        'assign_roles': WidgetPermissionConfig(
          widgetName: 'assign_roles',
          requiredPermissions: ['users.assign_roles'],
          allowedRoles: ['admin'],
        ),
      },
    ),

    'role_management': ScreenPermissionConfig(
      screenName: 'role_management',
      requiredPermissions: ['roles.manage'],
      allowedRoles: ['admin'],
    ),

    'threshold_config': ScreenPermissionConfig(
      screenName: 'threshold_config',
      requiredPermissions: ['thresholds.manage'],
      allowedRoles: ['admin', 'manager'],
    ),

    'screen_management': ScreenPermissionConfig(
      screenName: 'screen_management',
      requiredPermissions: ['screens.manage'],
      allowedRoles: ['admin'],
    ),

    'widget_management': ScreenPermissionConfig(
      screenName: 'widget_management',
      requiredPermissions: ['widgets.manage'],
      allowedRoles: ['admin'],
    ),
  };

  /// Get default configuration for a screen
  static ScreenPermissionConfig? getDefaultConfig(String screenName) {
    return _defaultConfigs[screenName];
  }

  /// Get all default configurations
  static Map<String, ScreenPermissionConfig> getAllDefaultConfigs() {
    return Map.unmodifiable(_defaultConfigs);
  }

  /// Check if a screen has default configuration
  static bool hasDefaultConfig(String screenName) {
    return _defaultConfigs.containsKey(screenName);
  }
}
