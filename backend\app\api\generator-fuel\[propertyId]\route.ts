import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';
import { validateRequest } from '@/lib/validation';
import { createApiResponse, getRequestBody, handleError, corsHeaders, getQueryParams } from '@/lib/utils';
import Joi from 'joi';

const createFuelLogSchema = Joi.object({
  fuel_level_liters: Joi.number().positive().required(),
  consumption_rate: Joi.number().positive().optional(),
  runtime_hours: Joi.number().positive().optional(),
  efficiency_percentage: Joi.number().min(0).max(100).optional(),
  last_maintenance: Joi.date().optional(),
  next_maintenance: Joi.date().optional(),
  notes: Joi.string().optional(),
});

async function getFuelLogsHandler(
  request: NextRequest, 
  context: { params: { propertyId: string } }, 
  currentUser: any
) {
  try {
    const { propertyId } = context.params;
    const params = getQueryParams(request);
    const { limit = '10', offset = '0' } = params;

    // Verify property exists
    const property = await prisma.property.findUnique({
      where: { id: propertyId },
    });

    if (!property) {
      return Response.json(
        createApiResponse(null, 'Property not found', 'NOT_FOUND'),
        { status: 404 }
      );
    }

    // Get fuel logs for the property
    const fuelLogs = await prisma.generatorFuelLog.findMany({
      where: {
        propertyId,
      },
      include: {
        recorder: {
          select: {
            id: true,
            fullName: true,
            email: true,
          },
        },
      },
      orderBy: {
        recordedAt: 'desc',
      },
      take: parseInt(limit),
      skip: parseInt(offset),
    });

    // Transform data to match API response format
    const transformedLogs = fuelLogs.map(log => ({
      id: log.id,
      property_id: log.propertyId,
      fuel_level_liters: parseFloat(log.fuelLevelLiters.toString()),
      consumption_rate: log.consumptionRate ? parseFloat(log.consumptionRate.toString()) : null,
      runtime_hours: log.runtimeHours ? parseFloat(log.runtimeHours.toString()) : null,
      efficiency_percentage: log.efficiencyPercentage ? parseFloat(log.efficiencyPercentage.toString()) : null,
      last_maintenance: log.lastMaintenance,
      next_maintenance: log.nextMaintenance,
      notes: log.notes,
      recorded_by: log.recordedBy,
      recorded_at: log.recordedAt,
      recorder: log.recorder ? {
        id: log.recorder.id,
        full_name: log.recorder.fullName,
        email: log.recorder.email,
      } : null,
    }));

    return Response.json(
      createApiResponse(transformedLogs),
      { 
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to fetch fuel logs');
  }
}

async function createFuelLogHandler(
  request: NextRequest, 
  context: { params: { propertyId: string } }, 
  currentUser: any
) {
  try {
    const { propertyId } = context.params;
    const body = await getRequestBody(request);
    
    // Validate request body
    const validation = validateRequest(createFuelLogSchema, body);
    if (!validation.isValid) {
      return Response.json(
        createApiResponse(null, 'Validation failed', 'VALIDATION_ERROR'),
        { status: 400 }
      );
    }

    const { 
      fuel_level_liters, 
      consumption_rate, 
      runtime_hours, 
      efficiency_percentage, 
      last_maintenance,
      next_maintenance,
      notes 
    } = validation.data;

    // Verify property exists
    const property = await prisma.property.findUnique({
      where: { id: propertyId },
    });

    if (!property) {
      return Response.json(
        createApiResponse(null, 'Property not found', 'NOT_FOUND'),
        { status: 404 }
      );
    }

    // Create fuel log
    const fuelLog = await prisma.generatorFuelLog.create({
      data: {
        propertyId,
        fuelLevelLiters: fuel_level_liters,
        consumptionRate: consumption_rate,
        runtimeHours: runtime_hours,
        efficiencyPercentage: efficiency_percentage,
        lastMaintenance: last_maintenance ? new Date(last_maintenance) : null,
        nextMaintenance: next_maintenance ? new Date(next_maintenance) : null,
        notes,
        recordedBy: currentUser.id,
      },
    });

    return Response.json(
      createApiResponse({
        message: 'Fuel log created successfully',
        log: {
          id: fuelLog.id,
          property_id: fuelLog.propertyId,
          fuel_level_liters: parseFloat(fuelLog.fuelLevelLiters.toString()),
          consumption_rate: fuelLog.consumptionRate ? parseFloat(fuelLog.consumptionRate.toString()) : null,
          runtime_hours: fuelLog.runtimeHours ? parseFloat(fuelLog.runtimeHours.toString()) : null,
          efficiency_percentage: fuelLog.efficiencyPercentage ? parseFloat(fuelLog.efficiencyPercentage.toString()) : null,
          last_maintenance: fuelLog.lastMaintenance,
          next_maintenance: fuelLog.nextMaintenance,
          notes: fuelLog.notes,
          recorded_by: fuelLog.recordedBy,
          recorded_at: fuelLog.recordedAt,
        },
      }),
      { 
        status: 201,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to create fuel log');
  }
}

export const GET = requireAuth(getFuelLogsHandler);
export const POST = requireAuth(createFuelLogHandler);

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: corsHeaders(),
  });
}
