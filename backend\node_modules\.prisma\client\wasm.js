
Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('@prisma/client/runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 5.22.0
 * Query Engine version: 605197351a3c8bdd595af2d2a9bc3025bca48ea2
 */
Prisma.prismaVersion = {
  client: "5.22.0",
  engine: "605197351a3c8bdd595af2d2a9bc3025bca48ea2"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.NotFoundError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`NotFoundError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  email: 'email',
  username: 'username',
  passwordHash: 'passwordHash',
  fullName: 'fullName',
  phone: 'phone',
  permissions: 'permissions',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.RoleScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  isSystemRole: 'isSystemRole',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PermissionScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  resource: 'resource',
  action: 'action',
  createdAt: 'createdAt'
};

exports.Prisma.RolePermissionScalarFieldEnum = {
  id: 'id',
  roleId: 'roleId',
  permissionId: 'permissionId',
  createdAt: 'createdAt'
};

exports.Prisma.UserRoleScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  roleId: 'roleId',
  assignedAt: 'assignedAt',
  assignedBy: 'assignedBy'
};

exports.Prisma.PropertyScalarFieldEnum = {
  id: 'id',
  name: 'name',
  type: 'type',
  parentPropertyId: 'parentPropertyId',
  address: 'address',
  description: 'description',
  capacity: 'capacity',
  department: 'department',
  projectType: 'projectType',
  startDate: 'startDate',
  expectedEndDate: 'expectedEndDate',
  hourlyRateStandard: 'hourlyRateStandard',
  location: 'location',
  imageUrl: 'imageUrl',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PropertyServiceScalarFieldEnum = {
  id: 'id',
  propertyId: 'propertyId',
  serviceType: 'serviceType',
  status: 'status',
  lastChecked: 'lastChecked',
  notes: 'notes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.MaintenanceIssueScalarFieldEnum = {
  id: 'id',
  propertyId: 'propertyId',
  serviceType: 'serviceType',
  title: 'title',
  description: 'description',
  priority: 'priority',
  status: 'status',
  department: 'department',
  assignedTo: 'assignedTo',
  reportedBy: 'reportedBy',
  resolvedBy: 'resolvedBy',
  estimatedCost: 'estimatedCost',
  actualCost: 'actualCost',
  dueDate: 'dueDate',
  resolvedAt: 'resolvedAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PropertyMemberScalarFieldEnum = {
  id: 'id',
  propertyId: 'propertyId',
  userId: 'userId',
  role: 'role',
  position: 'position',
  department: 'department',
  hourlyRate: 'hourlyRate',
  startDate: 'startDate',
  endDate: 'endDate',
  isActive: 'isActive',
  createdAt: 'createdAt'
};

exports.Prisma.PropertyAttendanceScalarFieldEnum = {
  id: 'id',
  propertyId: 'propertyId',
  userId: 'userId',
  date: 'date',
  checkInTime: 'checkInTime',
  checkOutTime: 'checkOutTime',
  hoursWorked: 'hoursWorked',
  notes: 'notes',
  recordedBy: 'recordedBy',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.GeneratorFuelLogScalarFieldEnum = {
  id: 'id',
  propertyId: 'propertyId',
  fuelLevelLiters: 'fuelLevelLiters',
  consumptionRate: 'consumptionRate',
  runtimeHours: 'runtimeHours',
  efficiencyPercentage: 'efficiencyPercentage',
  lastMaintenance: 'lastMaintenance',
  nextMaintenance: 'nextMaintenance',
  notes: 'notes',
  recordedBy: 'recordedBy',
  recordedAt: 'recordedAt'
};

exports.Prisma.DieselAdditionScalarFieldEnum = {
  id: 'id',
  propertyId: 'propertyId',
  quantityLiters: 'quantityLiters',
  costPerLiter: 'costPerLiter',
  totalCost: 'totalCost',
  supplier: 'supplier',
  receiptNumber: 'receiptNumber',
  addedBy: 'addedBy',
  addedAt: 'addedAt'
};

exports.Prisma.SecurityGuardLogScalarFieldEnum = {
  id: 'id',
  propertyId: 'propertyId',
  guardName: 'guardName',
  shiftStart: 'shiftStart',
  shiftEnd: 'shiftEnd',
  patrolRounds: 'patrolRounds',
  incidentsReported: 'incidentsReported',
  visitorsLogged: 'visitorsLogged',
  notes: 'notes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.OttServiceScalarFieldEnum = {
  id: 'id',
  propertyId: 'propertyId',
  serviceName: 'serviceName',
  subscriptionType: 'subscriptionType',
  monthlyCost: 'monthlyCost',
  renewalDate: 'renewalDate',
  status: 'status',
  loginCredentials: 'loginCredentials',
  notes: 'notes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.UptimeReportScalarFieldEnum = {
  id: 'id',
  propertyId: 'propertyId',
  serviceType: 'serviceType',
  date: 'date',
  uptimePercentage: 'uptimePercentage',
  downtimeMinutes: 'downtimeMinutes',
  incidentsCount: 'incidentsCount',
  notes: 'notes',
  createdAt: 'createdAt'
};

exports.Prisma.ServiceStatusLogScalarFieldEnum = {
  id: 'id',
  propertyServiceId: 'propertyServiceId',
  status: 'status',
  message: 'message',
  details: 'details',
  loggedBy: 'loggedBy',
  loggedAt: 'loggedAt'
};

exports.Prisma.FunctionProcessScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  category: 'category',
  inputParameters: 'inputParameters',
  outputParameters: 'outputParameters',
  executionFrequency: 'executionFrequency',
  lastExecuted: 'lastExecuted',
  status: 'status',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.FunctionProcessLogScalarFieldEnum = {
  id: 'id',
  functionProcessId: 'functionProcessId',
  executionTime: 'executionTime',
  status: 'status',
  inputData: 'inputData',
  outputData: 'outputData',
  errorMessage: 'errorMessage',
  executionDurationMs: 'executionDurationMs',
  executedBy: 'executedBy'
};

exports.Prisma.ThresholdConfigScalarFieldEnum = {
  id: 'id',
  serviceType: 'serviceType',
  metricName: 'metricName',
  warningThreshold: 'warningThreshold',
  criticalThreshold: 'criticalThreshold',
  unit: 'unit',
  description: 'description',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AlertScalarFieldEnum = {
  id: 'id',
  propertyId: 'propertyId',
  type: 'type',
  severity: 'severity',
  title: 'title',
  message: 'message',
  isResolved: 'isResolved',
  resolvedAt: 'resolvedAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.NotificationScalarFieldEnum = {
  id: 'id',
  type: 'type',
  title: 'title',
  message: 'message',
  data: 'data',
  priority: 'priority',
  propertyId: 'propertyId',
  targetUsers: 'targetUsers',
  targetRoles: 'targetRoles',
  isRead: 'isRead',
  createdAt: 'createdAt'
};

exports.Prisma.EscalationLogScalarFieldEnum = {
  id: 'id',
  issueId: 'issueId',
  escalationLevel: 'escalationLevel',
  escalatedTo: 'escalatedTo',
  escalatedBy: 'escalatedBy',
  escalationReason: 'escalationReason',
  escalatedAt: 'escalatedAt',
  resolvedAt: 'resolvedAt',
  resolutionNotes: 'resolutionNotes'
};

exports.Prisma.MonitoringDataScalarFieldEnum = {
  id: 'id',
  propertyId: 'propertyId',
  serviceType: 'serviceType',
  metricName: 'metricName',
  value: 'value',
  unit: 'unit',
  timestamp: 'timestamp',
  createdAt: 'createdAt'
};

exports.Prisma.ThresholdAlertScalarFieldEnum = {
  id: 'id',
  propertyId: 'propertyId',
  thresholdConfigId: 'thresholdConfigId',
  serviceType: 'serviceType',
  metricName: 'metricName',
  currentValue: 'currentValue',
  thresholdValue: 'thresholdValue',
  thresholdType: 'thresholdType',
  severity: 'severity',
  message: 'message',
  isResolved: 'isResolved',
  resolvedAt: 'resolvedAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ScreenPermissionScalarFieldEnum = {
  id: 'id',
  screenName: 'screenName',
  requiredPermissions: 'requiredPermissions',
  allowedRoles: 'allowedRoles',
  isEnabled: 'isEnabled',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.WidgetPermissionScalarFieldEnum = {
  id: 'id',
  screenName: 'screenName',
  widgetName: 'widgetName',
  requiredPermissions: 'requiredPermissions',
  allowedRoles: 'allowedRoles',
  isVisible: 'isVisible',
  isEnabled: 'isEnabled',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.UserNotificationScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  notificationId: 'notificationId',
  isRead: 'isRead',
  readAt: 'readAt',
  createdAt: 'createdAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.JsonNullValueInput = {
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};
exports.PropertyType = exports.$Enums.PropertyType = {
  RESIDENTIAL: 'RESIDENTIAL',
  OFFICE: 'OFFICE',
  CONSTRUCTION_SITE: 'CONSTRUCTION_SITE'
};

exports.ServiceType = exports.$Enums.ServiceType = {
  ELECTRICITY: 'ELECTRICITY',
  WATER: 'WATER',
  INTERNET: 'INTERNET',
  SECURITY: 'SECURITY',
  OTT: 'OTT'
};

exports.ServiceStatus = exports.$Enums.ServiceStatus = {
  OPERATIONAL: 'OPERATIONAL',
  WARNING: 'WARNING',
  CRITICAL: 'CRITICAL',
  MAINTENANCE: 'MAINTENANCE'
};

exports.Priority = exports.$Enums.Priority = {
  LOW: 'LOW',
  MEDIUM: 'MEDIUM',
  HIGH: 'HIGH',
  CRITICAL: 'CRITICAL'
};

exports.MaintenanceStatus = exports.$Enums.MaintenanceStatus = {
  OPEN: 'OPEN',
  IN_PROGRESS: 'IN_PROGRESS',
  RESOLVED: 'RESOLVED',
  CLOSED: 'CLOSED'
};

exports.OttStatus = exports.$Enums.OttStatus = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
  EXPIRED: 'EXPIRED'
};

exports.FunctionProcessStatus = exports.$Enums.FunctionProcessStatus = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
  MAINTENANCE: 'MAINTENANCE'
};

exports.FunctionLogStatus = exports.$Enums.FunctionLogStatus = {
  SUCCESS: 'SUCCESS',
  FAILURE: 'FAILURE',
  WARNING: 'WARNING'
};

exports.Prisma.ModelName = {
  User: 'User',
  Role: 'Role',
  Permission: 'Permission',
  RolePermission: 'RolePermission',
  UserRole: 'UserRole',
  Property: 'Property',
  PropertyService: 'PropertyService',
  MaintenanceIssue: 'MaintenanceIssue',
  PropertyMember: 'PropertyMember',
  PropertyAttendance: 'PropertyAttendance',
  GeneratorFuelLog: 'GeneratorFuelLog',
  DieselAddition: 'DieselAddition',
  SecurityGuardLog: 'SecurityGuardLog',
  OttService: 'OttService',
  UptimeReport: 'UptimeReport',
  ServiceStatusLog: 'ServiceStatusLog',
  FunctionProcess: 'FunctionProcess',
  FunctionProcessLog: 'FunctionProcessLog',
  ThresholdConfig: 'ThresholdConfig',
  Alert: 'Alert',
  Notification: 'Notification',
  EscalationLog: 'EscalationLog',
  MonitoringData: 'MonitoringData',
  ThresholdAlert: 'ThresholdAlert',
  ScreenPermission: 'ScreenPermission',
  WidgetPermission: 'WidgetPermission',
  UserNotification: 'UserNotification'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }
        
        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
