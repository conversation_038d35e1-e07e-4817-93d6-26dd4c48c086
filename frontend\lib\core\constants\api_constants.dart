class ApiConstants {
  // Base URLs
  static const String baseUrlDev = 'http://192.168.1.3:3001';
  static const String baseUrlProd = 'https://your-vercel-app.vercel.app';

  // Current base URL (defaults to dev)
  static const String baseUrl = baseUrlDev;

  // Authentication Endpoints
  static const String login = '/api/auth/login';
  static const String register = '/api/auth/register';
  static const String profile = '/api/auth/me';

  // Property Management Endpoints
  static const String properties = '/api/properties';
  static String propertyById(String id) => '/api/properties/$id';

  // Maintenance Endpoints
  static const String maintenance = '/api/maintenance';
  static const String maintenanceIssues = '/api/maintenance'; // Alias for compatibility
  static String maintenanceById(String id) => '/api/maintenance/$id';

  // User Management Endpoints
  static const String users = '/api/users';
  static String userById(String id) => '/api/users/$id';

  // Unified Property Management Endpoints (Consolidated)
  static String propertyMembers(String id) => '/api/properties/$id/members';
  static String propertyAttendance(String id) => '/api/properties/$id/attendance';

  // Attendance Endpoints
  static const String attendance = '/api/attendance';
  static String attendanceById(String id) => '/api/attendance/$id';
  static const String attendanceReportsSummary = '/api/attendance/reports/summary';
  static String attendanceReportsUser(String userId) => '/api/attendance/reports/user/$userId';

  // Generator Fuel Endpoints
  static String generatorFuel(String propertyId) => '/api/generator-fuel/$propertyId';
  static String generatorFuelById(String id) => '/api/generator-fuel/$id';

  // Additional Service Endpoints
  static String ottServices(String propertyId) => '/api/ott-services/$propertyId';
  static String uptimeReports(String propertyId) => '/api/uptime-reports/$propertyId';
  static String dieselAdditions(String propertyId) => '/api/diesel-additions/$propertyId';

  // Dashboard Endpoints
  static const String dashboardStatus = '/api/dashboard/status';

  // Admin Endpoints
  static const String adminWidgets = '/admin/widgets';
  static const String adminWidgetTypes = '/admin/widget-types';
  static String adminWidgetById(String id) => '/admin/widgets/$id';

  // Headers
  static const String contentType = 'application/json';
  static const String authorization = 'Authorization';
  static const String bearer = 'Bearer';

  // Timeouts
  static const int connectTimeout = 30000; // 30 seconds
  static const int receiveTimeout = 30000; // 30 seconds
  static const int sendTimeout = 30000; // 30 seconds
}
