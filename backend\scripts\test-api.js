const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api';
let authToken = '';

// Test configuration
const testConfig = {
  email: '<EMAIL>',
  password: 'admin123',
  propertyId: null, // Will be set after getting properties
};

async function makeRequest(method, endpoint, data = null, headers = {}) {
  try {
    const config = {
      method,
      url: `${BASE_URL}${endpoint}`,
      headers: {
        'Content-Type': 'application/json',
        ...headers,
      },
    };

    if (authToken) {
      config.headers.Authorization = `Bearer ${authToken}`;
    }

    if (data) {
      config.data = data;
    }

    const response = await axios(config);
    return { success: true, data: response.data, status: response.status };
  } catch (error) {
    return {
      success: false,
      error: error.response?.data || error.message,
      status: error.response?.status || 500,
    };
  }
}

async function testLogin() {
  console.log('🔐 Testing multi-field login...');
  
  // Test email login
  const emailLogin = await makeRequest('POST', '/auth/login', {
    identifier: testConfig.email,
    password: testConfig.password,
    login_type: 'email',
  });

  if (emailLogin.success) {
    authToken = emailLogin.data.token;
    console.log('✅ Email login successful');
    console.log(`   User: ${emailLogin.data.user.full_name}`);
    console.log(`   Roles: ${emailLogin.data.user.roles.join(', ')}`);
    console.log(`   Permissions: ${emailLogin.data.user.permissions.length} explicit permissions`);
    return true;
  } else {
    console.log('❌ Email login failed:', emailLogin.error);
    return false;
  }
}

async function testPermissionEndpoints() {
  console.log('\n🛡️ Testing permission configuration endpoints...');

  // Test get screen permissions
  const screenPermissions = await makeRequest('GET', '/permissions/screens');
  if (screenPermissions.success) {
    console.log(`✅ Retrieved ${screenPermissions.data.data.count} screen permissions`);
  } else {
    console.log('❌ Failed to get screen permissions:', screenPermissions.error);
  }

  // Test get widget permissions
  const widgetPermissions = await makeRequest('GET', '/permissions/widgets');
  if (widgetPermissions.success) {
    console.log(`✅ Retrieved ${widgetPermissions.data.data.count} widget permissions`);
  } else {
    console.log('❌ Failed to get widget permissions:', widgetPermissions.error);
  }

  // Test get specific screen permission
  const dashboardPermission = await makeRequest('GET', '/permissions/screens/dashboard');
  if (dashboardPermission.success) {
    console.log('✅ Retrieved dashboard screen permission');
  } else {
    console.log('❌ Failed to get dashboard permission:', dashboardPermission.error);
  }
}

async function testMonitoringEndpoints() {
  console.log('\n📊 Testing monitoring endpoints...');

  // Get properties first
  const properties = await makeRequest('GET', '/properties');
  if (properties.success && properties.data.data.length > 0) {
    testConfig.propertyId = properties.data.data[0].id;
    console.log(`✅ Using property: ${properties.data.data[0].name}`);
  } else {
    console.log('❌ No properties found for testing');
    return;
  }

  // Test single metric monitoring
  const singleMetric = await makeRequest('POST', '/monitoring', {
    property_id: testConfig.propertyId,
    service_type: 'fuel',
    metric_name: 'fuel_level',
    value: 45.5,
    unit: 'liters',
  });

  if (singleMetric.success) {
    console.log('✅ Single metric monitoring successful');
    if (singleMetric.data.data.alerts) {
      console.log(`   Generated ${singleMetric.data.data.alerts.length} alerts`);
    }
  } else {
    console.log('❌ Single metric monitoring failed:', singleMetric.error);
  }

  // Test multiple metrics monitoring
  const multipleMetrics = await makeRequest('POST', '/monitoring/multiple', {
    metrics: [
      {
        property_id: testConfig.propertyId,
        service_type: 'fuel',
        metric_name: 'fuel_percentage',
        value: 15.0,
        unit: '%',
      },
      {
        property_id: testConfig.propertyId,
        service_type: 'attendance',
        metric_name: 'attendance_percentage',
        value: 75.0,
        unit: '%',
      },
    ],
  });

  if (multipleMetrics.success) {
    console.log('✅ Multiple metrics monitoring successful');
    console.log(`   Monitored ${multipleMetrics.data.data.metrics_count} metrics`);
    console.log(`   Generated ${multipleMetrics.data.data.alerts_generated} alerts`);
  } else {
    console.log('❌ Multiple metrics monitoring failed:', multipleMetrics.error);
  }

  // Test get active alerts
  const activeAlerts = await makeRequest('GET', `/monitoring?property_id=${testConfig.propertyId}`);
  if (activeAlerts.success) {
    console.log(`✅ Retrieved ${activeAlerts.data.data.count} active alerts`);
  } else {
    console.log('❌ Failed to get active alerts:', activeAlerts.error);
  }
}

async function testNotificationEndpoints() {
  console.log('\n🔔 Testing notification endpoints...');

  // Test get notifications
  const notifications = await makeRequest('GET', '/notifications?unread_only=true');
  if (notifications.success) {
    console.log(`✅ Retrieved ${notifications.data.data.count} unread notifications`);
  } else {
    console.log('❌ Failed to get notifications:', notifications.error);
  }
}

async function testSSEConnection() {
  console.log('\n📡 Testing SSE connection...');
  
  // Note: This is a basic test - in a real scenario, you'd use EventSource
  const sseTest = await makeRequest('GET', '/notifications/sse');
  if (sseTest.status === 200) {
    console.log('✅ SSE endpoint is accessible');
  } else {
    console.log('❌ SSE endpoint failed:', sseTest.error);
  }
}

async function runAllTests() {
  console.log('🧪 Starting API endpoint tests...\n');

  try {
    // Test login first
    const loginSuccess = await testLogin();
    if (!loginSuccess) {
      console.log('❌ Cannot proceed without authentication');
      return;
    }

    // Test all endpoints
    await testPermissionEndpoints();
    await testMonitoringEndpoints();
    await testNotificationEndpoints();
    await testSSEConnection();

    console.log('\n🎉 All API tests completed!');
    console.log('\n📋 Test Summary:');
    console.log('  ✅ Multi-field login');
    console.log('  ✅ Permission configuration endpoints');
    console.log('  ✅ Monitoring endpoints');
    console.log('  ✅ Notification endpoints');
    console.log('  ✅ SSE endpoint accessibility');

  } catch (error) {
    console.error('💥 Test execution failed:', error);
  }
}

// Run tests if called directly
if (require.main === module) {
  runAllTests()
    .then(() => {
      console.log('\n✨ Testing completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Testing failed:', error);
      process.exit(1);
    });
}

module.exports = { runAllTests };
