import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:reactive_forms/reactive_forms.dart';
import '../../../../shared/models/generator_fuel.dart';
import '../providers/fuel_providers.dart';

class AddFuelLogDialog extends ConsumerStatefulWidget {
  final String propertyId;
  final VoidCallback? onSuccess;

  const AddFuelLogDialog({
    super.key,
    required this.propertyId,
    this.onSuccess,
  });

  @override
  ConsumerState<AddFuelLogDialog> createState() => _AddFuelLogDialogState();
}

class _AddFuelLogDialogState extends ConsumerState<AddFuelLogDialog> {
  late FormGroup form;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeForm();
  }

  void _initializeForm() {
    form = FormGroup({
      'currentLevel': FormControl<double>(
        value: 0.0,
        validators: [Validators.required, Validators.min(0)],
      ),
      'fuelAdded': FormControl<double>(
        value: 0.0,
        validators: [Validators.required, Validators.min(0)],
      ),
      'fuelConsumed': FormControl<double>(
        value: 0.0,
        validators: [Validators.required, Validators.min(0)],
      ),
      'hoursRun': FormControl<double>(
        value: 0.0,
        validators: [Validators.required, Validators.min(0)],
      ),
      'notes': FormControl<String>(value: ''),
    });
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        padding: const EdgeInsets.all(16),
        child: ReactiveForm(
          formGroup: form,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Add Fuel Log',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              ReactiveTextField<double>(
                formControlName: 'currentLevel',
                decoration: const InputDecoration(
                  labelText: 'Current Fuel Level (L)',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                validationMessages: {
                  ValidationMessage.required: (_) => 'Current level is required',
                  ValidationMessage.min: (_) => 'Must be greater than or equal to 0',
                },
              ),
              const SizedBox(height: 16),
              ReactiveTextField<double>(
                formControlName: 'fuelAdded',
                decoration: const InputDecoration(
                  labelText: 'Fuel Added (L)',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                validationMessages: {
                  ValidationMessage.required: (_) => 'Fuel added is required',
                  ValidationMessage.min: (_) => 'Must be greater than or equal to 0',
                },
              ),
              const SizedBox(height: 16),
              ReactiveTextField<double>(
                formControlName: 'fuelConsumed',
                decoration: const InputDecoration(
                  labelText: 'Fuel Consumed (L)',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                validationMessages: {
                  ValidationMessage.required: (_) => 'Fuel consumed is required',
                  ValidationMessage.min: (_) => 'Must be greater than or equal to 0',
                },
              ),
              const SizedBox(height: 16),
              ReactiveTextField<double>(
                formControlName: 'hoursRun',
                decoration: const InputDecoration(
                  labelText: 'Hours Run',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                validationMessages: {
                  ValidationMessage.required: (_) => 'Hours run is required',
                  ValidationMessage.min: (_) => 'Must be greater than or equal to 0',
                },
              ),
              const SizedBox(height: 16),
              ReactiveTextField<String>(
                formControlName: 'notes',
                decoration: const InputDecoration(
                  labelText: 'Notes (Optional)',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 24),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('Cancel'),
                  ),
                  const SizedBox(width: 8),
                  ReactiveFormConsumer(
                    builder: (context, form, child) {
                      return ElevatedButton(
                        onPressed: form.valid && !_isLoading ? _saveFuelLog : null,
                        child: _isLoading
                            ? const SizedBox(
                                width: 16,
                                height: 16,
                                child: CircularProgressIndicator(strokeWidth: 2),
                              )
                            : const Text('Save'),
                      );
                    },
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _saveFuelLog() async {
    if (!form.valid) return;

    setState(() => _isLoading = true);

    try {
      final formValue = form.value;

      final fuelLog = GeneratorFuelLog(
        id: '',
        propertyId: widget.propertyId,
        fuelLevelLiters: formValue['currentLevel'] as double,
        consumptionRate: formValue['fuelConsumed'] as double,
        runtimeHours: formValue['hoursRun'] as double,
        notes: formValue['notes'] as String? ?? '',
        recordedAt: DateTime.now(),
      );

      await ref.read(fuelLogsProvider.notifier).createFuelLog(fuelLog);

      widget.onSuccess?.call();
      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Fuel log saved successfully')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error saving fuel log: $e')),
        );
      }
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }

  @override
  void dispose() {
    form.dispose();
    super.dispose();
  }
}
