// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ThresholdConfig _$ThresholdConfigFromJson(Map<String, dynamic> json) =>
    ThresholdConfig(
      id: json['id'] as String,
      serviceType: json['service_type'] as String,
      metricName: json['metric_name'] as String,
      warningThreshold: _doubleFromJson(json['warning_threshold']),
      criticalThreshold: _doubleFromJson(json['critical_threshold']),
      unit: json['unit'] as String,
      description: json['description'] as String,
      isActive: json['is_active'] as bool,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$ThresholdConfigToJson(ThresholdConfig instance) =>
    <String, dynamic>{
      'id': instance.id,
      'service_type': instance.serviceType,
      'metric_name': instance.metricName,
      'warning_threshold': instance.warningThreshold,
      'critical_threshold': instance.criticalThreshold,
      'unit': instance.unit,
      'description': instance.description,
      'is_active': instance.isActive,
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt.toIso8601String(),
    };

CreateThresholdRequest _$CreateThresholdRequestFromJson(
        Map<String, dynamic> json) =>
    CreateThresholdRequest(
      serviceType: json['service_type'] as String,
      metricName: json['metric_name'] as String,
      warningThreshold: (json['warning_threshold'] as num).toDouble(),
      criticalThreshold: (json['critical_threshold'] as num).toDouble(),
      unit: json['unit'] as String,
      description: json['description'] as String,
      isActive: json['is_active'] as bool?,
    );

Map<String, dynamic> _$CreateThresholdRequestToJson(
        CreateThresholdRequest instance) =>
    <String, dynamic>{
      'service_type': instance.serviceType,
      'metric_name': instance.metricName,
      'warning_threshold': instance.warningThreshold,
      'critical_threshold': instance.criticalThreshold,
      'unit': instance.unit,
      'description': instance.description,
      'is_active': instance.isActive,
    };

UpdateThresholdRequest _$UpdateThresholdRequestFromJson(
        Map<String, dynamic> json) =>
    UpdateThresholdRequest(
      serviceType: json['service_type'] as String?,
      metricName: json['metric_name'] as String?,
      warningThreshold: (json['warning_threshold'] as num?)?.toDouble(),
      criticalThreshold: (json['critical_threshold'] as num?)?.toDouble(),
      unit: json['unit'] as String?,
      description: json['description'] as String?,
      isActive: json['is_active'] as bool?,
    );

Map<String, dynamic> _$UpdateThresholdRequestToJson(
        UpdateThresholdRequest instance) =>
    <String, dynamic>{
      'service_type': instance.serviceType,
      'metric_name': instance.metricName,
      'warning_threshold': instance.warningThreshold,
      'critical_threshold': instance.criticalThreshold,
      'unit': instance.unit,
      'description': instance.description,
      'is_active': instance.isActive,
    };

BulkCreateThresholdsRequest _$BulkCreateThresholdsRequestFromJson(
        Map<String, dynamic> json) =>
    BulkCreateThresholdsRequest(
      thresholds: (json['thresholds'] as List<dynamic>)
          .map(
              (e) => CreateThresholdRequest.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$BulkCreateThresholdsRequestToJson(
        BulkCreateThresholdsRequest instance) =>
    <String, dynamic>{
      'thresholds': instance.thresholds,
    };

BulkUpdateThresholdsRequest _$BulkUpdateThresholdsRequestFromJson(
        Map<String, dynamic> json) =>
    BulkUpdateThresholdsRequest(
      thresholds: (json['thresholds'] as List<dynamic>)
          .map((e) => ThresholdUpdateItem.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$BulkUpdateThresholdsRequestToJson(
        BulkUpdateThresholdsRequest instance) =>
    <String, dynamic>{
      'thresholds': instance.thresholds,
    };

ThresholdUpdateItem _$ThresholdUpdateItemFromJson(Map<String, dynamic> json) =>
    ThresholdUpdateItem(
      id: json['id'] as String,
      data:
          UpdateThresholdRequest.fromJson(json['data'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$ThresholdUpdateItemToJson(
        ThresholdUpdateItem instance) =>
    <String, dynamic>{
      'id': instance.id,
      'data': instance.data,
    };

VoidResponse _$VoidResponseFromJson(Map<String, dynamic> json) => VoidResponse(
      message: json['message'] as String,
    );

Map<String, dynamic> _$VoidResponseToJson(VoidResponse instance) =>
    <String, dynamic>{
      'message': instance.message,
    };
