import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../shared/models/attendance.dart';
import '../../../../shared/models/user.dart';
import '../providers/attendance_providers.dart';

class BulkAttendanceDialog extends ConsumerStatefulWidget {
  final String propertyId;
  final DateTime selectedDate;
  final VoidCallback? onSuccess;

  const BulkAttendanceDialog({
    super.key,
    required this.propertyId,
    required this.selectedDate,
    this.onSuccess,
  });

  @override
  ConsumerState<BulkAttendanceDialog> createState() => _BulkAttendanceDialogState();
}

class _BulkAttendanceDialogState extends ConsumerState<BulkAttendanceDialog> {
  final Map<String, AttendanceStatus> _attendanceMap = {};
  bool _isLoading = false;
  List<User> _users = [];

  @override
  void initState() {
    super.initState();
    _loadUsers();
  }

  Future<void> _loadUsers() async {
    // Load users for the property
    // This would typically come from a user provider
    setState(() {
      _users = []; // Placeholder - implement actual user loading
    });
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Bulk Attendance',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              'Date: ${widget.selectedDate.toString().split(' ')[0]}',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => _markAllAs(AttendanceStatus.present),
                    child: const Text('Mark All Present'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => _markAllAs(AttendanceStatus.absent),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Mark All Absent'),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Expanded(
              child: _users.isEmpty
                  ? const Center(child: Text('No users found'))
                  : ListView.builder(
                      itemCount: _users.length,
                      itemBuilder: (context, index) {
                        final user = _users[index];
                        return _buildUserAttendanceItem(user);
                      },
                    ),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Cancel'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _isLoading ? null : _saveAttendance,
                  child: _isLoading
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Text('Save'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUserAttendanceItem(User user) {
    final status = _attendanceMap[user.id] ?? AttendanceStatus.absent;

    return Card(
      child: ListTile(
        leading: CircleAvatar(
          child: Text(user.name.substring(0, 1).toUpperCase()),
        ),
        title: Text(user.name),
        subtitle: Text(user.email),
        trailing: DropdownButton<AttendanceStatus>(
          value: status,
          onChanged: (newStatus) {
            if (newStatus != null) {
              setState(() {
                _attendanceMap[user.id] = newStatus;
              });
            }
          },
          items: AttendanceStatus.values.map((status) {
            return DropdownMenuItem(
              value: status,
              child: Text(_getStatusLabel(status)),
            );
          }).toList(),
        ),
      ),
    );
  }

  void _markAllAs(AttendanceStatus status) {
    setState(() {
      for (final user in _users) {
        _attendanceMap[user.id] = status;
      }
    });
  }

  Future<void> _saveAttendance() async {
    setState(() => _isLoading = true);

    try {
      for (final entry in _attendanceMap.entries) {
        final userId = entry.key;
        final status = entry.value;

        // Create attendance record
        final record = AttendanceRecord(
          id: '',
          userId: userId,
          propertyId: widget.propertyId,
          workerName: _users.firstWhere((u) => u.id == userId).email, // Use email as fallback
          date: widget.selectedDate,
          status: status.value,
          checkInTime: status == AttendanceStatus.present ? DateTime.now().toString() : null,
          checkOutTime: null,
          notes: 'Bulk attendance entry',
          createdAt: DateTime.now(),
        );

        // Save through provider
        await ref.read(attendanceRecordsProvider.notifier).createAttendanceRecord(record);
      }

      widget.onSuccess?.call();
      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Bulk attendance saved successfully')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error saving attendance: $e')),
        );
      }
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }

  String _getStatusLabel(AttendanceStatus status) {
    return status.displayName;
  }
}
