import { NextRequest } from 'next/server';
import { getAuthUser } from '@/lib/auth';
import { NotificationService } from '@/lib/monitoring/notification-service';

const notificationService = new NotificationService();

export async function GET(request: NextRequest) {
  try {
    // Verify authentication
    const user = await getAuthUser(request);
    if (!user) {
      return new Response('Unauthorized', { status: 401 });
    }

    // Create SSE stream
    const stream = new ReadableStream({
      start(controller) {
        // Send initial connection message
        const initialMessage = `data: ${JSON.stringify({
          type: 'connection',
          message: 'Connected to notification stream',
          timestamp: new Date().toISOString(),
        })}\n\n`;
        
        controller.enqueue(new TextEncoder().encode(initialMessage));

        // Add user to SSE connections
        notificationService.addSSEConnection(user.id, controller);

        // Send heartbeat every 30 seconds
        const heartbeatInterval = setInterval(() => {
          try {
            const heartbeat = `data: ${JSON.stringify({
              type: 'heartbeat',
              timestamp: new Date().toISOString(),
            })}\n\n`;
            
            controller.enqueue(new TextEncoder().encode(heartbeat));
          } catch (error) {
            console.error('Error sending heartbeat:', error);
            clearInterval(heartbeatInterval);
            notificationService.removeSSEConnection(user.id);
          }
        }, 30000);

        // Handle connection close
        request.signal.addEventListener('abort', () => {
          clearInterval(heartbeatInterval);
          notificationService.removeSSEConnection(user.id);
          try {
            controller.close();
          } catch (error) {
            // Connection might already be closed
          }
        });
      },
    });

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET',
        'Access-Control-Allow-Headers': 'Authorization, Content-Type',
      },
    });
  } catch (error) {
    console.error('SSE connection error:', error);
    return new Response('Internal Server Error', { status: 500 });
  }
}

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET',
      'Access-Control-Allow-Headers': 'Authorization, Content-Type',
    },
  });
}
