import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth, requireRole } from '@/lib/auth';
import { createApiResponse, getQueryParams, getRequestBody, handleError, corsHeaders } from '@/lib/utils';
import { validateRequest } from '@/lib/validation';
import Jo<PERSON> from 'joi';

const createFunctionProcessLogSchema = Joi.object({
  status: Joi.string().valid('SUCCESS', 'FAILURE', 'TIMEOUT', 'ERROR').required(),
  input_data: Joi.object().optional(),
  output_data: Joi.object().optional(),
  execution_duration_ms: Joi.number().integer().min(0).optional(),
  error_message: Joi.string().optional(),
});

async function getFunctionProcessLogsHandler(
  request: NextRequest,
  context: { params: { id: string } },
  currentUser: any
) {
  try {
    const { id } = context.params;
    const params = getQueryParams(request);
    const { status, limit = '50', offset = '0' } = params;

    // Verify function process exists
    const functionProcess = await prisma.functionProcess.findUnique({
      where: { id },
    });

    if (!functionProcess) {
      return Response.json(
        createApiResponse(null, 'Function process not found', 'NOT_FOUND'),
        { status: 404 }
      );
    }

    // Build where clause
    const where: any = {
      functionProcessId: id,
    };
    
    if (status) {
      where.status = status.toUpperCase();
    }

    // Get function process logs
    const logs = await prisma.functionProcessLog.findMany({
      where,
      orderBy: {
        createdAt: 'desc',
      },
      take: parseInt(limit),
      skip: parseInt(offset),
    });

    // Get total count for pagination
    const totalCount = await prisma.functionProcessLog.count({ where });

    // Transform data to match API response format
    const transformedLogs = logs.map(log => ({
      id: log.id,
      function_process_id: log.functionProcessId,
      status: log.status,
      input_data: log.inputData,
      output_data: log.outputData,
      execution_duration_ms: log.executionDurationMs,
      error_message: log.errorMessage,
      created_at: log.createdAt,
    }));

    return Response.json(
      createApiResponse({
        logs: transformedLogs,
        pagination: {
          total: totalCount,
          limit: parseInt(limit),
          offset: parseInt(offset),
          has_more: totalCount > parseInt(offset) + parseInt(limit),
        },
        function_process: {
          id: functionProcess.id,
          name: functionProcess.name,
          category: functionProcess.category,
        },
      }),
      { 
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to fetch function process logs');
  }
}

async function createFunctionProcessLogHandler(
  request: NextRequest,
  context: { params: { id: string } },
  currentUser: any
) {
  try {
    const { id } = context.params;
    const body = await getRequestBody(request);
    
    // Validate request body
    const validation = validateRequest(createFunctionProcessLogSchema, body);
    if (!validation.isValid) {
      return Response.json(
        createApiResponse(null, 'Validation failed', 'VALIDATION_ERROR'),
        { status: 400 }
      );
    }

    // Verify function process exists
    const functionProcess = await prisma.functionProcess.findUnique({
      where: { id },
    });

    if (!functionProcess) {
      return Response.json(
        createApiResponse(null, 'Function process not found', 'NOT_FOUND'),
        { status: 404 }
      );
    }

    const { 
      status, 
      input_data, 
      output_data, 
      execution_duration_ms, 
      error_message 
    } = validation.data;

    // Create function process log
    const log = await prisma.functionProcessLog.create({
      data: {
        functionProcessId: id,
        status,
        inputData: input_data || {},
        outputData: output_data || {},
        executionDurationMs: execution_duration_ms,
        errorMessage: error_message,
      },
    });

    return Response.json(
      createApiResponse({
        message: 'Function process log created successfully',
        log: {
          id: log.id,
          function_process_id: log.functionProcessId,
          status: log.status,
          input_data: log.inputData,
          output_data: log.outputData,
          execution_duration_ms: log.executionDurationMs,
          error_message: log.errorMessage,
          created_at: log.createdAt,
        },
      }),
      { 
        status: 201,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to create function process log');
  }
}

export const GET = requireAuth(getFunctionProcessLogsHandler);
export const POST = requireRole(['admin', 'system'])(createFunctionProcessLogHandler);

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: corsHeaders(),
  });
}
