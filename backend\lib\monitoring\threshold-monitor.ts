import { prisma } from '../prisma';
import { NotificationService } from './notification-service';

export interface ThresholdAlert {
  id: string;
  propertyId: string;
  propertyName: string;
  serviceType: string;
  metricName: string;
  currentValue: number;
  thresholdType: 'warning' | 'critical';
  thresholdValue: number;
  message: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  timestamp: Date;
  isResolved: boolean;
}

export interface MonitoringData {
  propertyId: string;
  serviceType: string;
  metricName: string;
  value: number;
  unit?: string;
  timestamp?: Date;
}

export class ThresholdMonitor {
  private notificationService: NotificationService;
  private activeAlerts: Map<string, ThresholdAlert> = new Map();

  constructor() {
    this.notificationService = new NotificationService();
  }

  /**
   * Monitor a single metric value against thresholds
   */
  async monitorMetric(data: MonitoringData): Promise<ThresholdAlert[]> {
    const alerts: ThresholdAlert[] = [];

    try {
      // Get threshold configuration for this metric
      const thresholds = await prisma.thresholdConfig.findMany({
        where: {
          serviceType: data.serviceType,
          metricName: data.metricName,
          isActive: true,
        },
      });

      if (thresholds.length === 0) {
        console.log(`No thresholds configured for ${data.serviceType}.${data.metricName}`);
        return alerts;
      }

      // Get property information
      const property = await prisma.property.findUnique({
        where: { id: data.propertyId },
        select: { id: true, name: true, type: true },
      });

      if (!property) {
        console.error(`Property not found: ${data.propertyId}`);
        return alerts;
      }

      // Check each threshold
      for (const threshold of thresholds) {
        const alertKey = `${data.propertyId}-${data.serviceType}-${data.metricName}`;
        const existingAlert = this.activeAlerts.get(alertKey);

        // Check critical threshold first
        if (threshold.criticalThreshold && this.isThresholdBreached(data.value, threshold.criticalThreshold, data.metricName)) {
          const alert = await this.createAlert({
            propertyId: data.propertyId,
            propertyName: property.name,
            serviceType: data.serviceType,
            metricName: data.metricName,
            currentValue: data.value,
            thresholdType: 'critical',
            thresholdValue: threshold.criticalThreshold.toNumber(),
            unit: data.unit || threshold.unit,
            timestamp: data.timestamp || new Date(),
          });

          alerts.push(alert);
          this.activeAlerts.set(alertKey, alert);

          // Send notification for critical alerts
          await this.notificationService.sendThresholdAlert(alert);
        }
        // Check warning threshold if no critical alert
        else if (threshold.warningThreshold && this.isThresholdBreached(data.value, threshold.warningThreshold, data.metricName)) {
          // Only create warning alert if no critical alert exists
          if (!existingAlert || existingAlert.thresholdType !== 'critical') {
            const alert = await this.createAlert({
              propertyId: data.propertyId,
              propertyName: property.name,
              serviceType: data.serviceType,
              metricName: data.metricName,
              currentValue: data.value,
              thresholdType: 'warning',
              thresholdValue: threshold.warningThreshold.toNumber(),
              unit: data.unit || threshold.unit,
              timestamp: data.timestamp || new Date(),
            });

            alerts.push(alert);
            this.activeAlerts.set(alertKey, alert);

            // Send notification for warning alerts
            await this.notificationService.sendThresholdAlert(alert);
          }
        }
        // Value is within normal range
        else {
          // Resolve existing alert if value is back to normal
          if (existingAlert && !existingAlert.isResolved) {
            await this.resolveAlert(existingAlert);
            this.activeAlerts.delete(alertKey);
          }
        }
      }
    } catch (error) {
      console.error('Error monitoring metric:', error);
    }

    return alerts;
  }

  /**
   * Monitor multiple metrics at once
   */
  async monitorMetrics(dataPoints: MonitoringData[]): Promise<ThresholdAlert[]> {
    const allAlerts: ThresholdAlert[] = [];

    for (const data of dataPoints) {
      const alerts = await this.monitorMetric(data);
      allAlerts.push(...alerts);
    }

    return allAlerts;
  }

  /**
   * Check if a threshold is breached based on metric type
   */
  private isThresholdBreached(currentValue: number, thresholdValue: any, metricName: string): boolean {
    const threshold = typeof thresholdValue === 'number' ? thresholdValue : thresholdValue.toNumber();

    // For metrics where lower values are bad (fuel level, attendance percentage)
    const lowerIsBad = [
      'fuel_level',
      'fuel_percentage',
      'attendance_percentage',
      'backup_hours',
      'battery_level',
      'efficiency_percentage'
    ];

    // For metrics where higher values are bad (open issues, response time)
    const higherIsBad = [
      'open_issues',
      'pending_issues',
      'overdue_issues',
      'response_time',
      'downtime_hours',
      'failure_rate'
    ];

    if (lowerIsBad.some(metric => metricName.includes(metric))) {
      return currentValue < threshold;
    } else if (higherIsBad.some(metric => metricName.includes(metric))) {
      return currentValue > threshold;
    }

    // Default: assume lower values are bad
    return currentValue < threshold;
  }

  /**
   * Create a new threshold alert
   */
  private async createAlert(params: {
    propertyId: string;
    propertyName: string;
    serviceType: string;
    metricName: string;
    currentValue: number;
    thresholdType: 'warning' | 'critical';
    thresholdValue: number;
    unit?: string;
    timestamp: Date;
  }): Promise<ThresholdAlert> {
    const alertId = `${params.propertyId}-${params.serviceType}-${params.metricName}-${Date.now()}`;
    
    const message = this.generateAlertMessage(params);
    const severity = this.determineSeverity(params.thresholdType, params.metricName);

    const alert: ThresholdAlert = {
      id: alertId,
      propertyId: params.propertyId,
      propertyName: params.propertyName,
      serviceType: params.serviceType,
      metricName: params.metricName,
      currentValue: params.currentValue,
      thresholdType: params.thresholdType,
      thresholdValue: params.thresholdValue,
      message,
      severity,
      timestamp: params.timestamp,
      isResolved: false,
    };

    // Store alert in database
    try {
      await prisma.alert.create({
        data: {
          id: alertId,
          propertyId: params.propertyId,
          type: params.serviceType,
          severity: severity.toUpperCase(),
          title: `${params.thresholdType.toUpperCase()}: ${params.metricName}`,
          message,
          isResolved: false,
          createdAt: params.timestamp,
        },
      });
    } catch (error) {
      console.error('Error storing alert in database:', error);
    }

    return alert;
  }

  /**
   * Generate human-readable alert message
   */
  private generateAlertMessage(params: {
    propertyName: string;
    serviceType: string;
    metricName: string;
    currentValue: number;
    thresholdType: 'warning' | 'critical';
    thresholdValue: number;
    unit?: string;
  }): string {
    const unit = params.unit || '';
    const value = params.currentValue.toFixed(1);
    const threshold = params.thresholdValue.toFixed(1);

    const metricDisplayName = this.getMetricDisplayName(params.metricName);
    const serviceDisplayName = this.getServiceDisplayName(params.serviceType);

    if (params.thresholdType === 'critical') {
      return `CRITICAL: ${metricDisplayName} at ${params.propertyName} is ${value}${unit}, below critical threshold of ${threshold}${unit}. Immediate attention required.`;
    } else {
      return `WARNING: ${metricDisplayName} at ${params.propertyName} is ${value}${unit}, below warning threshold of ${threshold}${unit}. Please monitor closely.`;
    }
  }

  /**
   * Determine alert severity
   */
  private determineSeverity(thresholdType: 'warning' | 'critical', metricName: string): 'low' | 'medium' | 'high' | 'critical' {
    if (thresholdType === 'critical') {
      // Critical fuel or safety issues
      if (metricName.includes('fuel') || metricName.includes('safety') || metricName.includes('security')) {
        return 'critical';
      }
      return 'high';
    } else {
      // Warning level
      if (metricName.includes('fuel') || metricName.includes('attendance')) {
        return 'medium';
      }
      return 'low';
    }
  }

  /**
   * Resolve an existing alert
   */
  private async resolveAlert(alert: ThresholdAlert): Promise<void> {
    try {
      alert.isResolved = true;

      // Update in database
      await prisma.alert.update({
        where: { id: alert.id },
        data: {
          isResolved: true,
          resolvedAt: new Date(),
        },
      });

      // Send resolution notification
      await this.notificationService.sendAlertResolution(alert);
    } catch (error) {
      console.error('Error resolving alert:', error);
    }
  }

  /**
   * Get active alerts for a property
   */
  async getActiveAlerts(propertyId?: string): Promise<ThresholdAlert[]> {
    const alerts = Array.from(this.activeAlerts.values());
    
    if (propertyId) {
      return alerts.filter(alert => alert.propertyId === propertyId && !alert.isResolved);
    }
    
    return alerts.filter(alert => !alert.isResolved);
  }

  /**
   * Get metric display name
   */
  private getMetricDisplayName(metricName: string): string {
    const displayNames: Record<string, string> = {
      'fuel_level': 'Fuel Level',
      'fuel_percentage': 'Fuel Percentage',
      'backup_hours': 'Backup Hours',
      'attendance_percentage': 'Attendance Rate',
      'open_issues': 'Open Issues',
      'response_time': 'Response Time',
      'efficiency_percentage': 'Efficiency',
    };

    return displayNames[metricName] || metricName.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  }

  /**
   * Get service display name
   */
  private getServiceDisplayName(serviceType: string): string {
    const displayNames: Record<string, string> = {
      'fuel': 'Generator Fuel',
      'maintenance': 'Maintenance',
      'attendance': 'Staff Attendance',
      'security': 'Security',
      'electricity': 'Electrical System',
    };

    return displayNames[serviceType] || serviceType.replace(/\b\w/g, l => l.toUpperCase());
  }
}
