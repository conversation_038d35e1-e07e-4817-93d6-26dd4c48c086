// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'attendance_api_service.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SubmitPropertyAttendanceRequest _$SubmitPropertyAttendanceRequestFromJson(
        Map<String, dynamic> json) =>
    SubmitPropertyAttendanceRequest(
      propertyId: json['property_id'] as String,
      date: json['date'] as String,
      attendance: (json['attendance'] as List<dynamic>)
          .map((e) => AttendanceEntry.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$SubmitPropertyAttendanceRequestToJson(
        SubmitPropertyAttendanceRequest instance) =>
    <String, dynamic>{
      'property_id': instance.propertyId,
      'date': instance.date,
      'attendance': instance.attendance,
    };

UpdatePropertyAttendanceRequest _$UpdatePropertyAttendanceRequestFromJson(
        Map<String, dynamic> json) =>
    UpdatePropertyAttendanceRequest(
      status: json['status'] as String?,
      checkInTime: json['check_in_time'] as String?,
      checkOutTime: json['check_out_time'] as String?,
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$UpdatePropertyAttendanceRequestToJson(
        UpdatePropertyAttendanceRequest instance) =>
    <String, dynamic>{
      'status': instance.status,
      'check_in_time': instance.checkInTime,
      'check_out_time': instance.checkOutTime,
      'notes': instance.notes,
    };

SubmitSiteAttendanceRequest _$SubmitSiteAttendanceRequestFromJson(
        Map<String, dynamic> json) =>
    SubmitSiteAttendanceRequest(
      propertyId: json['property_id'] as String,
      date: json['date'] as String,
      attendance: (json['attendance'] as List<dynamic>)
          .map((e) => AttendanceEntry.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$SubmitSiteAttendanceRequestToJson(
        SubmitSiteAttendanceRequest instance) =>
    <String, dynamic>{
      'property_id': instance.propertyId,
      'date': instance.date,
      'attendance': instance.attendance,
    };

SubmitOfficeAttendanceRequest _$SubmitOfficeAttendanceRequestFromJson(
        Map<String, dynamic> json) =>
    SubmitOfficeAttendanceRequest(
      officeId: json['office_id'] as String,
      date: json['date'] as String,
      attendance: (json['attendance'] as List<dynamic>)
          .map((e) => AttendanceEntry.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$SubmitOfficeAttendanceRequestToJson(
        SubmitOfficeAttendanceRequest instance) =>
    <String, dynamic>{
      'office_id': instance.officeId,
      'date': instance.date,
      'attendance': instance.attendance,
    };

UpdateSiteAttendanceRequest _$UpdateSiteAttendanceRequestFromJson(
        Map<String, dynamic> json) =>
    UpdateSiteAttendanceRequest(
      status: json['status'] as String?,
      checkInTime: json['check_in_time'] as String?,
      checkOutTime: json['check_out_time'] as String?,
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$UpdateSiteAttendanceRequestToJson(
        UpdateSiteAttendanceRequest instance) =>
    <String, dynamic>{
      'status': instance.status,
      'check_in_time': instance.checkInTime,
      'check_out_time': instance.checkOutTime,
      'notes': instance.notes,
    };

UpdateOfficeAttendanceRequest _$UpdateOfficeAttendanceRequestFromJson(
        Map<String, dynamic> json) =>
    UpdateOfficeAttendanceRequest(
      status: json['status'] as String?,
      checkInTime: json['check_in_time'] as String?,
      checkOutTime: json['check_out_time'] as String?,
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$UpdateOfficeAttendanceRequestToJson(
        UpdateOfficeAttendanceRequest instance) =>
    <String, dynamic>{
      'status': instance.status,
      'check_in_time': instance.checkInTime,
      'check_out_time': instance.checkOutTime,
      'notes': instance.notes,
    };

AttendanceEntry _$AttendanceEntryFromJson(Map<String, dynamic> json) =>
    AttendanceEntry(
      userId: json['user_id'] as String,
      status: json['status'] as String,
      checkInTime: json['check_in_time'] as String?,
      checkOutTime: json['check_out_time'] as String?,
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$AttendanceEntryToJson(AttendanceEntry instance) =>
    <String, dynamic>{
      'user_id': instance.userId,
      'status': instance.status,
      'check_in_time': instance.checkInTime,
      'check_out_time': instance.checkOutTime,
      'notes': instance.notes,
    };

AttendanceSummary _$AttendanceSummaryFromJson(Map<String, dynamic> json) =>
    AttendanceSummary(
      totalEmployees: (json['total_employees'] as num).toInt(),
      presentCount: (json['present_count'] as num).toInt(),
      absentCount: (json['absent_count'] as num).toInt(),
      lateCount: (json['late_count'] as num).toInt(),
      attendancePercentage: (json['attendance_percentage'] as num).toDouble(),
      startDate: json['start_date'] as String,
      endDate: json['end_date'] as String,
    );

Map<String, dynamic> _$AttendanceSummaryToJson(AttendanceSummary instance) =>
    <String, dynamic>{
      'total_employees': instance.totalEmployees,
      'present_count': instance.presentCount,
      'absent_count': instance.absentCount,
      'late_count': instance.lateCount,
      'attendance_percentage': instance.attendancePercentage,
      'start_date': instance.startDate,
      'end_date': instance.endDate,
    };

UserAttendanceReport _$UserAttendanceReportFromJson(
        Map<String, dynamic> json) =>
    UserAttendanceReport(
      userId: json['user_id'] as String,
      userName: json['user_name'] as String,
      totalDays: (json['total_days'] as num).toInt(),
      presentDays: (json['present_days'] as num).toInt(),
      absentDays: (json['absent_days'] as num).toInt(),
      lateDays: (json['late_days'] as num).toInt(),
      attendancePercentage: (json['attendance_percentage'] as num).toDouble(),
      records: (json['records'] as List<dynamic>)
          .map((e) => AttendanceRecord.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$UserAttendanceReportToJson(
        UserAttendanceReport instance) =>
    <String, dynamic>{
      'user_id': instance.userId,
      'user_name': instance.userName,
      'total_days': instance.totalDays,
      'present_days': instance.presentDays,
      'absent_days': instance.absentDays,
      'late_days': instance.lateDays,
      'attendance_percentage': instance.attendancePercentage,
      'records': instance.records,
    };

VoidResponse _$VoidResponseFromJson(Map<String, dynamic> json) => VoidResponse(
      message: json['message'] as String,
    );

Map<String, dynamic> _$VoidResponseToJson(VoidResponse instance) =>
    <String, dynamic>{
      'message': instance.message,
    };

// **************************************************************************
// RetrofitGenerator
// **************************************************************************

// ignore_for_file: unnecessary_brace_in_string_interps,no_leading_underscores_for_local_identifiers,unused_element,unnecessary_string_interpolations

class _AttendanceApiService implements AttendanceApiService {
  _AttendanceApiService(
    this._dio, {
    this.baseUrl,
    this.errorLogger,
  });

  final Dio _dio;

  String? baseUrl;

  final ParseErrorLogger? errorLogger;

  @override
  Future<ApiResponse<List<AttendanceRecord>>> getPropertyAttendance({
    String? propertyId,
    String? date,
    String? startDate,
    String? endDate,
    String? userId,
    int? page,
    int? limit,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'property_id': propertyId,
      r'date': date,
      r'start_date': startDate,
      r'end_date': endDate,
      r'user_id': userId,
      r'page': page,
      r'limit': limit,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options =
        _setStreamType<ApiResponse<List<AttendanceRecord>>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/attendance',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(
                baseUrl: _combineBaseUrls(
              _dio.options.baseUrl,
              baseUrl,
            )));
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<List<AttendanceRecord>> _value;
    try {
      _value = ApiResponse<List<AttendanceRecord>>.fromJson(
        _result.data!,
        (json) => json is List<dynamic>
            ? json
                .map<AttendanceRecord>(
                    (i) => AttendanceRecord.fromJson(i as Map<String, dynamic>))
                .toList()
            : List.empty(),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<List<AttendanceRecord>>> submitSiteAttendance(
      SubmitSiteAttendanceRequest request) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(request.toJson());
    final _options =
        _setStreamType<ApiResponse<List<AttendanceRecord>>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/attendance/sites',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(
                baseUrl: _combineBaseUrls(
              _dio.options.baseUrl,
              baseUrl,
            )));
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<List<AttendanceRecord>> _value;
    try {
      _value = ApiResponse<List<AttendanceRecord>>.fromJson(
        _result.data!,
        (json) => json is List<dynamic>
            ? json
                .map<AttendanceRecord>(
                    (i) => AttendanceRecord.fromJson(i as Map<String, dynamic>))
                .toList()
            : List.empty(),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<AttendanceRecord>> getSiteAttendanceById(String id) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<ApiResponse<AttendanceRecord>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/api/attendance/sites/${id}',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<AttendanceRecord> _value;
    try {
      _value = ApiResponse<AttendanceRecord>.fromJson(
        _result.data!,
        (json) => AttendanceRecord.fromJson(json as Map<String, dynamic>),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<AttendanceRecord>> updateSiteAttendance(
    String id,
    UpdateSiteAttendanceRequest request,
  ) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(request.toJson());
    final _options = _setStreamType<ApiResponse<AttendanceRecord>>(Options(
      method: 'PUT',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/api/attendance/sites/${id}',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<AttendanceRecord> _value;
    try {
      _value = ApiResponse<AttendanceRecord>.fromJson(
        _result.data!,
        (json) => AttendanceRecord.fromJson(json as Map<String, dynamic>),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<VoidResponse>> deleteSiteAttendance(String id) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<ApiResponse<VoidResponse>>(Options(
      method: 'DELETE',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/api/attendance/sites/${id}',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<VoidResponse> _value;
    try {
      _value = ApiResponse<VoidResponse>.fromJson(
        _result.data!,
        (json) => VoidResponse.fromJson(json as Map<String, dynamic>),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<List<AttendanceRecord>>> getAttendanceByProperty({
    String? propertyId,
    String? date,
    String? startDate,
    String? endDate,
    String? userId,
    int? page,
    int? limit,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'property_id': propertyId,
      r'date': date,
      r'start_date': startDate,
      r'end_date': endDate,
      r'user_id': userId,
      r'page': page,
      r'limit': limit,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options =
        _setStreamType<ApiResponse<List<AttendanceRecord>>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/attendance',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(
                baseUrl: _combineBaseUrls(
              _dio.options.baseUrl,
              baseUrl,
            )));
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<List<AttendanceRecord>> _value;
    try {
      _value = ApiResponse<List<AttendanceRecord>>.fromJson(
        _result.data!,
        (json) => json is List<dynamic>
            ? json
                .map<AttendanceRecord>(
                    (i) => AttendanceRecord.fromJson(i as Map<String, dynamic>))
                .toList()
            : List.empty(),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<List<AttendanceRecord>>> submitAttendance(
      SubmitPropertyAttendanceRequest request) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(request.toJson());
    final _options =
        _setStreamType<ApiResponse<List<AttendanceRecord>>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/attendance',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(
                baseUrl: _combineBaseUrls(
              _dio.options.baseUrl,
              baseUrl,
            )));
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<List<AttendanceRecord>> _value;
    try {
      _value = ApiResponse<List<AttendanceRecord>>.fromJson(
        _result.data!,
        (json) => json is List<dynamic>
            ? json
                .map<AttendanceRecord>(
                    (i) => AttendanceRecord.fromJson(i as Map<String, dynamic>))
                .toList()
            : List.empty(),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<AttendanceRecord>> getAttendanceById(String id) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<ApiResponse<AttendanceRecord>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/api/attendance/${id}',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<AttendanceRecord> _value;
    try {
      _value = ApiResponse<AttendanceRecord>.fromJson(
        _result.data!,
        (json) => AttendanceRecord.fromJson(json as Map<String, dynamic>),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<AttendanceRecord>> updateAttendance(
    String id,
    UpdatePropertyAttendanceRequest request,
  ) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(request.toJson());
    final _options = _setStreamType<ApiResponse<AttendanceRecord>>(Options(
      method: 'PUT',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/api/attendance/${id}',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<AttendanceRecord> _value;
    try {
      _value = ApiResponse<AttendanceRecord>.fromJson(
        _result.data!,
        (json) => AttendanceRecord.fromJson(json as Map<String, dynamic>),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<VoidResponse>> deleteAttendance(String id) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<ApiResponse<VoidResponse>>(Options(
      method: 'DELETE',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/api/attendance/${id}',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<VoidResponse> _value;
    try {
      _value = ApiResponse<VoidResponse>.fromJson(
        _result.data!,
        (json) => VoidResponse.fromJson(json as Map<String, dynamic>),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<AttendanceSummary>> getAttendanceSummary({
    String? startDate,
    String? endDate,
    String? propertyId,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'start_date': startDate,
      r'end_date': endDate,
      r'property_id': propertyId,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<ApiResponse<AttendanceSummary>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/api/attendance/reports/summary',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<AttendanceSummary> _value;
    try {
      _value = ApiResponse<AttendanceSummary>.fromJson(
        _result.data!,
        (json) => AttendanceSummary.fromJson(json as Map<String, dynamic>),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<UserAttendanceReport>> getUserAttendanceReport(
    String userId,
    String? startDate,
    String? endDate,
  ) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'start_date': startDate,
      r'end_date': endDate,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<ApiResponse<UserAttendanceReport>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/api/attendance/reports/user/${userId}',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<UserAttendanceReport> _value;
    try {
      _value = ApiResponse<UserAttendanceReport>.fromJson(
        _result.data!,
        (json) => UserAttendanceReport.fromJson(json as Map<String, dynamic>),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  RequestOptions _setStreamType<T>(RequestOptions requestOptions) {
    if (T != dynamic &&
        !(requestOptions.responseType == ResponseType.bytes ||
            requestOptions.responseType == ResponseType.stream)) {
      if (T == String) {
        requestOptions.responseType = ResponseType.plain;
      } else {
        requestOptions.responseType = ResponseType.json;
      }
    }
    return requestOptions;
  }

  String _combineBaseUrls(
    String dioBaseUrl,
    String? baseUrl,
  ) {
    if (baseUrl == null || baseUrl.trim().isEmpty) {
      return dioBaseUrl;
    }

    final url = Uri.parse(baseUrl);

    if (url.isAbsolute) {
      return url.toString();
    }

    return Uri.parse(dioBaseUrl).resolveUri(url).toString();
  }
}
