"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/dashboard/status/route";
exports.ids = ["app/api/dashboard/status/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdashboard%2Fstatus%2Froute&page=%2Fapi%2Fdashboard%2Fstatus%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fstatus%2Froute.ts&appDir=D%3A%5Cworkspaces%5Cnsl%5Cback%5CSrsrMan%5Cbackend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cworkspaces%5Cnsl%5Cback%5CSrsrMan%5Cbackend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdashboard%2Fstatus%2Froute&page=%2Fapi%2Fdashboard%2Fstatus%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fstatus%2Froute.ts&appDir=D%3A%5Cworkspaces%5Cnsl%5Cback%5CSrsrMan%5Cbackend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cworkspaces%5Cnsl%5Cback%5CSrsrMan%5Cbackend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_workspaces_nsl_back_SrsrMan_backend_app_api_dashboard_status_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/dashboard/status/route.ts */ \"(rsc)/./app/api/dashboard/status/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/dashboard/status/route\",\n        pathname: \"/api/dashboard/status\",\n        filename: \"route\",\n        bundlePath: \"app/api/dashboard/status/route\"\n    },\n    resolvedPagePath: \"D:\\\\workspaces\\\\nsl\\\\back\\\\SrsrMan\\\\backend\\\\app\\\\api\\\\dashboard\\\\status\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_workspaces_nsl_back_SrsrMan_backend_app_api_dashboard_status_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/dashboard/status/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdashboard%2Fstatus%2Froute&page=%2Fapi%2Fdashboard%2Fstatus%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fstatus%2Froute.ts&appDir=D%3A%5Cworkspaces%5Cnsl%5Cback%5CSrsrMan%5Cbackend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cworkspaces%5Cnsl%5Cback%5CSrsrMan%5Cbackend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/dashboard/status/route.ts":
/*!*******************************************!*\
  !*** ./app/api/dashboard/status/route.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   OPTIONS: () => (/* binding */ OPTIONS)\n/* harmony export */ });\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./lib/auth.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(rsc)/./lib/utils.ts\");\n/* harmony import */ var _lib_business_logic_dashboard_metrics__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/business-logic/dashboard-metrics */ \"(rsc)/./lib/business-logic/dashboard-metrics.ts\");\n/* harmony import */ var _lib_resilience_retry_handler__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/resilience/retry-handler */ \"(rsc)/./lib/resilience/retry-handler.ts\");\n\n\n\n\n\nasync function getDashboardStatusHandler(request, context, currentUser) {\n    try {\n        const url = new URL(request.url);\n        const propertyId = url.searchParams.get(\"property_id\");\n        const includeDetails = url.searchParams.get(\"include_details\") === \"true\";\n        // If property_id is specified, return property-specific dashboard (V1 style)\n        if (propertyId) {\n            const propertyDashboard = await (0,_lib_resilience_retry_handler__WEBPACK_IMPORTED_MODULE_4__.withApiResilience)(()=>(0,_lib_business_logic_dashboard_metrics__WEBPACK_IMPORTED_MODULE_3__.generatePropertyDashboardStatus)(propertyId), {\n                endpoint: `dashboard/property/${propertyId}`,\n                fallbackValue: {\n                    id: propertyId,\n                    name: \"Unknown Property\",\n                    type: \"residential\",\n                    overallStatus: \"red\",\n                    functionalAreas: {\n                        electricity: {\n                            status: \"red\",\n                            metrics: [],\n                            issueCount: 1,\n                            lastUpdated: new Date()\n                        },\n                        internet: {\n                            status: \"green\",\n                            metrics: [],\n                            issueCount: 0,\n                            lastUpdated: new Date()\n                        },\n                        maintenance: {\n                            status: \"green\",\n                            metrics: [],\n                            issueCount: 0,\n                            lastUpdated: new Date()\n                        },\n                        ott_services: {\n                            status: \"green\",\n                            metrics: [],\n                            issueCount: 0,\n                            lastUpdated: new Date()\n                        }\n                    },\n                    lastUpdated: new Date()\n                }\n            });\n            return Response.json((0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.createApiResponse)(propertyDashboard), {\n                status: 200,\n                headers: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.corsHeaders)()\n            });\n        }\n        // System-wide dashboard with V1's rich metrics\n        const systemMetrics = await (0,_lib_resilience_retry_handler__WEBPACK_IMPORTED_MODULE_4__.withApiResilience)(()=>(0,_lib_business_logic_dashboard_metrics__WEBPACK_IMPORTED_MODULE_3__.generateSystemDashboardMetrics)(), {\n            endpoint: \"dashboard/system\",\n            fallbackValue: {\n                properties: {\n                    total: 0,\n                    operational: 0,\n                    warning: 0,\n                    critical: 0\n                },\n                maintenance_issues: {\n                    total: 0,\n                    open: 0,\n                    in_progress: 0,\n                    critical: 0\n                },\n                recent_alerts: [],\n                system_health: {\n                    overall_score: 0,\n                    uptime_percentage: 0,\n                    active_services: 0,\n                    total_services: 0\n                }\n            }\n        });\n        // Enhanced response with V1-style rich data\n        const responseData = {\n            ...systemMetrics,\n            // Add V1-style summary metrics\n            summary: {\n                overall_health: systemMetrics.system_health.overall_score,\n                critical_alerts: systemMetrics.recent_alerts.filter((alert)=>alert.severity === \"critical\").length,\n                properties_needing_attention: systemMetrics.properties.warning + systemMetrics.properties.critical,\n                system_status: systemMetrics.system_health.overall_score >= 90 ? \"excellent\" : systemMetrics.system_health.overall_score >= 75 ? \"good\" : systemMetrics.system_health.overall_score >= 50 ? \"fair\" : \"poor\"\n            },\n            // Add timestamp for cache management\n            generated_at: new Date(),\n            cache_duration: 300\n        };\n        // Include detailed property breakdowns if requested\n        if (includeDetails) {\n            const activeProperties = await (0,_lib_resilience_retry_handler__WEBPACK_IMPORTED_MODULE_4__.withApiResilience)(()=>_lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.property.findMany({\n                    where: {\n                        isActive: true\n                    },\n                    select: {\n                        id: true,\n                        name: true,\n                        type: true\n                    },\n                    take: 10\n                }), {\n                endpoint: \"dashboard/properties\",\n                fallbackValue: []\n            });\n            const propertyDetails = await Promise.allSettled(activeProperties.map((property)=>(0,_lib_resilience_retry_handler__WEBPACK_IMPORTED_MODULE_4__.withApiResilience)(()=>(0,_lib_business_logic_dashboard_metrics__WEBPACK_IMPORTED_MODULE_3__.generatePropertyDashboardStatus)(property.id), {\n                    endpoint: `dashboard/property/${property.id}`,\n                    fallbackValue: null\n                })));\n            responseData.property_details = propertyDetails.filter((result)=>result.status === \"fulfilled\" && result.value !== null).map((result)=>result.value);\n        }\n        return Response.json((0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.createApiResponse)(responseData), {\n            status: 200,\n            headers: {\n                ...(0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.corsHeaders)(),\n                \"Cache-Control\": \"public, max-age=300\"\n            }\n        });\n    } catch (error) {\n        return (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.handleError)(error, \"Failed to fetch dashboard status\", \"dashboard-status\");\n    }\n}\nconst GET = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.requireAuth)(getDashboardStatusHandler);\nasync function OPTIONS() {\n    return new Response(null, {\n        status: 200,\n        headers: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.corsHeaders)()\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvYXBpL2Rhc2hib2FyZC9zdGF0dXMvcm91dGUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUNzQztBQUNHO0FBQ2lDO0FBSTFCO0FBQ21CO0FBRW5FLGVBQWVRLDBCQUEwQkMsT0FBb0IsRUFBRUMsT0FBWSxFQUFFQyxXQUFnQjtJQUMzRixJQUFJO1FBQ0YsTUFBTUMsTUFBTSxJQUFJQyxJQUFJSixRQUFRRyxHQUFHO1FBQy9CLE1BQU1FLGFBQWFGLElBQUlHLFlBQVksQ0FBQ0MsR0FBRyxDQUFDO1FBQ3hDLE1BQU1DLGlCQUFpQkwsSUFBSUcsWUFBWSxDQUFDQyxHQUFHLENBQUMsdUJBQXVCO1FBRW5FLDZFQUE2RTtRQUM3RSxJQUFJRixZQUFZO1lBQ2QsTUFBTUksb0JBQW9CLE1BQU1YLGdGQUFpQkEsQ0FDL0MsSUFBTUQsc0dBQStCQSxDQUFDUSxhQUN0QztnQkFDRUssVUFBVSxDQUFDLG1CQUFtQixFQUFFTCxXQUFXLENBQUM7Z0JBQzVDTSxlQUFlO29CQUNiQyxJQUFJUDtvQkFDSlEsTUFBTTtvQkFDTkMsTUFBTTtvQkFDTkMsZUFBZTtvQkFDZkMsaUJBQWlCO3dCQUNmQyxhQUFhOzRCQUNYQyxRQUFROzRCQUNSQyxTQUFTLEVBQUU7NEJBQ1hDLFlBQVk7NEJBQ1pDLGFBQWEsSUFBSUM7d0JBQ25CO3dCQUNBQyxVQUFVOzRCQUNSTCxRQUFROzRCQUNSQyxTQUFTLEVBQUU7NEJBQ1hDLFlBQVk7NEJBQ1pDLGFBQWEsSUFBSUM7d0JBQ25CO3dCQUNBRSxhQUFhOzRCQUNYTixRQUFROzRCQUNSQyxTQUFTLEVBQUU7NEJBQ1hDLFlBQVk7NEJBQ1pDLGFBQWEsSUFBSUM7d0JBQ25CO3dCQUNBRyxjQUFjOzRCQUNaUCxRQUFROzRCQUNSQyxTQUFTLEVBQUU7NEJBQ1hDLFlBQVk7NEJBQ1pDLGFBQWEsSUFBSUM7d0JBQ25CO29CQUNGO29CQUNBRCxhQUFhLElBQUlDO2dCQUNuQjtZQUNGO1lBR0YsT0FBT0ksU0FBU0MsSUFBSSxDQUNsQmxDLDZEQUFpQkEsQ0FBQ2dCLG9CQUNsQjtnQkFDRVMsUUFBUTtnQkFDUlUsU0FBU2pDLHVEQUFXQTtZQUN0QjtRQUVKO1FBRUEsK0NBQStDO1FBQy9DLE1BQU1rQyxnQkFBZ0IsTUFBTS9CLGdGQUFpQkEsQ0FDM0MsSUFBTUYscUdBQThCQSxJQUNwQztZQUNFYyxVQUFVO1lBQ1ZDLGVBQWU7Z0JBQ2JtQixZQUFZO29CQUFFQyxPQUFPO29CQUFHQyxhQUFhO29CQUFHQyxTQUFTO29CQUFHQyxVQUFVO2dCQUFFO2dCQUNoRUMsb0JBQW9CO29CQUFFSixPQUFPO29CQUFHSyxNQUFNO29CQUFHQyxhQUFhO29CQUFHSCxVQUFVO2dCQUFFO2dCQUNyRUksZUFBZSxFQUFFO2dCQUNqQkMsZUFBZTtvQkFBRUMsZUFBZTtvQkFBR0MsbUJBQW1CO29CQUFHQyxpQkFBaUI7b0JBQUdDLGdCQUFnQjtnQkFBRTtZQUNqRztRQUNGO1FBR0YsNENBQTRDO1FBQzVDLE1BQU1DLGVBQWU7WUFDbkIsR0FBR2YsYUFBYTtZQUNoQiwrQkFBK0I7WUFDL0JnQixTQUFTO2dCQUNQQyxnQkFBZ0JqQixjQUFjVSxhQUFhLENBQUNDLGFBQWE7Z0JBQ3pETyxpQkFBaUJsQixjQUFjUyxhQUFhLENBQUNVLE1BQU0sQ0FBQ0MsQ0FBQUEsUUFBU0EsTUFBTUMsUUFBUSxLQUFLLFlBQVlDLE1BQU07Z0JBQ2xHQyw4QkFBOEJ2QixjQUFjQyxVQUFVLENBQUNHLE9BQU8sR0FBR0osY0FBY0MsVUFBVSxDQUFDSSxRQUFRO2dCQUNsR21CLGVBQWV4QixjQUFjVSxhQUFhLENBQUNDLGFBQWEsSUFBSSxLQUFLLGNBQ25EWCxjQUFjVSxhQUFhLENBQUNDLGFBQWEsSUFBSSxLQUFLLFNBQ2xEWCxjQUFjVSxhQUFhLENBQUNDLGFBQWEsSUFBSSxLQUFLLFNBQVM7WUFDM0U7WUFDQSxxQ0FBcUM7WUFDckNjLGNBQWMsSUFBSWhDO1lBQ2xCaUMsZ0JBQWdCO1FBQ2xCO1FBRUEsb0RBQW9EO1FBQ3BELElBQUkvQyxnQkFBZ0I7WUFDbEIsTUFBTWdELG1CQUFtQixNQUFNMUQsZ0ZBQWlCQSxDQUM5QyxJQUFNUCwrQ0FBTUEsQ0FBQ2tFLFFBQVEsQ0FBQ0MsUUFBUSxDQUFDO29CQUM3QkMsT0FBTzt3QkFBRUMsVUFBVTtvQkFBSztvQkFDeEJDLFFBQVE7d0JBQUVqRCxJQUFJO3dCQUFNQyxNQUFNO3dCQUFNQyxNQUFNO29CQUFLO29CQUMzQ2dELE1BQU07Z0JBQ1IsSUFDQTtnQkFDRXBELFVBQVU7Z0JBQ1ZDLGVBQWUsRUFBRTtZQUNuQjtZQUdGLE1BQU1vRCxrQkFBa0IsTUFBTUMsUUFBUUMsVUFBVSxDQUM5Q1QsaUJBQWlCVSxHQUFHLENBQUNULENBQUFBLFdBQ25CM0QsZ0ZBQWlCQSxDQUNmLElBQU1ELHNHQUErQkEsQ0FBQzRELFNBQVM3QyxFQUFFLEdBQ2pEO29CQUNFRixVQUFVLENBQUMsbUJBQW1CLEVBQUUrQyxTQUFTN0MsRUFBRSxDQUFDLENBQUM7b0JBQzdDRCxlQUFlO2dCQUNqQjtZQUtOaUMsYUFBYXVCLGdCQUFnQixHQUFHSixnQkFDN0JmLE1BQU0sQ0FBQyxDQUFDb0IsU0FDUEEsT0FBT2xELE1BQU0sS0FBSyxlQUFla0QsT0FBT0MsS0FBSyxLQUFLLE1BRW5ESCxHQUFHLENBQUNFLENBQUFBLFNBQVVBLE9BQU9DLEtBQUs7UUFDL0I7UUFFQSxPQUFPM0MsU0FBU0MsSUFBSSxDQUNsQmxDLDZEQUFpQkEsQ0FBQ21ELGVBQ2xCO1lBQ0UxQixRQUFRO1lBQ1JVLFNBQVM7Z0JBQ1AsR0FBR2pDLHVEQUFXQSxFQUFFO2dCQUNoQixpQkFBaUI7WUFDbkI7UUFDRjtJQUVKLEVBQUUsT0FBTzJFLE9BQU87UUFDZCxPQUFPNUUsdURBQVdBLENBQUM0RSxPQUFPLG9DQUFvQztJQUNoRTtBQUNGO0FBRU8sTUFBTUMsTUFBTS9FLHNEQUFXQSxDQUFDTywyQkFBMkI7QUFFbkQsZUFBZXlFO0lBQ3BCLE9BQU8sSUFBSTlDLFNBQVMsTUFBTTtRQUN4QlIsUUFBUTtRQUNSVSxTQUFTakMsdURBQVdBO0lBQ3RCO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zcnNyLXByb3BlcnR5LW1hbmFnZW1lbnQtYmFja2VuZC8uL2FwcC9hcGkvZGFzaGJvYXJkL3N0YXR1cy9yb3V0ZS50cz81NzZjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IE5leHRSZXF1ZXN0IH0gZnJvbSAnbmV4dC9zZXJ2ZXInO1xuaW1wb3J0IHsgcHJpc21hIH0gZnJvbSAnQC9saWIvcHJpc21hJztcbmltcG9ydCB7IHJlcXVpcmVBdXRoIH0gZnJvbSAnQC9saWIvYXV0aCc7XG5pbXBvcnQgeyBjcmVhdGVBcGlSZXNwb25zZSwgaGFuZGxlRXJyb3IsIGNvcnNIZWFkZXJzIH0gZnJvbSAnQC9saWIvdXRpbHMnO1xuaW1wb3J0IHtcbiAgZ2VuZXJhdGVTeXN0ZW1EYXNoYm9hcmRNZXRyaWNzLFxuICBnZW5lcmF0ZVByb3BlcnR5RGFzaGJvYXJkU3RhdHVzXG59IGZyb20gJ0AvbGliL2J1c2luZXNzLWxvZ2ljL2Rhc2hib2FyZC1tZXRyaWNzJztcbmltcG9ydCB7IHdpdGhBcGlSZXNpbGllbmNlIH0gZnJvbSAnQC9saWIvcmVzaWxpZW5jZS9yZXRyeS1oYW5kbGVyJztcblxuYXN5bmMgZnVuY3Rpb24gZ2V0RGFzaGJvYXJkU3RhdHVzSGFuZGxlcihyZXF1ZXN0OiBOZXh0UmVxdWVzdCwgY29udGV4dDogYW55LCBjdXJyZW50VXNlcjogYW55KSB7XG4gIHRyeSB7XG4gICAgY29uc3QgdXJsID0gbmV3IFVSTChyZXF1ZXN0LnVybCk7XG4gICAgY29uc3QgcHJvcGVydHlJZCA9IHVybC5zZWFyY2hQYXJhbXMuZ2V0KCdwcm9wZXJ0eV9pZCcpO1xuICAgIGNvbnN0IGluY2x1ZGVEZXRhaWxzID0gdXJsLnNlYXJjaFBhcmFtcy5nZXQoJ2luY2x1ZGVfZGV0YWlscycpID09PSAndHJ1ZSc7XG5cbiAgICAvLyBJZiBwcm9wZXJ0eV9pZCBpcyBzcGVjaWZpZWQsIHJldHVybiBwcm9wZXJ0eS1zcGVjaWZpYyBkYXNoYm9hcmQgKFYxIHN0eWxlKVxuICAgIGlmIChwcm9wZXJ0eUlkKSB7XG4gICAgICBjb25zdCBwcm9wZXJ0eURhc2hib2FyZCA9IGF3YWl0IHdpdGhBcGlSZXNpbGllbmNlKFxuICAgICAgICAoKSA9PiBnZW5lcmF0ZVByb3BlcnR5RGFzaGJvYXJkU3RhdHVzKHByb3BlcnR5SWQpLFxuICAgICAgICB7XG4gICAgICAgICAgZW5kcG9pbnQ6IGBkYXNoYm9hcmQvcHJvcGVydHkvJHtwcm9wZXJ0eUlkfWAsXG4gICAgICAgICAgZmFsbGJhY2tWYWx1ZToge1xuICAgICAgICAgICAgaWQ6IHByb3BlcnR5SWQsXG4gICAgICAgICAgICBuYW1lOiAnVW5rbm93biBQcm9wZXJ0eScsXG4gICAgICAgICAgICB0eXBlOiAncmVzaWRlbnRpYWwnIGFzIGNvbnN0LFxuICAgICAgICAgICAgb3ZlcmFsbFN0YXR1czogJ3JlZCcgYXMgY29uc3QsXG4gICAgICAgICAgICBmdW5jdGlvbmFsQXJlYXM6IHtcbiAgICAgICAgICAgICAgZWxlY3RyaWNpdHk6IHtcbiAgICAgICAgICAgICAgICBzdGF0dXM6ICdyZWQnIGFzIGNvbnN0LFxuICAgICAgICAgICAgICAgIG1ldHJpY3M6IFtdLFxuICAgICAgICAgICAgICAgIGlzc3VlQ291bnQ6IDEsXG4gICAgICAgICAgICAgICAgbGFzdFVwZGF0ZWQ6IG5ldyBEYXRlKCksXG4gICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgIGludGVybmV0OiB7XG4gICAgICAgICAgICAgICAgc3RhdHVzOiAnZ3JlZW4nIGFzIGNvbnN0LFxuICAgICAgICAgICAgICAgIG1ldHJpY3M6IFtdLFxuICAgICAgICAgICAgICAgIGlzc3VlQ291bnQ6IDAsXG4gICAgICAgICAgICAgICAgbGFzdFVwZGF0ZWQ6IG5ldyBEYXRlKCksXG4gICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgIG1haW50ZW5hbmNlOiB7XG4gICAgICAgICAgICAgICAgc3RhdHVzOiAnZ3JlZW4nIGFzIGNvbnN0LFxuICAgICAgICAgICAgICAgIG1ldHJpY3M6IFtdLFxuICAgICAgICAgICAgICAgIGlzc3VlQ291bnQ6IDAsXG4gICAgICAgICAgICAgICAgbGFzdFVwZGF0ZWQ6IG5ldyBEYXRlKCksXG4gICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgIG90dF9zZXJ2aWNlczoge1xuICAgICAgICAgICAgICAgIHN0YXR1czogJ2dyZWVuJyBhcyBjb25zdCxcbiAgICAgICAgICAgICAgICBtZXRyaWNzOiBbXSxcbiAgICAgICAgICAgICAgICBpc3N1ZUNvdW50OiAwLFxuICAgICAgICAgICAgICAgIGxhc3RVcGRhdGVkOiBuZXcgRGF0ZSgpLFxuICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIGxhc3RVcGRhdGVkOiBuZXcgRGF0ZSgpLFxuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgKTtcblxuICAgICAgcmV0dXJuIFJlc3BvbnNlLmpzb24oXG4gICAgICAgIGNyZWF0ZUFwaVJlc3BvbnNlKHByb3BlcnR5RGFzaGJvYXJkKSxcbiAgICAgICAge1xuICAgICAgICAgIHN0YXR1czogMjAwLFxuICAgICAgICAgIGhlYWRlcnM6IGNvcnNIZWFkZXJzKCksXG4gICAgICAgIH1cbiAgICAgICk7XG4gICAgfVxuXG4gICAgLy8gU3lzdGVtLXdpZGUgZGFzaGJvYXJkIHdpdGggVjEncyByaWNoIG1ldHJpY3NcbiAgICBjb25zdCBzeXN0ZW1NZXRyaWNzID0gYXdhaXQgd2l0aEFwaVJlc2lsaWVuY2UoXG4gICAgICAoKSA9PiBnZW5lcmF0ZVN5c3RlbURhc2hib2FyZE1ldHJpY3MoKSxcbiAgICAgIHtcbiAgICAgICAgZW5kcG9pbnQ6ICdkYXNoYm9hcmQvc3lzdGVtJyxcbiAgICAgICAgZmFsbGJhY2tWYWx1ZToge1xuICAgICAgICAgIHByb3BlcnRpZXM6IHsgdG90YWw6IDAsIG9wZXJhdGlvbmFsOiAwLCB3YXJuaW5nOiAwLCBjcml0aWNhbDogMCB9LFxuICAgICAgICAgIG1haW50ZW5hbmNlX2lzc3VlczogeyB0b3RhbDogMCwgb3BlbjogMCwgaW5fcHJvZ3Jlc3M6IDAsIGNyaXRpY2FsOiAwIH0sXG4gICAgICAgICAgcmVjZW50X2FsZXJ0czogW10sXG4gICAgICAgICAgc3lzdGVtX2hlYWx0aDogeyBvdmVyYWxsX3Njb3JlOiAwLCB1cHRpbWVfcGVyY2VudGFnZTogMCwgYWN0aXZlX3NlcnZpY2VzOiAwLCB0b3RhbF9zZXJ2aWNlczogMCB9LFxuICAgICAgICB9XG4gICAgICB9XG4gICAgKTtcblxuICAgIC8vIEVuaGFuY2VkIHJlc3BvbnNlIHdpdGggVjEtc3R5bGUgcmljaCBkYXRhXG4gICAgY29uc3QgcmVzcG9uc2VEYXRhID0ge1xuICAgICAgLi4uc3lzdGVtTWV0cmljcyxcbiAgICAgIC8vIEFkZCBWMS1zdHlsZSBzdW1tYXJ5IG1ldHJpY3NcbiAgICAgIHN1bW1hcnk6IHtcbiAgICAgICAgb3ZlcmFsbF9oZWFsdGg6IHN5c3RlbU1ldHJpY3Muc3lzdGVtX2hlYWx0aC5vdmVyYWxsX3Njb3JlLFxuICAgICAgICBjcml0aWNhbF9hbGVydHM6IHN5c3RlbU1ldHJpY3MucmVjZW50X2FsZXJ0cy5maWx0ZXIoYWxlcnQgPT4gYWxlcnQuc2V2ZXJpdHkgPT09ICdjcml0aWNhbCcpLmxlbmd0aCxcbiAgICAgICAgcHJvcGVydGllc19uZWVkaW5nX2F0dGVudGlvbjogc3lzdGVtTWV0cmljcy5wcm9wZXJ0aWVzLndhcm5pbmcgKyBzeXN0ZW1NZXRyaWNzLnByb3BlcnRpZXMuY3JpdGljYWwsXG4gICAgICAgIHN5c3RlbV9zdGF0dXM6IHN5c3RlbU1ldHJpY3Muc3lzdGVtX2hlYWx0aC5vdmVyYWxsX3Njb3JlID49IDkwID8gJ2V4Y2VsbGVudCcgOlxuICAgICAgICAgICAgICAgICAgICAgIHN5c3RlbU1ldHJpY3Muc3lzdGVtX2hlYWx0aC5vdmVyYWxsX3Njb3JlID49IDc1ID8gJ2dvb2QnIDpcbiAgICAgICAgICAgICAgICAgICAgICBzeXN0ZW1NZXRyaWNzLnN5c3RlbV9oZWFsdGgub3ZlcmFsbF9zY29yZSA+PSA1MCA/ICdmYWlyJyA6ICdwb29yJyxcbiAgICAgIH0sXG4gICAgICAvLyBBZGQgdGltZXN0YW1wIGZvciBjYWNoZSBtYW5hZ2VtZW50XG4gICAgICBnZW5lcmF0ZWRfYXQ6IG5ldyBEYXRlKCksXG4gICAgICBjYWNoZV9kdXJhdGlvbjogMzAwLCAvLyA1IG1pbnV0ZXMgY2FjaGUgc3VnZ2VzdGlvblxuICAgIH07XG5cbiAgICAvLyBJbmNsdWRlIGRldGFpbGVkIHByb3BlcnR5IGJyZWFrZG93bnMgaWYgcmVxdWVzdGVkXG4gICAgaWYgKGluY2x1ZGVEZXRhaWxzKSB7XG4gICAgICBjb25zdCBhY3RpdmVQcm9wZXJ0aWVzID0gYXdhaXQgd2l0aEFwaVJlc2lsaWVuY2UoXG4gICAgICAgICgpID0+IHByaXNtYS5wcm9wZXJ0eS5maW5kTWFueSh7XG4gICAgICAgICAgd2hlcmU6IHsgaXNBY3RpdmU6IHRydWUgfSxcbiAgICAgICAgICBzZWxlY3Q6IHsgaWQ6IHRydWUsIG5hbWU6IHRydWUsIHR5cGU6IHRydWUgfSxcbiAgICAgICAgICB0YWtlOiAxMCwgLy8gTGltaXQgZm9yIHBlcmZvcm1hbmNlXG4gICAgICAgIH0pLFxuICAgICAgICB7XG4gICAgICAgICAgZW5kcG9pbnQ6ICdkYXNoYm9hcmQvcHJvcGVydGllcycsXG4gICAgICAgICAgZmFsbGJhY2tWYWx1ZTogW11cbiAgICAgICAgfVxuICAgICAgKTtcblxuICAgICAgY29uc3QgcHJvcGVydHlEZXRhaWxzID0gYXdhaXQgUHJvbWlzZS5hbGxTZXR0bGVkKFxuICAgICAgICBhY3RpdmVQcm9wZXJ0aWVzLm1hcChwcm9wZXJ0eSA9PlxuICAgICAgICAgIHdpdGhBcGlSZXNpbGllbmNlKFxuICAgICAgICAgICAgKCkgPT4gZ2VuZXJhdGVQcm9wZXJ0eURhc2hib2FyZFN0YXR1cyhwcm9wZXJ0eS5pZCksXG4gICAgICAgICAgICB7XG4gICAgICAgICAgICAgIGVuZHBvaW50OiBgZGFzaGJvYXJkL3Byb3BlcnR5LyR7cHJvcGVydHkuaWR9YCxcbiAgICAgICAgICAgICAgZmFsbGJhY2tWYWx1ZTogbnVsbFxuICAgICAgICAgICAgfVxuICAgICAgICAgIClcbiAgICAgICAgKVxuICAgICAgKTtcblxuICAgICAgcmVzcG9uc2VEYXRhLnByb3BlcnR5X2RldGFpbHMgPSBwcm9wZXJ0eURldGFpbHNcbiAgICAgICAgLmZpbHRlcigocmVzdWx0KTogcmVzdWx0IGlzIFByb21pc2VGdWxmaWxsZWRSZXN1bHQ8YW55PiA9PlxuICAgICAgICAgIHJlc3VsdC5zdGF0dXMgPT09ICdmdWxmaWxsZWQnICYmIHJlc3VsdC52YWx1ZSAhPT0gbnVsbFxuICAgICAgICApXG4gICAgICAgIC5tYXAocmVzdWx0ID0+IHJlc3VsdC52YWx1ZSk7XG4gICAgfVxuXG4gICAgcmV0dXJuIFJlc3BvbnNlLmpzb24oXG4gICAgICBjcmVhdGVBcGlSZXNwb25zZShyZXNwb25zZURhdGEpLFxuICAgICAge1xuICAgICAgICBzdGF0dXM6IDIwMCxcbiAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgIC4uLmNvcnNIZWFkZXJzKCksXG4gICAgICAgICAgJ0NhY2hlLUNvbnRyb2wnOiAncHVibGljLCBtYXgtYWdlPTMwMCcsIC8vIDUgbWludXRlcyBjYWNoZVxuICAgICAgICB9LFxuICAgICAgfVxuICAgICk7XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgcmV0dXJuIGhhbmRsZUVycm9yKGVycm9yLCAnRmFpbGVkIHRvIGZldGNoIGRhc2hib2FyZCBzdGF0dXMnLCAnZGFzaGJvYXJkLXN0YXR1cycpO1xuICB9XG59XG5cbmV4cG9ydCBjb25zdCBHRVQgPSByZXF1aXJlQXV0aChnZXREYXNoYm9hcmRTdGF0dXNIYW5kbGVyKTtcblxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIE9QVElPTlMoKSB7XG4gIHJldHVybiBuZXcgUmVzcG9uc2UobnVsbCwge1xuICAgIHN0YXR1czogMjAwLFxuICAgIGhlYWRlcnM6IGNvcnNIZWFkZXJzKCksXG4gIH0pO1xufVxuIl0sIm5hbWVzIjpbInByaXNtYSIsInJlcXVpcmVBdXRoIiwiY3JlYXRlQXBpUmVzcG9uc2UiLCJoYW5kbGVFcnJvciIsImNvcnNIZWFkZXJzIiwiZ2VuZXJhdGVTeXN0ZW1EYXNoYm9hcmRNZXRyaWNzIiwiZ2VuZXJhdGVQcm9wZXJ0eURhc2hib2FyZFN0YXR1cyIsIndpdGhBcGlSZXNpbGllbmNlIiwiZ2V0RGFzaGJvYXJkU3RhdHVzSGFuZGxlciIsInJlcXVlc3QiLCJjb250ZXh0IiwiY3VycmVudFVzZXIiLCJ1cmwiLCJVUkwiLCJwcm9wZXJ0eUlkIiwic2VhcmNoUGFyYW1zIiwiZ2V0IiwiaW5jbHVkZURldGFpbHMiLCJwcm9wZXJ0eURhc2hib2FyZCIsImVuZHBvaW50IiwiZmFsbGJhY2tWYWx1ZSIsImlkIiwibmFtZSIsInR5cGUiLCJvdmVyYWxsU3RhdHVzIiwiZnVuY3Rpb25hbEFyZWFzIiwiZWxlY3RyaWNpdHkiLCJzdGF0dXMiLCJtZXRyaWNzIiwiaXNzdWVDb3VudCIsImxhc3RVcGRhdGVkIiwiRGF0ZSIsImludGVybmV0IiwibWFpbnRlbmFuY2UiLCJvdHRfc2VydmljZXMiLCJSZXNwb25zZSIsImpzb24iLCJoZWFkZXJzIiwic3lzdGVtTWV0cmljcyIsInByb3BlcnRpZXMiLCJ0b3RhbCIsIm9wZXJhdGlvbmFsIiwid2FybmluZyIsImNyaXRpY2FsIiwibWFpbnRlbmFuY2VfaXNzdWVzIiwib3BlbiIsImluX3Byb2dyZXNzIiwicmVjZW50X2FsZXJ0cyIsInN5c3RlbV9oZWFsdGgiLCJvdmVyYWxsX3Njb3JlIiwidXB0aW1lX3BlcmNlbnRhZ2UiLCJhY3RpdmVfc2VydmljZXMiLCJ0b3RhbF9zZXJ2aWNlcyIsInJlc3BvbnNlRGF0YSIsInN1bW1hcnkiLCJvdmVyYWxsX2hlYWx0aCIsImNyaXRpY2FsX2FsZXJ0cyIsImZpbHRlciIsImFsZXJ0Iiwic2V2ZXJpdHkiLCJsZW5ndGgiLCJwcm9wZXJ0aWVzX25lZWRpbmdfYXR0ZW50aW9uIiwic3lzdGVtX3N0YXR1cyIsImdlbmVyYXRlZF9hdCIsImNhY2hlX2R1cmF0aW9uIiwiYWN0aXZlUHJvcGVydGllcyIsInByb3BlcnR5IiwiZmluZE1hbnkiLCJ3aGVyZSIsImlzQWN0aXZlIiwic2VsZWN0IiwidGFrZSIsInByb3BlcnR5RGV0YWlscyIsIlByb21pc2UiLCJhbGxTZXR0bGVkIiwibWFwIiwicHJvcGVydHlfZGV0YWlscyIsInJlc3VsdCIsInZhbHVlIiwiZXJyb3IiLCJHRVQiLCJPUFRJT05TIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/api/dashboard/status/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   comparePassword: () => (/* binding */ comparePassword),\n/* harmony export */   generateToken: () => (/* binding */ generateToken),\n/* harmony export */   getAuthUser: () => (/* binding */ getAuthUser),\n/* harmony export */   getUserRoles: () => (/* binding */ getUserRoles),\n/* harmony export */   hasPermission: () => (/* binding */ hasPermission),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   requireAuth: () => (/* binding */ requireAuth),\n/* harmony export */   requirePermission: () => (/* binding */ requirePermission),\n/* harmony export */   requireRole: () => (/* binding */ requireRole),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\nconst JWT_SECRET = process.env.JWT_SECRET || \"fallback-secret\";\nconst JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || \"7d\";\nfunction generateToken(payload) {\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().sign(payload, JWT_SECRET, {\n        expiresIn: JWT_EXPIRES_IN\n    });\n}\nfunction verifyToken(token) {\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().verify(token, JWT_SECRET);\n}\nasync function hashPassword(password) {\n    return bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().hash(password, 12);\n}\nasync function comparePassword(password, hashedPassword) {\n    return bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().compare(password, hashedPassword);\n}\nasync function getUserRoles(userId) {\n    const userRoles = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.userRole.findMany({\n        where: {\n            userId\n        },\n        include: {\n            role: true\n        }\n    });\n    return userRoles.map((ur)=>ur.role.name);\n}\nasync function getAuthUser(request) {\n    try {\n        const authHeader = request.headers.get(\"authorization\");\n        if (!authHeader || !authHeader.startsWith(\"Bearer \")) {\n            return null;\n        }\n        const token = authHeader.substring(7);\n        const payload = verifyToken(token);\n        const user = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.user.findUnique({\n            where: {\n                id: payload.userId\n            },\n            select: {\n                id: true,\n                email: true,\n                fullName: true,\n                phone: true,\n                isActive: true,\n                createdAt: true\n            }\n        });\n        if (!user || !user.isActive) {\n            return null;\n        }\n        // Get user roles\n        const roles = await getUserRoles(user.id);\n        return {\n            ...user,\n            roles\n        };\n    } catch (error) {\n        return null;\n    }\n}\nfunction requireAuth(handler) {\n    return async (request, context)=>{\n        const user = await getAuthUser(request);\n        if (!user) {\n            return Response.json({\n                success: false,\n                error: \"Unauthorized\",\n                code: \"UNAUTHORIZED\"\n            }, {\n                status: 401\n            });\n        }\n        return handler(request, context, user);\n    };\n}\nfunction requireRole(roles) {\n    return (handler)=>{\n        return async (request, context)=>{\n            const user = await getAuthUser(request);\n            if (!user) {\n                return Response.json({\n                    success: false,\n                    error: \"Unauthorized\",\n                    code: \"UNAUTHORIZED\"\n                }, {\n                    status: 401\n                });\n            }\n            const hasRole = roles.some((role)=>user.roles.includes(role));\n            if (!hasRole) {\n                return Response.json({\n                    success: false,\n                    error: \"Insufficient permissions\",\n                    code: \"FORBIDDEN\"\n                }, {\n                    status: 403\n                });\n            }\n            return handler(request, context, user);\n        };\n    };\n}\nasync function hasPermission(userId, resource, action) {\n    const userRoles = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.userRole.findMany({\n        where: {\n            userId\n        },\n        include: {\n            role: {\n                include: {\n                    rolePermissions: {\n                        include: {\n                            permission: true\n                        }\n                    }\n                }\n            }\n        }\n    });\n    for (const userRole of userRoles){\n        for (const rolePermission of userRole.role.rolePermissions){\n            const permission = rolePermission.permission;\n            if (permission.resource === resource && permission.action === action) {\n                return true;\n            }\n        }\n    }\n    return false;\n}\nfunction requirePermission(resource, action) {\n    return (handler)=>{\n        return async (request, context)=>{\n            const user = await getAuthUser(request);\n            if (!user) {\n                return Response.json({\n                    success: false,\n                    error: \"Unauthorized\",\n                    code: \"UNAUTHORIZED\"\n                }, {\n                    status: 401\n                });\n            }\n            const hasAccess = await hasPermission(user.id, resource, action);\n            if (!hasAccess) {\n                return Response.json({\n                    success: false,\n                    error: \"Insufficient permissions\",\n                    code: \"FORBIDDEN\"\n                }, {\n                    status: 403\n                });\n            }\n            return handler(request, context, user);\n        };\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/business-logic/dashboard-metrics.ts":
/*!*************************************************!*\
  !*** ./lib/business-logic/dashboard-metrics.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateElectricityStatus: () => (/* binding */ calculateElectricityStatus),\n/* harmony export */   calculateInternetStatus: () => (/* binding */ calculateInternetStatus),\n/* harmony export */   calculateMaintenanceStatus: () => (/* binding */ calculateMaintenanceStatus),\n/* harmony export */   calculateOttServicesStatus: () => (/* binding */ calculateOttServicesStatus),\n/* harmony export */   createSafeMetric: () => (/* binding */ createSafeMetric),\n/* harmony export */   generatePropertyDashboardStatus: () => (/* binding */ generatePropertyDashboardStatus),\n/* harmony export */   generateSystemDashboardMetrics: () => (/* binding */ generateSystemDashboardMetrics)\n/* harmony export */ });\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n/* harmony import */ var _generator_calculations__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./generator-calculations */ \"(rsc)/./lib/business-logic/generator-calculations.ts\");\n/**\n * Dashboard metrics calculation and business logic\n * Enhanced version combining V1's rich domain logic with Backend's data processing\n */ \n\n/**\n * Create a safe metric object with formatted display value\n */ function createSafeMetric(name, value, unit, status) {\n    let displayValue;\n    if (typeof value === \"number\") {\n        if (Number.isInteger(value)) {\n            displayValue = `${value}${unit}`;\n        } else {\n            displayValue = `${value.toFixed(1)}${unit}`;\n        }\n    } else {\n        displayValue = `${value}${unit}`;\n    }\n    return {\n        name,\n        value,\n        unit,\n        status,\n        displayValue\n    };\n}\n/**\n * Calculate electricity/generator status for a property\n */ async function calculateElectricityStatus(propertyId) {\n    try {\n        // Get latest generator fuel data\n        const latestFuelLog = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.generatorFuelLog.findFirst({\n            where: {\n                propertyId\n            },\n            orderBy: {\n                recordedAt: \"desc\"\n            }\n        });\n        if (!latestFuelLog) {\n            return {\n                status: \"red\",\n                metrics: [\n                    createSafeMetric(\"fuel\", \"No data\", \"\", \"red\")\n                ],\n                issueCount: 1,\n                lastUpdated: new Date()\n            };\n        }\n        // Calculate generator statistics using business logic\n        const stats = (0,_generator_calculations__WEBPACK_IMPORTED_MODULE_1__.calculateGeneratorStats)({\n            fuelLevelLiters: latestFuelLog.fuelLevelLiters,\n            consumptionRate: latestFuelLog.consumptionRate || undefined,\n            runtimeHours: latestFuelLog.runtimeHours || undefined,\n            efficiencyPercentage: latestFuelLog.efficiencyPercentage || undefined\n        });\n        const fuelStatus = (0,_generator_calculations__WEBPACK_IMPORTED_MODULE_1__.getFuelStatus)(stats.powerBackupHours);\n        const status = fuelStatus === \"critical\" ? \"red\" : fuelStatus === \"warning\" ? \"orange\" : \"green\";\n        return {\n            status,\n            metrics: [\n                createSafeMetric(\"fuel_level\", latestFuelLog.fuelLevelLiters, \" L\", status),\n                createSafeMetric(\"backup_hours\", stats.powerBackupHours, \" hrs\", status),\n                createSafeMetric(\"efficiency\", stats.efficiency, \"%\", \"green\")\n            ],\n            issueCount: status === \"green\" ? 0 : 1,\n            lastUpdated: latestFuelLog.recordedAt\n        };\n    } catch (error) {\n        console.error(`Error calculating electricity status for ${propertyId}:`, error);\n        return {\n            status: \"red\",\n            metrics: [\n                createSafeMetric(\"status\", \"Error\", \"\", \"red\")\n            ],\n            issueCount: 1,\n            lastUpdated: new Date()\n        };\n    }\n}\n/**\n * Calculate internet/uptime status for a property\n */ async function calculateInternetStatus(propertyId) {\n    try {\n        // Get recent uptime reports (last 7 days)\n        const sevenDaysAgo = new Date();\n        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);\n        const uptimeReports = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.uptimeReport.findMany({\n            where: {\n                propertyId,\n                date: {\n                    gte: sevenDaysAgo\n                }\n            },\n            orderBy: {\n                date: \"desc\"\n            }\n        });\n        if (uptimeReports.length === 0) {\n            return {\n                status: \"green\",\n                metrics: [\n                    createSafeMetric(\"connection\", \"Stable\", \"\", \"green\"),\n                    createSafeMetric(\"uptime\", 99.9, \"%\", \"green\")\n                ],\n                issueCount: 0,\n                lastUpdated: new Date()\n            };\n        }\n        // Calculate average uptime\n        const totalReports = uptimeReports.length;\n        const averageUptime = uptimeReports.reduce((sum, report)=>{\n            const uptime = report.uptimePercentage ? parseFloat(report.uptimePercentage.toString()) : 100;\n            return sum + uptime;\n        }, 0) / totalReports;\n        const totalDowntime = uptimeReports.reduce((sum, report)=>sum + (report.downtimeMinutes || 0), 0);\n        const totalIncidents = uptimeReports.reduce((sum, report)=>sum + (report.incidentsCount || 0), 0);\n        // Determine status based on uptime\n        let status = \"green\";\n        if (averageUptime < 97) status = \"red\";\n        else if (averageUptime < 99) status = \"orange\";\n        return {\n            status,\n            metrics: [\n                createSafeMetric(\"weekly_uptime\", averageUptime, \"%\", status),\n                createSafeMetric(\"total_downtime\", totalDowntime, \" mins\", totalDowntime > 60 ? \"orange\" : \"green\"),\n                createSafeMetric(\"incidents\", totalIncidents, \" events\", totalIncidents > 2 ? \"orange\" : \"green\")\n            ],\n            issueCount: status === \"green\" ? 0 : 1,\n            lastUpdated: uptimeReports[0].createdAt\n        };\n    } catch (error) {\n        console.error(`Error calculating internet status for ${propertyId}:`, error);\n        return {\n            status: \"green\",\n            metrics: [\n                createSafeMetric(\"connection\", \"Unknown\", \"\", \"green\")\n            ],\n            issueCount: 0,\n            lastUpdated: new Date()\n        };\n    }\n}\n/**\n * Calculate maintenance status for a property\n */ async function calculateMaintenanceStatus(propertyId) {\n    try {\n        const issues = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.maintenanceIssue.findMany({\n            where: {\n                propertyId\n            },\n            select: {\n                status: true,\n                priority: true\n            }\n        });\n        if (issues.length === 0) {\n            return {\n                status: \"green\",\n                metrics: [\n                    createSafeMetric(\"open_issues\", 0, \" open\", \"green\"),\n                    createSafeMetric(\"total_issues\", 0, \" total\", \"green\")\n                ],\n                issueCount: 0,\n                lastUpdated: new Date()\n            };\n        }\n        const openIssues = issues.filter((issue)=>issue.status === \"OPEN\");\n        const inProgressIssues = issues.filter((issue)=>issue.status === \"IN_PROGRESS\");\n        const criticalIssues = issues.filter((issue)=>issue.priority === \"CRITICAL\" && (issue.status === \"OPEN\" || issue.status === \"IN_PROGRESS\"));\n        const highPriorityOpen = issues.filter((issue)=>issue.priority === \"HIGH\" && issue.status === \"OPEN\");\n        let status = \"green\";\n        let issueCount = 0;\n        if (criticalIssues.length > 0) {\n            status = \"red\";\n            issueCount = criticalIssues.length;\n        } else if (highPriorityOpen.length > 0 || openIssues.length > 3) {\n            status = \"orange\";\n            issueCount = highPriorityOpen.length + Math.max(0, openIssues.length - 3);\n        }\n        return {\n            status,\n            metrics: [\n                createSafeMetric(\"open_issues\", openIssues.length, \" open\", openIssues.length > 0 ? \"red\" : \"green\"),\n                createSafeMetric(\"in_progress\", inProgressIssues.length, \" in progress\", inProgressIssues.length > 0 ? \"orange\" : \"green\"),\n                createSafeMetric(\"critical\", criticalIssues.length, \" critical\", criticalIssues.length > 0 ? \"red\" : \"green\"),\n                createSafeMetric(\"total_issues\", issues.length, \" total\", \"green\")\n            ],\n            issueCount,\n            lastUpdated: new Date()\n        };\n    } catch (error) {\n        console.error(`Error calculating maintenance status for ${propertyId}:`, error);\n        return {\n            status: \"green\",\n            metrics: [\n                createSafeMetric(\"issues\", \"Error\", \"\", \"green\")\n            ],\n            issueCount: 0,\n            lastUpdated: new Date()\n        };\n    }\n}\n/**\n * Calculate OTT services status for a property\n */ async function calculateOttServicesStatus(propertyId) {\n    try {\n        const ottServices = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.ottService.findMany({\n            where: {\n                propertyId\n            }\n        });\n        if (ottServices.length === 0) {\n            return {\n                status: \"green\",\n                metrics: [\n                    createSafeMetric(\"services\", 0, \" services\", \"green\")\n                ],\n                issueCount: 0,\n                lastUpdated: new Date()\n            };\n        }\n        const activeServices = ottServices.filter((service)=>service.status === \"ACTIVE\");\n        let status = \"green\";\n        let issueCount = 0;\n        const today = new Date();\n        for (const service of activeServices){\n            if (service.renewalDate) {\n                const daysUntilRenewal = Math.ceil((service.renewalDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n                if (daysUntilRenewal < 0) {\n                    status = \"red\";\n                    issueCount++;\n                } else if (daysUntilRenewal <= 7 && status !== \"red\") {\n                    status = \"orange\";\n                    issueCount++;\n                }\n            }\n        }\n        const expiredServices = ottServices.filter((service)=>service.status === \"EXPIRED\").length;\n        if (expiredServices > 0 && status === \"green\") {\n            status = \"orange\";\n            issueCount += expiredServices;\n        }\n        return {\n            status,\n            metrics: [\n                createSafeMetric(\"active_services\", activeServices.length, \" active\", \"green\"),\n                createSafeMetric(\"total_services\", ottServices.length, \" total\", \"green\"),\n                createSafeMetric(\"payment_status\", status === \"green\" ? \"Up to date\" : \"Due soon\", \"\", status),\n                createSafeMetric(\"expired\", expiredServices, \" expired\", expiredServices > 0 ? \"orange\" : \"green\")\n            ],\n            issueCount,\n            lastUpdated: new Date()\n        };\n    } catch (error) {\n        console.error(`Error calculating OTT services status for ${propertyId}:`, error);\n        return {\n            status: \"green\",\n            metrics: [\n                createSafeMetric(\"services\", \"Error\", \"\", \"green\")\n            ],\n            issueCount: 0,\n            lastUpdated: new Date()\n        };\n    }\n}\n/**\n * Generate comprehensive property dashboard status\n */ async function generatePropertyDashboardStatus(propertyId) {\n    // Get property information\n    const property = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.property.findUnique({\n        where: {\n            id: propertyId\n        },\n        select: {\n            name: true,\n            type: true\n        }\n    });\n    if (!property) {\n        throw new Error(\"Property not found\");\n    }\n    // Calculate all functional area statuses\n    const [electricity, internet, maintenance, ottServices] = await Promise.all([\n        calculateElectricityStatus(propertyId),\n        calculateInternetStatus(propertyId),\n        calculateMaintenanceStatus(propertyId),\n        calculateOttServicesStatus(propertyId)\n    ]);\n    // Determine overall status\n    const statuses = [\n        electricity.status,\n        internet.status,\n        maintenance.status,\n        ottServices.status\n    ];\n    let overallStatus = \"green\";\n    if (statuses.includes(\"red\")) {\n        overallStatus = \"red\";\n    } else if (statuses.includes(\"orange\")) {\n        overallStatus = \"orange\";\n    }\n    return {\n        id: propertyId,\n        name: property.name,\n        type: property.type.toLowerCase(),\n        overallStatus,\n        functionalAreas: {\n            electricity,\n            internet,\n            maintenance,\n            ott_services: ottServices\n        },\n        lastUpdated: new Date()\n    };\n}\n/**\n * Generate system-wide dashboard metrics\n */ async function generateSystemDashboardMetrics() {\n    // Get property statistics\n    const totalProperties = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.property.count({\n        where: {\n            isActive: true\n        }\n    });\n    const propertyServiceStats = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.propertyService.groupBy({\n        by: [\n            \"status\"\n        ],\n        _count: {\n            status: true\n        }\n    });\n    // Transform property service stats\n    const serviceStatusCounts = {\n        operational: 0,\n        warning: 0,\n        critical: 0,\n        maintenance: 0\n    };\n    propertyServiceStats.forEach((stat)=>{\n        const status = stat.status.toLowerCase();\n        if (status in serviceStatusCounts) {\n            serviceStatusCounts[status] = stat._count.status;\n        }\n    });\n    // Get maintenance statistics\n    const totalMaintenanceIssues = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.maintenanceIssue.count();\n    const maintenanceStats = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.maintenanceIssue.groupBy({\n        by: [\n            \"status\"\n        ],\n        _count: {\n            status: true\n        }\n    });\n    const criticalIssues = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.maintenanceIssue.count({\n        where: {\n            priority: \"CRITICAL\"\n        }\n    });\n    const maintenanceStatusCounts = {\n        open: 0,\n        in_progress: 0,\n        resolved: 0,\n        closed: 0\n    };\n    maintenanceStats.forEach((stat)=>{\n        const status = stat.status.toLowerCase();\n        if (status in maintenanceStatusCounts) {\n            maintenanceStatusCounts[status] = stat._count.status;\n        }\n    });\n    // Get recent alerts\n    const recentAlerts = await generateRecentAlerts();\n    // Calculate system health score\n    const systemHealth = calculateSystemHealthScore(serviceStatusCounts, maintenanceStatusCounts, totalProperties);\n    return {\n        properties: {\n            total: totalProperties,\n            operational: serviceStatusCounts.operational,\n            warning: serviceStatusCounts.warning,\n            critical: serviceStatusCounts.critical\n        },\n        maintenance_issues: {\n            total: totalMaintenanceIssues,\n            open: maintenanceStatusCounts.open,\n            in_progress: maintenanceStatusCounts.in_progress,\n            critical: criticalIssues\n        },\n        recent_alerts: recentAlerts,\n        system_health: systemHealth\n    };\n}\n/**\n * Generate recent alerts from various sources\n */ async function generateRecentAlerts() {\n    const alerts = [];\n    // Critical maintenance issues\n    const criticalIssues = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.maintenanceIssue.findMany({\n        where: {\n            priority: {\n                in: [\n                    \"CRITICAL\",\n                    \"HIGH\"\n                ]\n            },\n            status: {\n                in: [\n                    \"OPEN\",\n                    \"IN_PROGRESS\"\n                ]\n            }\n        },\n        include: {\n            property: {\n                select: {\n                    name: true\n                }\n            }\n        },\n        orderBy: {\n            createdAt: \"desc\"\n        },\n        take: 5\n    });\n    criticalIssues.forEach((issue)=>{\n        alerts.push({\n            id: issue.id,\n            type: \"maintenance\",\n            severity: issue.priority === \"CRITICAL\" ? \"critical\" : \"high\",\n            message: `${issue.priority.toLowerCase()} maintenance issue: ${issue.title}`,\n            property_name: issue.property.name,\n            timestamp: issue.createdAt\n        });\n    });\n    // Low fuel alerts\n    const lowFuelProperties = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.generatorFuelLog.findMany({\n        where: {\n            fuelLevelLiters: {\n                lt: 25\n            }\n        },\n        include: {\n            property: {\n                select: {\n                    name: true\n                }\n            }\n        },\n        orderBy: {\n            recordedAt: \"desc\"\n        },\n        take: 3\n    });\n    lowFuelProperties.forEach((fuelLog)=>{\n        alerts.push({\n            id: fuelLog.id,\n            type: \"fuel\",\n            severity: fuelLog.fuelLevelLiters < 10 ? \"critical\" : \"medium\",\n            message: `Low fuel level: ${fuelLog.fuelLevelLiters}L remaining`,\n            property_name: fuelLog.property.name,\n            timestamp: fuelLog.recordedAt\n        });\n    });\n    // Expired OTT services\n    const expiredServices = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.ottService.findMany({\n        where: {\n            OR: [\n                {\n                    status: \"EXPIRED\"\n                },\n                {\n                    renewalDate: {\n                        lt: new Date()\n                    },\n                    status: \"ACTIVE\"\n                }\n            ]\n        },\n        include: {\n            property: {\n                select: {\n                    name: true\n                }\n            }\n        },\n        orderBy: {\n            updatedAt: \"desc\"\n        },\n        take: 3\n    });\n    expiredServices.forEach((service)=>{\n        alerts.push({\n            id: service.id,\n            type: \"payment\",\n            severity: \"medium\",\n            message: `OTT service payment due: ${service.serviceName}`,\n            property_name: service.property.name,\n            timestamp: service.updatedAt\n        });\n    });\n    return alerts.sort((a, b)=>new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()).slice(0, 8);\n}\n/**\n * Calculate overall system health score\n */ function calculateSystemHealthScore(serviceStatusCounts, maintenanceStatusCounts, totalProperties) {\n    const totalServices = Object.values(serviceStatusCounts).reduce((sum, count)=>sum + count, 0);\n    const activeServices = serviceStatusCounts.operational;\n    const serviceHealthScore = totalServices > 0 ? activeServices / totalServices * 100 : 100;\n    const maintenanceHealthScore = totalProperties > 0 ? Math.max(0, 100 - maintenanceStatusCounts.open * 10 - maintenanceStatusCounts.in_progress * 5) : 100;\n    const overallScore = (serviceHealthScore + maintenanceHealthScore) / 2;\n    const uptimePercentage = Math.max(95, serviceHealthScore); // Assume minimum 95% uptime\n    return {\n        overall_score: Math.round(overallScore * 100) / 100,\n        uptime_percentage: Math.round(uptimePercentage * 100) / 100,\n        active_services: activeServices,\n        total_services: totalServices\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/business-logic/dashboard-metrics.ts\n");

/***/ }),

/***/ "(rsc)/./lib/business-logic/generator-calculations.ts":
/*!******************************************************!*\
  !*** ./lib/business-logic/generator-calculations.ts ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateGeneratorStats: () => (/* binding */ calculateGeneratorStats),\n/* harmony export */   generateFuelInsights: () => (/* binding */ generateFuelInsights),\n/* harmony export */   getFuelStatus: () => (/* binding */ getFuelStatus),\n/* harmony export */   predictFuelConsumption: () => (/* binding */ predictFuelConsumption),\n/* harmony export */   validateFuelLogData: () => (/* binding */ validateFuelLogData)\n/* harmony export */ });\n/**\n * Generator fuel calculation utilities\n * Ported from V1 business logic with improvements\n */ // Constants - should be configurable per property in production\nconst DEFAULT_GENERATOR_CAPACITY = 100; // liters\nconst DEFAULT_CONSUMPTION_RATE = 6.5; // liters per hour\nconst DEFAULT_EFFICIENCY = 85; // percentage\n/**\n * Calculate comprehensive generator statistics\n * Enhanced version of V1's calculateGeneratorStats\n */ function calculateGeneratorStats(fuelData, generatorCapacity = DEFAULT_GENERATOR_CAPACITY) {\n    if (!fuelData) {\n        return {\n            fuelInGenerator: 0,\n            totalFuel: 0,\n            powerBackupHours: 0,\n            consumptionRate: DEFAULT_CONSUMPTION_RATE,\n            efficiency: DEFAULT_EFFICIENCY\n        };\n    }\n    const fuelInGenerator = fuelData.fuelLevelLiters || 0;\n    const totalFuel = fuelInGenerator;\n    // Use provided consumption rate or calculate from runtime data\n    let consumptionRate = fuelData.consumptionRate || DEFAULT_CONSUMPTION_RATE;\n    if (fuelData.runtimeHours && fuelData.runtimeHours > 0) {\n        // Calculate actual consumption rate from runtime data\n        const fuelConsumed = generatorCapacity - fuelInGenerator;\n        consumptionRate = fuelConsumed / fuelData.runtimeHours;\n    }\n    const efficiency = fuelData.efficiencyPercentage || DEFAULT_EFFICIENCY;\n    // Calculate power backup hours with efficiency factor\n    const effectiveConsumptionRate = consumptionRate * (100 / efficiency);\n    const powerBackupHours = totalFuel > 0 ? Math.round(totalFuel / effectiveConsumptionRate * 10) / 10 : 0;\n    return {\n        fuelInGenerator,\n        totalFuel,\n        powerBackupHours,\n        consumptionRate,\n        efficiency\n    };\n}\n/**\n * Determine fuel status based on backup hours\n * Enhanced version with configurable thresholds\n */ function getFuelStatus(backupHours, criticalThreshold = 6, warningThreshold = 9) {\n    if (backupHours < criticalThreshold) return \"critical\";\n    if (backupHours < warningThreshold) return \"warning\";\n    return \"good\";\n}\n/**\n * Calculate fuel consumption prediction\n * New feature not in V1\n */ function predictFuelConsumption(currentFuelLevel, consumptionRate, hoursToPredict = 24) {\n    const fuelConsumedInPeriod = consumptionRate * hoursToPredict;\n    const predictedLevel = Math.max(0, currentFuelLevel - fuelConsumedInPeriod);\n    const hoursUntilEmpty = currentFuelLevel / consumptionRate;\n    const refuelRecommended = hoursUntilEmpty < 12; // Recommend refuel if less than 12 hours\n    return {\n        predictedLevel,\n        hoursUntilEmpty: Math.round(hoursUntilEmpty * 10) / 10,\n        refuelRecommended\n    };\n}\n/**\n * Validate fuel log data\n * Enhanced validation beyond schema validation\n */ function validateFuelLogData(data) {\n    const warnings = [];\n    const errors = [];\n    // Check for impossible values\n    if (data.fuelLevelLiters < 0) {\n        errors.push(\"Fuel level cannot be negative\");\n    }\n    if (data.fuelLevelLiters > DEFAULT_GENERATOR_CAPACITY) {\n        warnings.push(`Fuel level (${data.fuelLevelLiters}L) exceeds typical generator capacity (${DEFAULT_GENERATOR_CAPACITY}L)`);\n    }\n    if (data.consumptionRate && data.consumptionRate > 20) {\n        warnings.push(`Consumption rate (${data.consumptionRate}L/h) seems unusually high`);\n    }\n    if (data.efficiencyPercentage && data.efficiencyPercentage > 100) {\n        errors.push(\"Efficiency percentage cannot exceed 100%\");\n    }\n    if (data.runtimeHours && data.runtimeHours > 24) {\n        warnings.push(`Runtime hours (${data.runtimeHours}h) exceeds 24 hours`);\n    }\n    return {\n        isValid: errors.length === 0,\n        warnings,\n        errors\n    };\n}\n/**\n * Generate fuel log insights\n * New feature for enhanced reporting\n */ function generateFuelInsights(currentLog, previousLogs) {\n    const recommendations = [];\n    if (previousLogs.length === 0) {\n        return {\n            trend: \"stable\",\n            averageConsumption: currentLog.consumptionRate || DEFAULT_CONSUMPTION_RATE,\n            efficiencyTrend: \"stable\",\n            recommendations: [\n                \"Collect more data for trend analysis\"\n            ]\n        };\n    }\n    // Calculate average consumption from previous logs\n    const consumptionRates = previousLogs.filter((log)=>log.consumptionRate && log.consumptionRate > 0).map((log)=>log.consumptionRate);\n    const averageConsumption = consumptionRates.length > 0 ? consumptionRates.reduce((sum, rate)=>sum + rate, 0) / consumptionRates.length : DEFAULT_CONSUMPTION_RATE;\n    // Determine trends\n    const currentConsumption = currentLog.consumptionRate || averageConsumption;\n    const consumptionTrend = currentConsumption > averageConsumption * 1.1 ? \"declining\" : currentConsumption < averageConsumption * 0.9 ? \"improving\" : \"stable\";\n    const efficiencies = previousLogs.filter((log)=>log.efficiencyPercentage && log.efficiencyPercentage > 0).map((log)=>log.efficiencyPercentage);\n    const currentEfficiency = currentLog.efficiencyPercentage || DEFAULT_EFFICIENCY;\n    const averageEfficiency = efficiencies.length > 0 ? efficiencies.reduce((sum, eff)=>sum + eff, 0) / efficiencies.length : DEFAULT_EFFICIENCY;\n    const efficiencyTrend = currentEfficiency > averageEfficiency * 1.05 ? \"improving\" : currentEfficiency < averageEfficiency * 0.95 ? \"declining\" : \"stable\";\n    // Generate recommendations\n    if (consumptionTrend === \"declining\") {\n        recommendations.push(\"Consider generator maintenance - fuel consumption is increasing\");\n    }\n    if (efficiencyTrend === \"declining\") {\n        recommendations.push(\"Generator efficiency is declining - schedule maintenance check\");\n    }\n    if (currentLog.fuelLevelLiters < DEFAULT_GENERATOR_CAPACITY * 0.25) {\n        recommendations.push(\"Fuel level is low - consider refueling soon\");\n    }\n    return {\n        trend: consumptionTrend,\n        averageConsumption: Math.round(averageConsumption * 100) / 100,\n        efficiencyTrend,\n        recommendations\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/business-logic/generator-calculations.ts\n");

/***/ }),

/***/ "(rsc)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcHJpc21hLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE4QztBQUU5QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQVNGLGdCQUFnQkUsTUFBTSxJQUFJLElBQUlILHdEQUFZQSxHQUFHO0FBRW5FLElBQUlJLElBQXlCLEVBQWNILGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsid2VicGFjazovL3Nyc3ItcHJvcGVydHktbWFuYWdlbWVudC1iYWNrZW5kLy4vbGliL3ByaXNtYS50cz85ODIyIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFByaXNtYUNsaWVudCB9IGZyb20gJ0BwcmlzbWEvY2xpZW50JztcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWQ7XG59O1xuXG5leHBvcnQgY29uc3QgcHJpc21hID0gZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA/PyBuZXcgUHJpc21hQ2xpZW50KCk7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID0gcHJpc21hO1xuIl0sIm5hbWVzIjpbIlByaXNtYUNsaWVudCIsImdsb2JhbEZvclByaXNtYSIsImdsb2JhbFRoaXMiLCJwcmlzbWEiLCJwcm9jZXNzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma.ts\n");

/***/ }),

/***/ "(rsc)/./lib/resilience/retry-handler.ts":
/*!*****************************************!*\
  !*** ./lib/resilience/retry-handler.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createResilientHandler: () => (/* binding */ createResilientHandler),\n/* harmony export */   detectRateLimit: () => (/* binding */ detectRateLimit),\n/* harmony export */   resilientHandlers: () => (/* binding */ resilientHandlers),\n/* harmony export */   withApiResilience: () => (/* binding */ withApiResilience),\n/* harmony export */   withBatchResilience: () => (/* binding */ withBatchResilience),\n/* harmony export */   withDatabaseResilience: () => (/* binding */ withDatabaseResilience),\n/* harmony export */   withRetry: () => (/* binding */ withRetry)\n/* harmony export */ });\n/**\n * Enhanced retry and resilience patterns from V1\n * Implements exponential backoff, rate limiting detection, and graceful degradation\n */ /**\n * Enhanced retry mechanism with exponential backoff and jitter\n * Based on V1's proven resilience patterns\n */ async function withRetry(operation, options = {}) {\n    const { maxRetries = 3, baseDelay = 1000, maxDelay = 30000, backoffMultiplier = 2, jitter = true, retryCondition = defaultRetryCondition, onRetry } = options;\n    let lastError;\n    for(let attempt = 0; attempt <= maxRetries; attempt++){\n        try {\n            return await operation();\n        } catch (error) {\n            lastError = error;\n            // Don't retry on the last attempt\n            if (attempt === maxRetries) {\n                break;\n            }\n            // Check if we should retry this error\n            if (!retryCondition(error)) {\n                break;\n            }\n            // Calculate delay with exponential backoff and optional jitter\n            let delay = Math.min(baseDelay * Math.pow(backoffMultiplier, attempt), maxDelay);\n            if (jitter) {\n                // Add random jitter (±25% of delay)\n                const jitterAmount = delay * 0.25;\n                delay += (Math.random() - 0.5) * 2 * jitterAmount;\n            }\n            console.warn(`Operation failed (attempt ${attempt + 1}/${maxRetries + 1}), retrying in ${Math.round(delay)}ms:`, getErrorMessage(error));\n            if (onRetry) {\n                onRetry(attempt + 1, error);\n            }\n            await sleep(delay);\n        }\n    }\n    throw lastError;\n}\n/**\n * Default retry condition based on V1's patterns\n */ function defaultRetryCondition(error) {\n    const rateLimitInfo = detectRateLimit(error);\n    // Always retry rate limits and network errors\n    if (rateLimitInfo.isRateLimited) {\n        return true;\n    }\n    // Retry on specific HTTP status codes\n    if (error.response?.status) {\n        const status = error.response.status;\n        return status >= 500 || status === 429 || status === 408 || status === 503;\n    }\n    // Retry on network errors\n    if (error.code === \"ECONNRESET\" || error.code === \"ETIMEDOUT\" || error.code === \"ENOTFOUND\") {\n        return true;\n    }\n    return false;\n}\n/**\n * Detect rate limiting and parsing errors like V1\n */ function detectRateLimit(error) {\n    const errorMessage = getErrorMessage(error);\n    const lowerMessage = errorMessage.toLowerCase();\n    // Rate limiting detection patterns from V1\n    if (lowerMessage.includes(\"too many requests\") || lowerMessage.includes(\"rate limit\") || lowerMessage.includes(\"429\") || error.response?.status === 429) {\n        const retryAfter = error.response?.headers?.[\"retry-after\"];\n        return {\n            isRateLimited: true,\n            retryAfter: retryAfter ? parseInt(retryAfter) * 1000 : undefined,\n            errorType: \"rate_limit\"\n        };\n    }\n    // JSON parsing errors (common with rate limiting)\n    if (error instanceof SyntaxError || lowerMessage.includes(\"unexpected token\") || lowerMessage.includes(\"json\") || lowerMessage.includes(\"syntaxerror\")) {\n        return {\n            isRateLimited: true,\n            errorType: \"json_parse\"\n        };\n    }\n    // Network errors\n    if (lowerMessage.includes(\"network\") || lowerMessage.includes(\"fetch\") || lowerMessage.includes(\"connection\")) {\n        return {\n            isRateLimited: true,\n            errorType: \"network\"\n        };\n    }\n    return {\n        isRateLimited: false,\n        errorType: \"unknown\"\n    };\n}\n/**\n * Enhanced error handler with V1's resilience patterns\n */ function createResilientHandler(operation, fallbackValue, options = {}) {\n    const { gracefulDegradation = true, logErrors = true, ...retryOptions } = options;\n    return async ()=>{\n        try {\n            return await withRetry(operation, retryOptions);\n        } catch (error) {\n            if (logErrors) {\n                console.error(\"Operation failed after all retries:\", getErrorMessage(error));\n            }\n            if (gracefulDegradation) {\n                console.warn(\"Falling back to default value due to persistent errors\");\n                return fallbackValue;\n            }\n            throw error;\n        }\n    };\n}\n/**\n * Batch operation with individual error handling\n * Based on V1's batch processing patterns\n */ async function withBatchResilience(items, operation, options = {}) {\n    const { batchSize = 10, continueOnError = true, retryOptions = {} } = options;\n    const results = [];\n    // Process in batches to avoid overwhelming the system\n    for(let i = 0; i < items.length; i += batchSize){\n        const batch = items.slice(i, i + batchSize);\n        const batchPromises = batch.map(async (item)=>{\n            try {\n                const result = await withRetry(()=>operation(item), retryOptions);\n                return {\n                    item,\n                    result\n                };\n            } catch (error) {\n                if (continueOnError) {\n                    console.warn(`Batch operation failed for item:`, item, getErrorMessage(error));\n                    return {\n                        item,\n                        error\n                    };\n                }\n                throw error;\n            }\n        });\n        const batchResults = await Promise.all(batchPromises);\n        results.push(...batchResults);\n        // Add small delay between batches to be respectful\n        if (i + batchSize < items.length) {\n            await sleep(100);\n        }\n    }\n    return results;\n}\n/**\n * Database operation wrapper with V1's resilience patterns\n */ async function withDatabaseResilience(operation, fallbackValue) {\n    const retryOptions = {\n        maxRetries: 3,\n        baseDelay: 1000,\n        backoffMultiplier: 2,\n        retryCondition: (error)=>{\n            // Retry on database connection issues\n            if (error.code === \"P1001\" || error.code === \"P1008\" || error.code === \"P1017\") {\n                return true;\n            }\n            // Retry on timeout errors\n            if (error.code === \"P2024\") {\n                return true;\n            }\n            // Use default retry condition for other errors\n            return defaultRetryCondition(error);\n        },\n        onRetry: (attempt, error)=>{\n            console.warn(`Database operation retry ${attempt}:`, error.code || error.message);\n        }\n    };\n    if (fallbackValue !== undefined) {\n        return createResilientHandler(operation, fallbackValue, retryOptions)();\n    }\n    return withRetry(operation, retryOptions);\n}\n/**\n * API call wrapper with rate limiting awareness\n */ async function withApiResilience(apiCall, options = {}) {\n    const { endpoint, fallbackValue, respectRateLimit = true } = options;\n    const retryOptions = {\n        maxRetries: 5,\n        baseDelay: 1000,\n        maxDelay: 60000,\n        retryCondition: (error)=>{\n            const rateLimitInfo = detectRateLimit(error);\n            return rateLimitInfo.isRateLimited || defaultRetryCondition(error);\n        },\n        onRetry: (attempt, error)=>{\n            const rateLimitInfo = detectRateLimit(error);\n            console.warn(`API call retry ${attempt} for ${endpoint || \"unknown endpoint\"}:`, `Type: ${rateLimitInfo.errorType}, Message: ${getErrorMessage(error)}`);\n        }\n    };\n    // Add initial delay to prevent rate limiting (like V1)\n    if (respectRateLimit) {\n        await sleep(Math.random() * 500 + 200); // 200-700ms random delay\n    }\n    if (fallbackValue !== undefined) {\n        return createResilientHandler(apiCall, fallbackValue, retryOptions)();\n    }\n    return withRetry(apiCall, retryOptions);\n}\n/**\n * Utility functions\n */ function sleep(ms) {\n    return new Promise((resolve)=>setTimeout(resolve, ms));\n}\nfunction getErrorMessage(error) {\n    if (typeof error === \"string\") return error;\n    if (error?.message) return error.message;\n    if (error?.response?.data?.message) return error.response.data.message;\n    if (error?.response?.statusText) return error.response.statusText;\n    return \"Unknown error\";\n}\n/**\n * Pre-configured resilient handlers for common operations\n */ const resilientHandlers = {\n    /**\n   * For database queries that should return empty arrays on failure\n   */ queryWithEmptyFallback: (query)=>createResilientHandler(query, [], {\n            maxRetries: 3,\n            gracefulDegradation: true\n        }),\n    /**\n   * For operations that should return null on failure\n   */ queryWithNullFallback: (query)=>createResilientHandler(query, null, {\n            maxRetries: 3,\n            gracefulDegradation: true\n        }),\n    /**\n   * For critical operations that should not fail silently\n   */ criticalOperation: (operation)=>withRetry(operation, {\n            maxRetries: 5,\n            baseDelay: 2000,\n            gracefulDegradation: false\n        })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/resilience/retry-handler.ts\n");

/***/ }),

/***/ "(rsc)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   corsHeaders: () => (/* binding */ corsHeaders),\n/* harmony export */   createApiResponse: () => (/* binding */ createApiResponse),\n/* harmony export */   createPaginationResponse: () => (/* binding */ createPaginationResponse),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatDateTime: () => (/* binding */ formatDateTime),\n/* harmony export */   getQueryParams: () => (/* binding */ getQueryParams),\n/* harmony export */   getRequestBody: () => (/* binding */ getRequestBody),\n/* harmony export */   handleError: () => (/* binding */ handleError),\n/* harmony export */   parseDate: () => (/* binding */ parseDate)\n/* harmony export */ });\nfunction createApiResponse(data, error, code) {\n    return {\n        success: !error,\n        ...data && {\n            data\n        },\n        ...error && {\n            error\n        },\n        ...code && {\n            code\n        }\n    };\n}\nfunction createPaginationResponse(data, page, limit, total) {\n    const pages = Math.ceil(total / limit);\n    return {\n        success: true,\n        data,\n        pagination: {\n            page,\n            limit,\n            total,\n            pages,\n            has_next: page < pages,\n            has_prev: page > 1\n        }\n    };\n}\nfunction getQueryParams(request) {\n    const { searchParams } = new URL(request.url);\n    const params = {};\n    searchParams.forEach((value, key)=>{\n        // Handle numeric values\n        if (!isNaN(Number(value))) {\n            params[key] = Number(value);\n        } else if (value === \"true\" || value === \"false\") {\n            // Handle boolean values\n            params[key] = value === \"true\";\n        } else {\n            params[key] = value;\n        }\n    });\n    return params;\n}\nasync function getRequestBody(request) {\n    try {\n        return await request.json();\n    } catch (error) {\n        return null;\n    }\n}\nfunction handleError(error, defaultMessage = \"Internal server error\", context) {\n    console.error(`API Error${context ? ` (${context})` : \"\"}:`, error);\n    // Rate limiting errors (from V1 patterns)\n    if (isRateLimitError(error)) {\n        return Response.json(createApiResponse(null, \"Rate limit exceeded. Please try again in a moment.\", \"RATE_LIMIT_EXCEEDED\"), {\n            status: 429,\n            headers: {\n                ...corsHeaders(),\n                \"Retry-After\": \"60\"\n            }\n        });\n    }\n    // JSON parsing errors (often related to rate limiting)\n    if (isJsonParsingError(error)) {\n        return Response.json(createApiResponse(null, \"Request parsing failed. Please try again.\", \"PARSING_ERROR\"), {\n            status: 400,\n            headers: corsHeaders()\n        });\n    }\n    // Database constraint errors\n    if (error.code === \"P2002\") {\n        const field = error.meta?.target?.[0] || \"field\";\n        return Response.json(createApiResponse(null, `${field} already exists`, \"DUPLICATE_ENTRY\"), {\n            status: 409,\n            headers: corsHeaders()\n        });\n    }\n    if (error.code === \"P2025\") {\n        return Response.json(createApiResponse(null, \"Resource not found\", \"NOT_FOUND\"), {\n            status: 404,\n            headers: corsHeaders()\n        });\n    }\n    // Database connection errors\n    if (error.code === \"P1001\" || error.code === \"P1008\") {\n        return Response.json(createApiResponse(null, \"Database connection failed. Please try again.\", \"DATABASE_ERROR\"), {\n            status: 503,\n            headers: corsHeaders()\n        });\n    }\n    // Validation errors\n    if (error.name === \"ValidationError\" || error.isJoi) {\n        return Response.json(createApiResponse(null, error.message || \"Validation failed\", \"VALIDATION_ERROR\"), {\n            status: 400,\n            headers: corsHeaders()\n        });\n    }\n    // Network/timeout errors\n    if (error.code === \"ECONNRESET\" || error.code === \"ETIMEDOUT\") {\n        return Response.json(createApiResponse(null, \"Network error. Please try again.\", \"NETWORK_ERROR\"), {\n            status: 503,\n            headers: corsHeaders()\n        });\n    }\n    return Response.json(createApiResponse(null, defaultMessage, \"INTERNAL_ERROR\"), {\n        status: 500,\n        headers: corsHeaders()\n    });\n}\n// Helper functions for error detection (from V1 patterns)\nfunction isRateLimitError(error) {\n    const message = (error?.message || \"\").toLowerCase();\n    return message.includes(\"too many requests\") || message.includes(\"rate limit\") || message.includes(\"429\") || error?.response?.status === 429;\n}\nfunction isJsonParsingError(error) {\n    const message = (error?.message || \"\").toLowerCase();\n    return error instanceof SyntaxError || message.includes(\"unexpected token\") || message.includes(\"json\") || message.includes(\"syntaxerror\");\n}\nfunction corsHeaders() {\n    return {\n        \"Access-Control-Allow-Origin\": \"*\",\n        \"Access-Control-Allow-Methods\": \"GET, POST, PUT, DELETE, OPTIONS\",\n        \"Access-Control-Allow-Headers\": \"Content-Type, Authorization\"\n    };\n}\n// Date utilities\nfunction formatDate(date) {\n    return date.toISOString().split(\"T\")[0];\n}\nfunction formatDateTime(date) {\n    return date.toISOString();\n}\nfunction parseDate(dateString) {\n    return new Date(dateString);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBc0JPLFNBQVNBLGtCQUNkQyxJQUFRLEVBQ1JDLEtBQWMsRUFDZEMsSUFBYTtJQUViLE9BQU87UUFDTEMsU0FBUyxDQUFDRjtRQUNWLEdBQUlELFFBQVE7WUFBRUE7UUFBSyxDQUFDO1FBQ3BCLEdBQUlDLFNBQVM7WUFBRUE7UUFBTSxDQUFDO1FBQ3RCLEdBQUlDLFFBQVE7WUFBRUE7UUFBSyxDQUFDO0lBQ3RCO0FBQ0Y7QUFFTyxTQUFTRSx5QkFDZEosSUFBUyxFQUNUSyxJQUFZLEVBQ1pDLEtBQWEsRUFDYkMsS0FBYTtJQUViLE1BQU1DLFFBQVFDLEtBQUtDLElBQUksQ0FBQ0gsUUFBUUQ7SUFFaEMsT0FBTztRQUNMSCxTQUFTO1FBQ1RIO1FBQ0FXLFlBQVk7WUFDVk47WUFDQUM7WUFDQUM7WUFDQUM7WUFDQUksVUFBVVAsT0FBT0c7WUFDakJLLFVBQVVSLE9BQU87UUFDbkI7SUFDRjtBQUNGO0FBRU8sU0FBU1MsZUFBZUMsT0FBb0I7SUFDakQsTUFBTSxFQUFFQyxZQUFZLEVBQUUsR0FBRyxJQUFJQyxJQUFJRixRQUFRRyxHQUFHO0lBQzVDLE1BQU1DLFNBQThCLENBQUM7SUFFckNILGFBQWFJLE9BQU8sQ0FBQyxDQUFDQyxPQUFPQztRQUMzQix3QkFBd0I7UUFDeEIsSUFBSSxDQUFDQyxNQUFNQyxPQUFPSCxTQUFTO1lBQ3pCRixNQUFNLENBQUNHLElBQUksR0FBR0UsT0FBT0g7UUFDdkIsT0FBTyxJQUFJQSxVQUFVLFVBQVVBLFVBQVUsU0FBUztZQUNoRCx3QkFBd0I7WUFDeEJGLE1BQU0sQ0FBQ0csSUFBSSxHQUFHRCxVQUFVO1FBQzFCLE9BQU87WUFDTEYsTUFBTSxDQUFDRyxJQUFJLEdBQUdEO1FBQ2hCO0lBQ0Y7SUFFQSxPQUFPRjtBQUNUO0FBRU8sZUFBZU0sZUFBZVYsT0FBb0I7SUFDdkQsSUFBSTtRQUNGLE9BQU8sTUFBTUEsUUFBUVcsSUFBSTtJQUMzQixFQUFFLE9BQU96QixPQUFPO1FBQ2QsT0FBTztJQUNUO0FBQ0Y7QUFFTyxTQUFTMEIsWUFBWTFCLEtBQVUsRUFBRTJCLGlCQUFpQix1QkFBdUIsRUFBRUMsT0FBZ0I7SUFDaEdDLFFBQVE3QixLQUFLLENBQUMsQ0FBQyxTQUFTLEVBQUU0QixVQUFVLENBQUMsRUFBRSxFQUFFQSxRQUFRLENBQUMsQ0FBQyxHQUFHLEdBQUcsQ0FBQyxDQUFDLEVBQUU1QjtJQUU3RCwwQ0FBMEM7SUFDMUMsSUFBSThCLGlCQUFpQjlCLFFBQVE7UUFDM0IsT0FBTytCLFNBQVNOLElBQUksQ0FDbEIzQixrQkFBa0IsTUFBTSxzREFBc0Qsd0JBQzlFO1lBQ0VrQyxRQUFRO1lBQ1JDLFNBQVM7Z0JBQ1AsR0FBR0MsYUFBYTtnQkFDaEIsZUFBZTtZQUNqQjtRQUNGO0lBRUo7SUFFQSx1REFBdUQ7SUFDdkQsSUFBSUMsbUJBQW1CbkMsUUFBUTtRQUM3QixPQUFPK0IsU0FBU04sSUFBSSxDQUNsQjNCLGtCQUFrQixNQUFNLDZDQUE2QyxrQkFDckU7WUFBRWtDLFFBQVE7WUFBS0MsU0FBU0M7UUFBYztJQUUxQztJQUVBLDZCQUE2QjtJQUM3QixJQUFJbEMsTUFBTUMsSUFBSSxLQUFLLFNBQVM7UUFDMUIsTUFBTW1DLFFBQVFwQyxNQUFNcUMsSUFBSSxFQUFFQyxRQUFRLENBQUMsRUFBRSxJQUFJO1FBQ3pDLE9BQU9QLFNBQVNOLElBQUksQ0FDbEIzQixrQkFBa0IsTUFBTSxDQUFDLEVBQUVzQyxNQUFNLGVBQWUsQ0FBQyxFQUFFLG9CQUNuRDtZQUFFSixRQUFRO1lBQUtDLFNBQVNDO1FBQWM7SUFFMUM7SUFFQSxJQUFJbEMsTUFBTUMsSUFBSSxLQUFLLFNBQVM7UUFDMUIsT0FBTzhCLFNBQVNOLElBQUksQ0FDbEIzQixrQkFBa0IsTUFBTSxzQkFBc0IsY0FDOUM7WUFBRWtDLFFBQVE7WUFBS0MsU0FBU0M7UUFBYztJQUUxQztJQUVBLDZCQUE2QjtJQUM3QixJQUFJbEMsTUFBTUMsSUFBSSxLQUFLLFdBQVdELE1BQU1DLElBQUksS0FBSyxTQUFTO1FBQ3BELE9BQU84QixTQUFTTixJQUFJLENBQ2xCM0Isa0JBQWtCLE1BQU0saURBQWlELG1CQUN6RTtZQUFFa0MsUUFBUTtZQUFLQyxTQUFTQztRQUFjO0lBRTFDO0lBRUEsb0JBQW9CO0lBQ3BCLElBQUlsQyxNQUFNdUMsSUFBSSxLQUFLLHFCQUFxQnZDLE1BQU13QyxLQUFLLEVBQUU7UUFDbkQsT0FBT1QsU0FBU04sSUFBSSxDQUNsQjNCLGtCQUFrQixNQUFNRSxNQUFNeUMsT0FBTyxJQUFJLHFCQUFxQixxQkFDOUQ7WUFBRVQsUUFBUTtZQUFLQyxTQUFTQztRQUFjO0lBRTFDO0lBRUEseUJBQXlCO0lBQ3pCLElBQUlsQyxNQUFNQyxJQUFJLEtBQUssZ0JBQWdCRCxNQUFNQyxJQUFJLEtBQUssYUFBYTtRQUM3RCxPQUFPOEIsU0FBU04sSUFBSSxDQUNsQjNCLGtCQUFrQixNQUFNLG9DQUFvQyxrQkFDNUQ7WUFBRWtDLFFBQVE7WUFBS0MsU0FBU0M7UUFBYztJQUUxQztJQUVBLE9BQU9ILFNBQVNOLElBQUksQ0FDbEIzQixrQkFBa0IsTUFBTTZCLGdCQUFnQixtQkFDeEM7UUFBRUssUUFBUTtRQUFLQyxTQUFTQztJQUFjO0FBRTFDO0FBRUEsMERBQTBEO0FBQzFELFNBQVNKLGlCQUFpQjlCLEtBQVU7SUFDbEMsTUFBTXlDLFVBQVUsQ0FBQ3pDLE9BQU95QyxXQUFXLEVBQUMsRUFBR0MsV0FBVztJQUNsRCxPQUNFRCxRQUFRRSxRQUFRLENBQUMsd0JBQ2pCRixRQUFRRSxRQUFRLENBQUMsaUJBQ2pCRixRQUFRRSxRQUFRLENBQUMsVUFDakIzQyxPQUFPNEMsVUFBVVosV0FBVztBQUVoQztBQUVBLFNBQVNHLG1CQUFtQm5DLEtBQVU7SUFDcEMsTUFBTXlDLFVBQVUsQ0FBQ3pDLE9BQU95QyxXQUFXLEVBQUMsRUFBR0MsV0FBVztJQUNsRCxPQUNFMUMsaUJBQWlCNkMsZUFDakJKLFFBQVFFLFFBQVEsQ0FBQyx1QkFDakJGLFFBQVFFLFFBQVEsQ0FBQyxXQUNqQkYsUUFBUUUsUUFBUSxDQUFDO0FBRXJCO0FBRU8sU0FBU1Q7SUFDZCxPQUFPO1FBQ0wsK0JBQStCO1FBQy9CLGdDQUFnQztRQUNoQyxnQ0FBZ0M7SUFDbEM7QUFDRjtBQUVBLGlCQUFpQjtBQUNWLFNBQVNZLFdBQVdDLElBQVU7SUFDbkMsT0FBT0EsS0FBS0MsV0FBVyxHQUFHQyxLQUFLLENBQUMsSUFBSSxDQUFDLEVBQUU7QUFDekM7QUFFTyxTQUFTQyxlQUFlSCxJQUFVO0lBQ3ZDLE9BQU9BLEtBQUtDLFdBQVc7QUFDekI7QUFFTyxTQUFTRyxVQUFVQyxVQUFrQjtJQUMxQyxPQUFPLElBQUlDLEtBQUtEO0FBQ2xCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3Jzci1wcm9wZXJ0eS1tYW5hZ2VtZW50LWJhY2tlbmQvLi9saWIvdXRpbHMudHM/Zjc0NSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBOZXh0UmVxdWVzdCB9IGZyb20gJ25leHQvc2VydmVyJztcblxuZXhwb3J0IGludGVyZmFjZSBBcGlSZXNwb25zZTxUID0gYW55PiB7XG4gIHN1Y2Nlc3M6IGJvb2xlYW47XG4gIGRhdGE/OiBUO1xuICBlcnJvcj86IHN0cmluZztcbiAgY29kZT86IHN0cmluZztcbn1cblxuZXhwb3J0IGludGVyZmFjZSBQYWdpbmF0aW9uUmVzcG9uc2U8VCA9IGFueT4ge1xuICBzdWNjZXNzOiBib29sZWFuO1xuICBkYXRhOiBUW107XG4gIHBhZ2luYXRpb246IHtcbiAgICBwYWdlOiBudW1iZXI7XG4gICAgbGltaXQ6IG51bWJlcjtcbiAgICB0b3RhbDogbnVtYmVyO1xuICAgIHBhZ2VzOiBudW1iZXI7XG4gICAgaGFzX25leHQ6IGJvb2xlYW47XG4gICAgaGFzX3ByZXY6IGJvb2xlYW47XG4gIH07XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBjcmVhdGVBcGlSZXNwb25zZTxUPihcbiAgZGF0YT86IFQsXG4gIGVycm9yPzogc3RyaW5nLFxuICBjb2RlPzogc3RyaW5nXG4pOiBBcGlSZXNwb25zZTxUPiB7XG4gIHJldHVybiB7XG4gICAgc3VjY2VzczogIWVycm9yLFxuICAgIC4uLihkYXRhICYmIHsgZGF0YSB9KSxcbiAgICAuLi4oZXJyb3IgJiYgeyBlcnJvciB9KSxcbiAgICAuLi4oY29kZSAmJiB7IGNvZGUgfSksXG4gIH07XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBjcmVhdGVQYWdpbmF0aW9uUmVzcG9uc2U8VD4oXG4gIGRhdGE6IFRbXSxcbiAgcGFnZTogbnVtYmVyLFxuICBsaW1pdDogbnVtYmVyLFxuICB0b3RhbDogbnVtYmVyXG4pOiBQYWdpbmF0aW9uUmVzcG9uc2U8VD4ge1xuICBjb25zdCBwYWdlcyA9IE1hdGguY2VpbCh0b3RhbCAvIGxpbWl0KTtcblxuICByZXR1cm4ge1xuICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgZGF0YSxcbiAgICBwYWdpbmF0aW9uOiB7XG4gICAgICBwYWdlLFxuICAgICAgbGltaXQsXG4gICAgICB0b3RhbCxcbiAgICAgIHBhZ2VzLFxuICAgICAgaGFzX25leHQ6IHBhZ2UgPCBwYWdlcyxcbiAgICAgIGhhc19wcmV2OiBwYWdlID4gMSxcbiAgICB9LFxuICB9O1xufVxuXG5leHBvcnQgZnVuY3Rpb24gZ2V0UXVlcnlQYXJhbXMocmVxdWVzdDogTmV4dFJlcXVlc3QpIHtcbiAgY29uc3QgeyBzZWFyY2hQYXJhbXMgfSA9IG5ldyBVUkwocmVxdWVzdC51cmwpO1xuICBjb25zdCBwYXJhbXM6IFJlY29yZDxzdHJpbmcsIGFueT4gPSB7fTtcblxuICBzZWFyY2hQYXJhbXMuZm9yRWFjaCgodmFsdWUsIGtleSkgPT4ge1xuICAgIC8vIEhhbmRsZSBudW1lcmljIHZhbHVlc1xuICAgIGlmICghaXNOYU4oTnVtYmVyKHZhbHVlKSkpIHtcbiAgICAgIHBhcmFtc1trZXldID0gTnVtYmVyKHZhbHVlKTtcbiAgICB9IGVsc2UgaWYgKHZhbHVlID09PSAndHJ1ZScgfHwgdmFsdWUgPT09ICdmYWxzZScpIHtcbiAgICAgIC8vIEhhbmRsZSBib29sZWFuIHZhbHVlc1xuICAgICAgcGFyYW1zW2tleV0gPSB2YWx1ZSA9PT0gJ3RydWUnO1xuICAgIH0gZWxzZSB7XG4gICAgICBwYXJhbXNba2V5XSA9IHZhbHVlO1xuICAgIH1cbiAgfSk7XG5cbiAgcmV0dXJuIHBhcmFtcztcbn1cblxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGdldFJlcXVlc3RCb2R5KHJlcXVlc3Q6IE5leHRSZXF1ZXN0KSB7XG4gIHRyeSB7XG4gICAgcmV0dXJuIGF3YWl0IHJlcXVlc3QuanNvbigpO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIHJldHVybiBudWxsO1xuICB9XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBoYW5kbGVFcnJvcihlcnJvcjogYW55LCBkZWZhdWx0TWVzc2FnZSA9ICdJbnRlcm5hbCBzZXJ2ZXIgZXJyb3InLCBjb250ZXh0Pzogc3RyaW5nKSB7XG4gIGNvbnNvbGUuZXJyb3IoYEFQSSBFcnJvciR7Y29udGV4dCA/IGAgKCR7Y29udGV4dH0pYCA6ICcnfTpgLCBlcnJvcik7XG5cbiAgLy8gUmF0ZSBsaW1pdGluZyBlcnJvcnMgKGZyb20gVjEgcGF0dGVybnMpXG4gIGlmIChpc1JhdGVMaW1pdEVycm9yKGVycm9yKSkge1xuICAgIHJldHVybiBSZXNwb25zZS5qc29uKFxuICAgICAgY3JlYXRlQXBpUmVzcG9uc2UobnVsbCwgJ1JhdGUgbGltaXQgZXhjZWVkZWQuIFBsZWFzZSB0cnkgYWdhaW4gaW4gYSBtb21lbnQuJywgJ1JBVEVfTElNSVRfRVhDRUVERUQnKSxcbiAgICAgIHtcbiAgICAgICAgc3RhdHVzOiA0MjksXG4gICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAuLi5jb3JzSGVhZGVycygpLFxuICAgICAgICAgICdSZXRyeS1BZnRlcic6ICc2MCcsIC8vIFN1Z2dlc3QgcmV0cnkgYWZ0ZXIgNjAgc2Vjb25kc1xuICAgICAgICB9XG4gICAgICB9XG4gICAgKTtcbiAgfVxuXG4gIC8vIEpTT04gcGFyc2luZyBlcnJvcnMgKG9mdGVuIHJlbGF0ZWQgdG8gcmF0ZSBsaW1pdGluZylcbiAgaWYgKGlzSnNvblBhcnNpbmdFcnJvcihlcnJvcikpIHtcbiAgICByZXR1cm4gUmVzcG9uc2UuanNvbihcbiAgICAgIGNyZWF0ZUFwaVJlc3BvbnNlKG51bGwsICdSZXF1ZXN0IHBhcnNpbmcgZmFpbGVkLiBQbGVhc2UgdHJ5IGFnYWluLicsICdQQVJTSU5HX0VSUk9SJyksXG4gICAgICB7IHN0YXR1czogNDAwLCBoZWFkZXJzOiBjb3JzSGVhZGVycygpIH1cbiAgICApO1xuICB9XG5cbiAgLy8gRGF0YWJhc2UgY29uc3RyYWludCBlcnJvcnNcbiAgaWYgKGVycm9yLmNvZGUgPT09ICdQMjAwMicpIHtcbiAgICBjb25zdCBmaWVsZCA9IGVycm9yLm1ldGE/LnRhcmdldD8uWzBdIHx8ICdmaWVsZCc7XG4gICAgcmV0dXJuIFJlc3BvbnNlLmpzb24oXG4gICAgICBjcmVhdGVBcGlSZXNwb25zZShudWxsLCBgJHtmaWVsZH0gYWxyZWFkeSBleGlzdHNgLCAnRFVQTElDQVRFX0VOVFJZJyksXG4gICAgICB7IHN0YXR1czogNDA5LCBoZWFkZXJzOiBjb3JzSGVhZGVycygpIH1cbiAgICApO1xuICB9XG5cbiAgaWYgKGVycm9yLmNvZGUgPT09ICdQMjAyNScpIHtcbiAgICByZXR1cm4gUmVzcG9uc2UuanNvbihcbiAgICAgIGNyZWF0ZUFwaVJlc3BvbnNlKG51bGwsICdSZXNvdXJjZSBub3QgZm91bmQnLCAnTk9UX0ZPVU5EJyksXG4gICAgICB7IHN0YXR1czogNDA0LCBoZWFkZXJzOiBjb3JzSGVhZGVycygpIH1cbiAgICApO1xuICB9XG5cbiAgLy8gRGF0YWJhc2UgY29ubmVjdGlvbiBlcnJvcnNcbiAgaWYgKGVycm9yLmNvZGUgPT09ICdQMTAwMScgfHwgZXJyb3IuY29kZSA9PT0gJ1AxMDA4Jykge1xuICAgIHJldHVybiBSZXNwb25zZS5qc29uKFxuICAgICAgY3JlYXRlQXBpUmVzcG9uc2UobnVsbCwgJ0RhdGFiYXNlIGNvbm5lY3Rpb24gZmFpbGVkLiBQbGVhc2UgdHJ5IGFnYWluLicsICdEQVRBQkFTRV9FUlJPUicpLFxuICAgICAgeyBzdGF0dXM6IDUwMywgaGVhZGVyczogY29yc0hlYWRlcnMoKSB9XG4gICAgKTtcbiAgfVxuXG4gIC8vIFZhbGlkYXRpb24gZXJyb3JzXG4gIGlmIChlcnJvci5uYW1lID09PSAnVmFsaWRhdGlvbkVycm9yJyB8fCBlcnJvci5pc0pvaSkge1xuICAgIHJldHVybiBSZXNwb25zZS5qc29uKFxuICAgICAgY3JlYXRlQXBpUmVzcG9uc2UobnVsbCwgZXJyb3IubWVzc2FnZSB8fCAnVmFsaWRhdGlvbiBmYWlsZWQnLCAnVkFMSURBVElPTl9FUlJPUicpLFxuICAgICAgeyBzdGF0dXM6IDQwMCwgaGVhZGVyczogY29yc0hlYWRlcnMoKSB9XG4gICAgKTtcbiAgfVxuXG4gIC8vIE5ldHdvcmsvdGltZW91dCBlcnJvcnNcbiAgaWYgKGVycm9yLmNvZGUgPT09ICdFQ09OTlJFU0VUJyB8fCBlcnJvci5jb2RlID09PSAnRVRJTUVET1VUJykge1xuICAgIHJldHVybiBSZXNwb25zZS5qc29uKFxuICAgICAgY3JlYXRlQXBpUmVzcG9uc2UobnVsbCwgJ05ldHdvcmsgZXJyb3IuIFBsZWFzZSB0cnkgYWdhaW4uJywgJ05FVFdPUktfRVJST1InKSxcbiAgICAgIHsgc3RhdHVzOiA1MDMsIGhlYWRlcnM6IGNvcnNIZWFkZXJzKCkgfVxuICAgICk7XG4gIH1cblxuICByZXR1cm4gUmVzcG9uc2UuanNvbihcbiAgICBjcmVhdGVBcGlSZXNwb25zZShudWxsLCBkZWZhdWx0TWVzc2FnZSwgJ0lOVEVSTkFMX0VSUk9SJyksXG4gICAgeyBzdGF0dXM6IDUwMCwgaGVhZGVyczogY29yc0hlYWRlcnMoKSB9XG4gICk7XG59XG5cbi8vIEhlbHBlciBmdW5jdGlvbnMgZm9yIGVycm9yIGRldGVjdGlvbiAoZnJvbSBWMSBwYXR0ZXJucylcbmZ1bmN0aW9uIGlzUmF0ZUxpbWl0RXJyb3IoZXJyb3I6IGFueSk6IGJvb2xlYW4ge1xuICBjb25zdCBtZXNzYWdlID0gKGVycm9yPy5tZXNzYWdlIHx8ICcnKS50b0xvd2VyQ2FzZSgpO1xuICByZXR1cm4gKFxuICAgIG1lc3NhZ2UuaW5jbHVkZXMoJ3RvbyBtYW55IHJlcXVlc3RzJykgfHxcbiAgICBtZXNzYWdlLmluY2x1ZGVzKCdyYXRlIGxpbWl0JykgfHxcbiAgICBtZXNzYWdlLmluY2x1ZGVzKCc0MjknKSB8fFxuICAgIGVycm9yPy5yZXNwb25zZT8uc3RhdHVzID09PSA0MjlcbiAgKTtcbn1cblxuZnVuY3Rpb24gaXNKc29uUGFyc2luZ0Vycm9yKGVycm9yOiBhbnkpOiBib29sZWFuIHtcbiAgY29uc3QgbWVzc2FnZSA9IChlcnJvcj8ubWVzc2FnZSB8fCAnJykudG9Mb3dlckNhc2UoKTtcbiAgcmV0dXJuIChcbiAgICBlcnJvciBpbnN0YW5jZW9mIFN5bnRheEVycm9yIHx8XG4gICAgbWVzc2FnZS5pbmNsdWRlcygndW5leHBlY3RlZCB0b2tlbicpIHx8XG4gICAgbWVzc2FnZS5pbmNsdWRlcygnanNvbicpIHx8XG4gICAgbWVzc2FnZS5pbmNsdWRlcygnc3ludGF4ZXJyb3InKVxuICApO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gY29yc0hlYWRlcnMoKSB7XG4gIHJldHVybiB7XG4gICAgJ0FjY2Vzcy1Db250cm9sLUFsbG93LU9yaWdpbic6ICcqJyxcbiAgICAnQWNjZXNzLUNvbnRyb2wtQWxsb3ctTWV0aG9kcyc6ICdHRVQsIFBPU1QsIFBVVCwgREVMRVRFLCBPUFRJT05TJyxcbiAgICAnQWNjZXNzLUNvbnRyb2wtQWxsb3ctSGVhZGVycyc6ICdDb250ZW50LVR5cGUsIEF1dGhvcml6YXRpb24nLFxuICB9O1xufVxuXG4vLyBEYXRlIHV0aWxpdGllc1xuZXhwb3J0IGZ1bmN0aW9uIGZvcm1hdERhdGUoZGF0ZTogRGF0ZSk6IHN0cmluZyB7XG4gIHJldHVybiBkYXRlLnRvSVNPU3RyaW5nKCkuc3BsaXQoJ1QnKVswXTtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIGZvcm1hdERhdGVUaW1lKGRhdGU6IERhdGUpOiBzdHJpbmcge1xuICByZXR1cm4gZGF0ZS50b0lTT1N0cmluZygpO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gcGFyc2VEYXRlKGRhdGVTdHJpbmc6IHN0cmluZyk6IERhdGUge1xuICByZXR1cm4gbmV3IERhdGUoZGF0ZVN0cmluZyk7XG59XG4iXSwibmFtZXMiOlsiY3JlYXRlQXBpUmVzcG9uc2UiLCJkYXRhIiwiZXJyb3IiLCJjb2RlIiwic3VjY2VzcyIsImNyZWF0ZVBhZ2luYXRpb25SZXNwb25zZSIsInBhZ2UiLCJsaW1pdCIsInRvdGFsIiwicGFnZXMiLCJNYXRoIiwiY2VpbCIsInBhZ2luYXRpb24iLCJoYXNfbmV4dCIsImhhc19wcmV2IiwiZ2V0UXVlcnlQYXJhbXMiLCJyZXF1ZXN0Iiwic2VhcmNoUGFyYW1zIiwiVVJMIiwidXJsIiwicGFyYW1zIiwiZm9yRWFjaCIsInZhbHVlIiwia2V5IiwiaXNOYU4iLCJOdW1iZXIiLCJnZXRSZXF1ZXN0Qm9keSIsImpzb24iLCJoYW5kbGVFcnJvciIsImRlZmF1bHRNZXNzYWdlIiwiY29udGV4dCIsImNvbnNvbGUiLCJpc1JhdGVMaW1pdEVycm9yIiwiUmVzcG9uc2UiLCJzdGF0dXMiLCJoZWFkZXJzIiwiY29yc0hlYWRlcnMiLCJpc0pzb25QYXJzaW5nRXJyb3IiLCJmaWVsZCIsIm1ldGEiLCJ0YXJnZXQiLCJuYW1lIiwiaXNKb2kiLCJtZXNzYWdlIiwidG9Mb3dlckNhc2UiLCJpbmNsdWRlcyIsInJlc3BvbnNlIiwiU3ludGF4RXJyb3IiLCJmb3JtYXREYXRlIiwiZGF0ZSIsInRvSVNPU3RyaW5nIiwic3BsaXQiLCJmb3JtYXREYXRlVGltZSIsInBhcnNlRGF0ZSIsImRhdGVTdHJpbmciLCJEYXRlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./lib/utils.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/semver","vendor-chunks/bcryptjs","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/lodash.once","vendor-chunks/jwa","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdashboard%2Fstatus%2Froute&page=%2Fapi%2Fdashboard%2Fstatus%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fstatus%2Froute.ts&appDir=D%3A%5Cworkspaces%5Cnsl%5Cback%5CSrsrMan%5Cbackend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cworkspaces%5Cnsl%5Cback%5CSrsrMan%5Cbackend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();