const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// Based on actual Flutter frontend screens and existing database roles/permissions
const defaultScreenPermissions = [
  // Auth Screens (public access)
  {
    screenName: 'login',
    requiredPermissions: [],
    allowedRoles: [], // Public access
    isEnabled: true,
  },
  {
    screenName: 'register',
    requiredPermissions: [],
    allowedRoles: [], // Public access
    isEnabled: true,
  },

  // Main App Screens
  {
    screenName: 'dashboard',
    requiredPermissions: ['view_dashboard'],
    allowedRoles: ['admin', 'property_manager', 'maintenance_staff', 'security_guard', 'househelp', 'office_manager', 'site_supervisor'],
    isEnabled: true,
  },
  {
    screenName: 'properties',
    requiredPermissions: ['view_properties'],
    allowedRoles: ['admin', 'property_manager', 'maintenance_staff', 'security_guard', 'househelp'],
    isEnabled: true,
  },
  {
    screenName: 'maintenance',
    requiredPermissions: ['view_maintenance'],
    allowedRoles: ['admin', 'property_manager', 'maintenance_staff', 'househelp'],
    isEnabled: true,
  },
  {
    screenName: 'attendance',
    requiredPermissions: ['view_attendance'],
    allowedRoles: ['admin', 'property_manager'],
    isEnabled: true,
  },
  {
    screenName: 'fuel_monitoring',
    requiredPermissions: ['view_properties'],
    allowedRoles: ['admin', 'property_manager', 'maintenance_staff', 'security_guard', 'househelp'],
    isEnabled: true,
  },
  {
    screenName: 'security',
    requiredPermissions: ['view_security'],
    allowedRoles: ['admin', 'security_guard'],
    isEnabled: true,
  },
  {
    screenName: 'reports',
    requiredPermissions: ['view_reports'],
    allowedRoles: ['admin', 'property_manager'],
    isEnabled: true,
  },

  // Admin Screens
  {
    screenName: 'admin_dashboard',
    requiredPermissions: ['manage_users'],
    allowedRoles: ['admin'],
    isEnabled: true,
  },
  {
    screenName: 'user_management',
    requiredPermissions: ['manage_users'],
    allowedRoles: ['admin'],
    isEnabled: true,
  },
  {
    screenName: 'threshold_config',
    requiredPermissions: ['manage_thresholds'],
    allowedRoles: ['admin'],
    isEnabled: true,
  },
  {
    screenName: 'role_management',
    requiredPermissions: ['manage_roles'],
    allowedRoles: ['admin'],
    isEnabled: true,
  },
  {
    screenName: 'screen_management',
    requiredPermissions: ['manage_permissions'],
    allowedRoles: ['admin'],
    isEnabled: true,
  },
  {
    screenName: 'widget_management',
    requiredPermissions: ['manage_permissions'],
    allowedRoles: ['admin'],
    isEnabled: true,
  },
  {
    screenName: 'permission_config',
    requiredPermissions: ['manage_permissions'],
    allowedRoles: ['admin'],
    isEnabled: true,
  },
];

// Based on actual Flutter frontend widgets identified from ConfigurablePermissionSection usage
const defaultWidgetPermissions = [
  // Dashboard widgets (from dashboard_screen.dart)
  {
    screenName: 'dashboard',
    widgetName: 'property_stats',
    requiredPermissions: ['view_properties'],
    allowedRoles: ['admin', 'property_manager', 'maintenance_staff', 'security_guard', 'househelp'],
    isVisible: true,
    isEnabled: true,
  },
  {
    screenName: 'dashboard',
    widgetName: 'recent_activities',
    requiredPermissions: ['view_dashboard'],
    allowedRoles: ['admin', 'property_manager', 'maintenance_staff', 'security_guard', 'househelp', 'office_manager', 'site_supervisor'],
    isVisible: true,
    isEnabled: true,
  },
  {
    screenName: 'dashboard',
    widgetName: 'maintenance_stats',
    requiredPermissions: ['view_maintenance'],
    allowedRoles: ['admin', 'property_manager', 'maintenance_staff', 'househelp'],
    isVisible: true,
    isEnabled: true,
  },
  {
    screenName: 'dashboard',
    widgetName: 'attendance_summary',
    requiredPermissions: ['view_attendance'],
    allowedRoles: ['admin', 'property_manager'],
    isVisible: true,
    isEnabled: true,
  },
  {
    screenName: 'dashboard',
    widgetName: 'fuel_summary',
    requiredPermissions: ['view_properties'],
    allowedRoles: ['admin', 'property_manager', 'maintenance_staff', 'security_guard', 'househelp'],
    isVisible: true,
    isEnabled: true,
  },
  {
    screenName: 'dashboard',
    widgetName: 'alerts_summary',
    requiredPermissions: ['view_dashboard'],
    allowedRoles: ['admin', 'property_manager', 'maintenance_staff', 'security_guard'],
    isVisible: true,
    isEnabled: true,
  },
  {
    screenName: 'dashboard',
    widgetName: 'security_summary',
    requiredPermissions: ['view_security'],
    allowedRoles: ['admin', 'security_guard'],
    isVisible: true,
    isEnabled: true,
  },

  // Properties widgets
  {
    screenName: 'properties',
    widgetName: 'property_list',
    requiredPermissions: ['view_properties'],
    allowedRoles: ['admin', 'property_manager', 'maintenance_staff', 'security_guard', 'househelp'],
    isVisible: true,
    isEnabled: true,
  },
  {
    screenName: 'properties',
    widgetName: 'property_details',
    requiredPermissions: ['view_properties'],
    allowedRoles: ['admin', 'property_manager', 'maintenance_staff', 'security_guard', 'househelp'],
    isVisible: true,
    isEnabled: true,
  },
  {
    screenName: 'properties',
    widgetName: 'add_property',
    requiredPermissions: ['manage_properties'],
    allowedRoles: ['admin', 'property_manager'],
    isVisible: true,
    isEnabled: true,
  },
  {
    screenName: 'properties',
    widgetName: 'edit_property',
    requiredPermissions: ['manage_properties'],
    allowedRoles: ['admin', 'property_manager'],
    isVisible: true,
    isEnabled: true,
  },
  {
    screenName: 'properties',
    widgetName: 'property_services',
    requiredPermissions: ['view_properties'],
    allowedRoles: ['admin', 'property_manager', 'maintenance_staff', 'security_guard'],
    isVisible: true,
    isEnabled: true,
  },

  // Maintenance widgets
  {
    screenName: 'maintenance',
    widgetName: 'issue_list',
    requiredPermissions: ['view_maintenance'],
    allowedRoles: ['admin', 'property_manager', 'maintenance_staff'],
    isVisible: true,
    isEnabled: true,
  },
  {
    screenName: 'maintenance',
    widgetName: 'create_issue',
    requiredPermissions: ['manage_maintenance'],
    allowedRoles: ['admin', 'property_manager', 'maintenance_staff'],
    isVisible: true,
    isEnabled: true,
  },
  {
    screenName: 'maintenance',
    widgetName: 'assign_issue',
    requiredPermissions: ['manage_maintenance'],
    allowedRoles: ['admin', 'property_manager'],
    isVisible: true,
    isEnabled: true,
  },
  {
    screenName: 'maintenance',
    widgetName: 'issue_details',
    requiredPermissions: ['view_maintenance'],
    allowedRoles: ['admin', 'property_manager', 'maintenance_staff'],
    isVisible: true,
    isEnabled: true,
  },
  {
    screenName: 'maintenance',
    widgetName: 'escalation_controls',
    requiredPermissions: ['manage_maintenance'],
    allowedRoles: ['admin', 'property_manager'],
    isVisible: true,
    isEnabled: true,
  },

  // Attendance widgets
  {
    screenName: 'attendance',
    widgetName: 'attendance_list',
    requiredPermissions: ['view_attendance'],
    allowedRoles: ['admin', 'property_manager'],
    isVisible: true,
    isEnabled: true,
  },
  {
    screenName: 'attendance',
    widgetName: 'mark_attendance',
    requiredPermissions: ['manage_attendance'],
    allowedRoles: ['admin', 'property_manager'],
    isVisible: true,
    isEnabled: true,
  },
  {
    screenName: 'attendance',
    widgetName: 'attendance_reports',
    requiredPermissions: ['view_attendance'],
    allowedRoles: ['admin', 'property_manager'],
    isVisible: true,
    isEnabled: true,
  },
  {
    screenName: 'attendance',
    widgetName: 'attendance_summary',
    requiredPermissions: ['view_attendance'],
    allowedRoles: ['admin', 'property_manager'],
    isVisible: true,
    isEnabled: true,
  },

  // Fuel Monitoring widgets
  {
    screenName: 'fuel_monitoring',
    widgetName: 'fuel_logs',
    requiredPermissions: ['view_properties'],
    allowedRoles: ['admin', 'property_manager', 'maintenance_staff', 'security_guard', 'househelp'],
    isVisible: true,
    isEnabled: true,
  },
  {
    screenName: 'fuel_monitoring',
    widgetName: 'add_fuel_log',
    requiredPermissions: ['manage_properties'],
    allowedRoles: ['admin', 'property_manager'],
    isVisible: true,
    isEnabled: true,
  },
  {
    screenName: 'fuel_monitoring',
    widgetName: 'fuel_analytics',
    requiredPermissions: ['view_properties'],
    allowedRoles: ['admin', 'property_manager', 'maintenance_staff', 'security_guard', 'househelp'],
    isVisible: true,
    isEnabled: true,
  },
  {
    screenName: 'fuel_monitoring',
    widgetName: 'diesel_additions',
    requiredPermissions: ['manage_properties'],
    allowedRoles: ['admin', 'property_manager'],
    isVisible: true,
    isEnabled: true,
  },

  // Security widgets
  {
    screenName: 'security',
    widgetName: 'security_logs',
    requiredPermissions: ['view_security'],
    allowedRoles: ['admin', 'security_guard'],
    isVisible: true,
    isEnabled: true,
  },
  {
    screenName: 'security',
    widgetName: 'incident_reports',
    requiredPermissions: ['manage_security'],
    allowedRoles: ['admin', 'security_guard'],
    isVisible: true,
    isEnabled: true,
  },

  // Reports widgets
  {
    screenName: 'reports',
    widgetName: 'property_reports',
    requiredPermissions: ['view_reports'],
    allowedRoles: ['admin', 'property_manager'],
    isVisible: true,
    isEnabled: true,
  },
  {
    screenName: 'reports',
    widgetName: 'maintenance_reports',
    requiredPermissions: ['view_reports'],
    allowedRoles: ['admin', 'property_manager'],
    isVisible: true,
    isEnabled: true,
  },

  // Admin Dashboard widgets
  {
    screenName: 'admin_dashboard',
    widgetName: 'user_stats',
    requiredPermissions: ['manage_users'],
    allowedRoles: ['admin'],
    isVisible: true,
    isEnabled: true,
  },
  {
    screenName: 'admin_dashboard',
    widgetName: 'system_health',
    requiredPermissions: ['manage_users'],
    allowedRoles: ['admin'],
    isVisible: true,
    isEnabled: true,
  },
  {
    screenName: 'admin_dashboard',
    widgetName: 'threshold_alerts',
    requiredPermissions: ['manage_thresholds'],
    allowedRoles: ['admin'],
    isVisible: true,
    isEnabled: true,
  },
  {
    screenName: 'admin_dashboard',
    widgetName: 'quick_actions',
    requiredPermissions: ['manage_users'],
    allowedRoles: ['admin'],
    isVisible: true,
    isEnabled: true,
  },

  // User Management widgets
  {
    screenName: 'user_management',
    widgetName: 'user_list',
    requiredPermissions: ['manage_users'],
    allowedRoles: ['admin'],
    isVisible: true,
    isEnabled: true,
  },
  {
    screenName: 'user_management',
    widgetName: 'create_user',
    requiredPermissions: ['manage_users'],
    allowedRoles: ['admin'],
    isVisible: true,
    isEnabled: true,
  },
  {
    screenName: 'user_management',
    widgetName: 'user_roles',
    requiredPermissions: ['manage_users'],
    allowedRoles: ['admin'],
    isVisible: true,
    isEnabled: true,
  },
  {
    screenName: 'user_management',
    widgetName: 'user_permissions',
    requiredPermissions: ['manage_users'],
    allowedRoles: ['admin'],
    isVisible: true,
    isEnabled: true,
  },

  // Role Management widgets
  {
    screenName: 'role_management',
    widgetName: 'role_list',
    requiredPermissions: ['manage_roles'],
    allowedRoles: ['admin'],
    isVisible: true,
    isEnabled: true,
  },
  {
    screenName: 'role_management',
    widgetName: 'create_role',
    requiredPermissions: ['manage_roles'],
    allowedRoles: ['admin'],
    isVisible: true,
    isEnabled: true,
  },
  {
    screenName: 'role_management',
    widgetName: 'role_permissions',
    requiredPermissions: ['manage_roles'],
    allowedRoles: ['admin'],
    isVisible: true,
    isEnabled: true,
  },

  // Threshold Config widgets
  {
    screenName: 'threshold_config',
    widgetName: 'threshold_list',
    requiredPermissions: ['manage_thresholds'],
    allowedRoles: ['admin', 'property_manager'],
    isVisible: true,
    isEnabled: true,
  },
  {
    screenName: 'threshold_config',
    widgetName: 'create_threshold',
    requiredPermissions: ['manage_thresholds'],
    allowedRoles: ['admin', 'property_manager'],
    isVisible: true,
    isEnabled: true,
  },
  {
    screenName: 'threshold_config',
    widgetName: 'threshold_testing',
    requiredPermissions: ['manage_thresholds'],
    allowedRoles: ['admin', 'property_manager'],
    isVisible: true,
    isEnabled: true,
  },

  // Screen Management widgets
  {
    screenName: 'screen_management',
    widgetName: 'screen_list',
    requiredPermissions: ['manage_permissions'],
    allowedRoles: ['admin'],
    isVisible: true,
    isEnabled: true,
  },
  {
    screenName: 'screen_management',
    widgetName: 'screen_permissions',
    requiredPermissions: ['manage_permissions'],
    allowedRoles: ['admin'],
    isVisible: true,
    isEnabled: true,
  },

  // Widget Management widgets
  {
    screenName: 'widget_management',
    widgetName: 'widget_list',
    requiredPermissions: ['manage_permissions'],
    allowedRoles: ['admin'],
    isVisible: true,
    isEnabled: true,
  },
  {
    screenName: 'widget_management',
    widgetName: 'widget_builder',
    requiredPermissions: ['manage_permissions'],
    allowedRoles: ['admin'],
    isVisible: true,
    isEnabled: true,
  },
  {
    screenName: 'widget_management',
    widgetName: 'widget_permissions',
    requiredPermissions: ['manage_permissions'],
    allowedRoles: ['admin'],
    isVisible: true,
    isEnabled: true,
  },

  // Permission Config widgets
  {
    screenName: 'permission_config',
    widgetName: 'permission_overview',
    requiredPermissions: ['manage_permissions'],
    allowedRoles: ['admin'],
    isVisible: true,
    isEnabled: true,
  },
  {
    screenName: 'permission_config',
    widgetName: 'export_import',
    requiredPermissions: ['manage_permissions'],
    allowedRoles: ['admin'],
    isVisible: true,
    isEnabled: true,
  },

  // Navigation drawer items (role-based widgets)
  {
    screenName: 'navigation',
    widgetName: 'dashboard_nav',
    requiredPermissions: ['view_dashboard'],
    allowedRoles: ['admin', 'property_manager', 'maintenance_staff', 'security_guard', 'househelp', 'office_manager', 'site_supervisor'],
    isVisible: true,
    isEnabled: true,
  },
  {
    screenName: 'navigation',
    widgetName: 'properties_nav',
    requiredPermissions: ['view_properties'],
    allowedRoles: ['admin', 'property_manager', 'maintenance_staff', 'security_guard', 'househelp'],
    isVisible: true,
    isEnabled: true,
  },
  {
    screenName: 'navigation',
    widgetName: 'maintenance_nav',
    requiredPermissions: ['view_maintenance'],
    allowedRoles: ['admin', 'property_manager', 'maintenance_staff', 'househelp'],
    isVisible: true,
    isEnabled: true,
  },
  {
    screenName: 'navigation',
    widgetName: 'attendance_nav',
    requiredPermissions: ['view_attendance'],
    allowedRoles: ['admin', 'property_manager'],
    isVisible: true,
    isEnabled: true,
  },
  {
    screenName: 'navigation',
    widgetName: 'fuel_nav',
    requiredPermissions: ['view_properties'],
    allowedRoles: ['admin', 'property_manager', 'maintenance_staff', 'security_guard', 'househelp'],
    isVisible: true,
    isEnabled: true,
  },
  {
    screenName: 'navigation',
    widgetName: 'admin_nav',
    requiredPermissions: ['manage_users'],
    allowedRoles: ['admin'],
    isVisible: true,
    isEnabled: true,
  },
  {
    screenName: 'navigation',
    widgetName: 'security_nav',
    requiredPermissions: ['view_security'],
    allowedRoles: ['admin', 'security_guard'],
    isVisible: true,
    isEnabled: true,
  },
  {
    screenName: 'navigation',
    widgetName: 'reports_nav',
    requiredPermissions: ['view_reports'],
    allowedRoles: ['admin', 'property_manager'],
    isVisible: true,
    isEnabled: true,
  },
];

async function seedPermissions() {
  try {
    console.log('Seeding default permission configurations...');

    // Seed screen permissions
    console.log('Creating screen permissions...');
    for (const screenPermission of defaultScreenPermissions) {
      await prisma.screenPermission.upsert({
        where: { screenName: screenPermission.screenName },
        update: screenPermission,
        create: screenPermission,
      });
      console.log(`✓ Screen permission: ${screenPermission.screenName}`);
    }

    // Seed widget permissions
    console.log('Creating widget permissions...');
    for (const widgetPermission of defaultWidgetPermissions) {
      await prisma.widgetPermission.upsert({
        where: {
          screenName_widgetName: {
            screenName: widgetPermission.screenName,
            widgetName: widgetPermission.widgetName,
          },
        },
        update: widgetPermission,
        create: widgetPermission,
      });
      console.log(`✓ Widget permission: ${widgetPermission.screenName}.${widgetPermission.widgetName}`);
    }

    console.log('✅ Permission configurations seeded successfully!');
    console.log(`📊 Created ${defaultScreenPermissions.length} screen permissions`);
    console.log(`📊 Created ${defaultWidgetPermissions.length} widget permissions`);
  } catch (error) {
    console.error('❌ Error seeding permissions:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seed function
if (require.main === module) {
  seedPermissions()
    .then(() => {
      console.log('🎉 Seeding completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Seeding failed:', error);
      process.exit(1);
    });
}

module.exports = { seedPermissions };
