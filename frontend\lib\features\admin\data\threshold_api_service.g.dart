// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'threshold_api_service.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ThresholdConfig _$ThresholdConfigFromJson(Map<String, dynamic> json) =>
    ThresholdConfig(
      id: json['id'] as String,
      serviceType: json['service_type'] as String,
      metricName: json['metric_name'] as String,
      warningThreshold: _doubleFromJson(json['warning_threshold']),
      criticalThreshold: _doubleFromJson(json['critical_threshold']),
      unit: json['unit'] as String,
      description: json['description'] as String,
      isActive: json['is_active'] as bool,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$ThresholdConfigToJson(ThresholdConfig instance) =>
    <String, dynamic>{
      'id': instance.id,
      'service_type': instance.serviceType,
      'metric_name': instance.metricName,
      'warning_threshold': instance.warningThreshold,
      'critical_threshold': instance.criticalThreshold,
      'unit': instance.unit,
      'description': instance.description,
      'is_active': instance.isActive,
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt.toIso8601String(),
    };

CreateThresholdRequest _$CreateThresholdRequestFromJson(
        Map<String, dynamic> json) =>
    CreateThresholdRequest(
      serviceType: json['service_type'] as String,
      metricName: json['metric_name'] as String,
      warningThreshold: (json['warning_threshold'] as num).toDouble(),
      criticalThreshold: (json['critical_threshold'] as num).toDouble(),
      unit: json['unit'] as String,
      description: json['description'] as String,
      isActive: json['is_active'] as bool?,
    );

Map<String, dynamic> _$CreateThresholdRequestToJson(
        CreateThresholdRequest instance) =>
    <String, dynamic>{
      'service_type': instance.serviceType,
      'metric_name': instance.metricName,
      'warning_threshold': instance.warningThreshold,
      'critical_threshold': instance.criticalThreshold,
      'unit': instance.unit,
      'description': instance.description,
      'is_active': instance.isActive,
    };

UpdateThresholdRequest _$UpdateThresholdRequestFromJson(
        Map<String, dynamic> json) =>
    UpdateThresholdRequest(
      serviceType: json['service_type'] as String?,
      metricName: json['metric_name'] as String?,
      warningThreshold: (json['warning_threshold'] as num?)?.toDouble(),
      criticalThreshold: (json['critical_threshold'] as num?)?.toDouble(),
      unit: json['unit'] as String?,
      description: json['description'] as String?,
      isActive: json['is_active'] as bool?,
    );

Map<String, dynamic> _$UpdateThresholdRequestToJson(
        UpdateThresholdRequest instance) =>
    <String, dynamic>{
      'service_type': instance.serviceType,
      'metric_name': instance.metricName,
      'warning_threshold': instance.warningThreshold,
      'critical_threshold': instance.criticalThreshold,
      'unit': instance.unit,
      'description': instance.description,
      'is_active': instance.isActive,
    };

BulkCreateThresholdsRequest _$BulkCreateThresholdsRequestFromJson(
        Map<String, dynamic> json) =>
    BulkCreateThresholdsRequest(
      thresholds: (json['thresholds'] as List<dynamic>)
          .map(
              (e) => CreateThresholdRequest.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$BulkCreateThresholdsRequestToJson(
        BulkCreateThresholdsRequest instance) =>
    <String, dynamic>{
      'thresholds': instance.thresholds,
    };

BulkUpdateThresholdsRequest _$BulkUpdateThresholdsRequestFromJson(
        Map<String, dynamic> json) =>
    BulkUpdateThresholdsRequest(
      thresholds: (json['thresholds'] as List<dynamic>)
          .map((e) => ThresholdUpdateItem.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$BulkUpdateThresholdsRequestToJson(
        BulkUpdateThresholdsRequest instance) =>
    <String, dynamic>{
      'thresholds': instance.thresholds,
    };

ThresholdUpdateItem _$ThresholdUpdateItemFromJson(Map<String, dynamic> json) =>
    ThresholdUpdateItem(
      id: json['id'] as String,
      data:
          UpdateThresholdRequest.fromJson(json['data'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$ThresholdUpdateItemToJson(
        ThresholdUpdateItem instance) =>
    <String, dynamic>{
      'id': instance.id,
      'data': instance.data,
    };

VoidResponse _$VoidResponseFromJson(Map<String, dynamic> json) => VoidResponse(
      message: json['message'] as String,
    );

Map<String, dynamic> _$VoidResponseToJson(VoidResponse instance) =>
    <String, dynamic>{
      'message': instance.message,
    };

// **************************************************************************
// RetrofitGenerator
// **************************************************************************

// ignore_for_file: unnecessary_brace_in_string_interps,no_leading_underscores_for_local_identifiers,unused_element,unnecessary_string_interpolations

class _ThresholdApiService implements ThresholdApiService {
  _ThresholdApiService(
    this._dio, {
    this.baseUrl,
    this.errorLogger,
  });

  final Dio _dio;

  String? baseUrl;

  final ParseErrorLogger? errorLogger;

  @override
  Future<ApiResponse<List<ThresholdConfig>>> getThresholds({
    String? functionalArea,
    String? propertyType,
    String? metricName,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'functional_area': functionalArea,
      r'property_type': propertyType,
      r'metric_name': metricName,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<ApiResponse<List<ThresholdConfig>>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/api/thresholds',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<List<ThresholdConfig>> _value;
    try {
      _value = ApiResponse<List<ThresholdConfig>>.fromJson(
        _result.data!,
        (json) => json is List<dynamic>
            ? json
                .map<ThresholdConfig>(
                    (i) => ThresholdConfig.fromJson(i as Map<String, dynamic>))
                .toList()
            : List.empty(),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<ThresholdConfig>> createThreshold(
      CreateThresholdRequest request) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(request.toJson());
    final _options = _setStreamType<ApiResponse<ThresholdConfig>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/api/thresholds',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<ThresholdConfig> _value;
    try {
      _value = ApiResponse<ThresholdConfig>.fromJson(
        _result.data!,
        (json) => ThresholdConfig.fromJson(json as Map<String, dynamic>),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<ThresholdConfig>> getThresholdById(String id) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<ApiResponse<ThresholdConfig>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/api/thresholds/${id}',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<ThresholdConfig> _value;
    try {
      _value = ApiResponse<ThresholdConfig>.fromJson(
        _result.data!,
        (json) => ThresholdConfig.fromJson(json as Map<String, dynamic>),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<ThresholdConfig>> updateThreshold(
    String id,
    UpdateThresholdRequest request,
  ) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(request.toJson());
    final _options = _setStreamType<ApiResponse<ThresholdConfig>>(Options(
      method: 'PUT',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/api/thresholds/${id}',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<ThresholdConfig> _value;
    try {
      _value = ApiResponse<ThresholdConfig>.fromJson(
        _result.data!,
        (json) => ThresholdConfig.fromJson(json as Map<String, dynamic>),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<VoidResponse>> deleteThreshold(String id) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<ApiResponse<VoidResponse>>(Options(
      method: 'DELETE',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/api/thresholds/${id}',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<VoidResponse> _value;
    try {
      _value = ApiResponse<VoidResponse>.fromJson(
        _result.data!,
        (json) => VoidResponse.fromJson(json as Map<String, dynamic>),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<List<ThresholdConfig>>> bulkCreateThresholds(
      BulkCreateThresholdsRequest request) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(request.toJson());
    final _options = _setStreamType<ApiResponse<List<ThresholdConfig>>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/api/thresholds/bulk',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<List<ThresholdConfig>> _value;
    try {
      _value = ApiResponse<List<ThresholdConfig>>.fromJson(
        _result.data!,
        (json) => json is List<dynamic>
            ? json
                .map<ThresholdConfig>(
                    (i) => ThresholdConfig.fromJson(i as Map<String, dynamic>))
                .toList()
            : List.empty(),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<List<ThresholdConfig>>> bulkUpdateThresholds(
      BulkUpdateThresholdsRequest request) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(request.toJson());
    final _options = _setStreamType<ApiResponse<List<ThresholdConfig>>>(Options(
      method: 'PUT',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/api/thresholds/bulk',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<List<ThresholdConfig>> _value;
    try {
      _value = ApiResponse<List<ThresholdConfig>>.fromJson(
        _result.data!,
        (json) => json is List<dynamic>
            ? json
                .map<ThresholdConfig>(
                    (i) => ThresholdConfig.fromJson(i as Map<String, dynamic>))
                .toList()
            : List.empty(),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  RequestOptions _setStreamType<T>(RequestOptions requestOptions) {
    if (T != dynamic &&
        !(requestOptions.responseType == ResponseType.bytes ||
            requestOptions.responseType == ResponseType.stream)) {
      if (T == String) {
        requestOptions.responseType = ResponseType.plain;
      } else {
        requestOptions.responseType = ResponseType.json;
      }
    }
    return requestOptions;
  }

  String _combineBaseUrls(
    String dioBaseUrl,
    String? baseUrl,
  ) {
    if (baseUrl == null || baseUrl.trim().isEmpty) {
      return dioBaseUrl;
    }

    final url = Uri.parse(baseUrl);

    if (url.isAbsolute) {
      return url.toString();
    }

    return Uri.parse(dioBaseUrl).resolveUri(url).toString();
  }
}
