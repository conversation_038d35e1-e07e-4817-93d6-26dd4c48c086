import 'package:json_annotation/json_annotation.dart';
import 'package:flutter/material.dart';
import '../../core/auth/models/user_role.dart';

part 'user.g.dart';

// Custom converter for roles that can be either strings or objects
class RolesConverter implements JsonConverter<List<String>, dynamic> {
  const RolesConverter();

  @override
  List<String> fromJson(dynamic json) {
    if (json == null) return [];

    if (json is List) {
      return json.map((item) {
        if (item is String) {
          return item;
        } else if (item is Map<String, dynamic>) {
          return item['name'] as String? ?? item['id'] as String? ?? 'unknown';
        }
        return item.toString();
      }).toList();
    }

    return [];
  }

  @override
  dynamic toJson(List<String> object) => object;
}

@JsonSerializable()
class User {
  final String id;
  final String email;
  @JsonKey(name: 'full_name')
  final String fullName;
  final String? username;
  @J<PERSON><PERSON><PERSON>(name: 'mobile_number')
  final String? mobileNumber;
  final String? phone;
  @JsonKey(name: 'primary_role')
  final String? primaryRole;
  @RolesConverter()
  final List<String> roles;
  final List<String> permissions;
  @JsonKey(name: 'is_active')
  final bool isActive;
  @JsonKey(name: 'is_approved')
  final bool isApproved;
  @JsonKey(name: 'approved_by')
  final String? approvedBy;
  @JsonKey(name: 'approved_at')
  final DateTime? approvedAt;
  @JsonKey(name: 'last_login')
  final DateTime? lastLogin;
  @JsonKey(name: 'created_at')
  final DateTime createdAt;
  @JsonKey(name: 'approval_status')
  final String? approvalStatus;

  const User({
    required this.id,
    required this.email,
    required this.fullName,
    this.username,
    this.mobileNumber,
    this.phone,
    this.primaryRole,
    required this.roles,
    this.permissions = const [],
    required this.isActive,
    this.isApproved = false,
    this.approvedBy,
    this.approvedAt,
    this.lastLogin,
    required this.createdAt,
    this.approvalStatus,
  });

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);
  Map<String, dynamic> toJson() => _$UserToJson(this);

  User copyWith({
    String? id,
    String? email,
    String? fullName,
    String? username,
    String? mobileNumber,
    String? phone,
    String? primaryRole,
    List<String>? roles,
    List<String>? permissions,
    bool? isActive,
    bool? isApproved,
    String? approvedBy,
    DateTime? approvedAt,
    DateTime? lastLogin,
    DateTime? createdAt,
    String? approvalStatus,
  }) {
    return User(
      id: id ?? this.id,
      email: email ?? this.email,
      fullName: fullName ?? this.fullName,
      username: username ?? this.username,
      mobileNumber: mobileNumber ?? this.mobileNumber,
      phone: phone ?? this.phone,
      primaryRole: primaryRole ?? this.primaryRole,
      roles: roles ?? this.roles,
      permissions: permissions ?? this.permissions,
      isActive: isActive ?? this.isActive,
      isApproved: isApproved ?? this.isApproved,
      approvedBy: approvedBy ?? this.approvedBy,
      approvedAt: approvedAt ?? this.approvedAt,
      lastLogin: lastLogin ?? this.lastLogin,
      createdAt: createdAt ?? this.createdAt,
      approvalStatus: approvalStatus ?? this.approvalStatus,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is User && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'User(id: $id, email: $email, fullName: $fullName, username: $username, mobileNumber: $mobileNumber, phone: $phone, primaryRole: $primaryRole, roles: $roles, isActive: $isActive, isApproved: $isApproved, createdAt: $createdAt)';
  }

  // Business logic methods
  UserRole? get userRole {
    // First try to use primaryRole if available
    String? roleToCheck = primaryRole;

    // If primaryRole is null, use the first role from roles array
    if (roleToCheck == null && roles.isNotEmpty) {
      roleToCheck = roles.first;
    }

    if (roleToCheck == null) return UserRole.viewer;

    switch (roleToCheck.toLowerCase()) {
      case 'admin':
        return UserRole.admin;
      case 'manager':
      case 'property_manager':
        return UserRole.manager;
      case 'security':
      case 'security_guard':
        return UserRole.security;
      case 'maintenance':
      case 'maintenance_staff':
        return UserRole.maintenance;
      case 'viewer':
      case 'househelp':
        return UserRole.viewer;
      default:
        return UserRole.viewer;
    }
  }

  // Alias for userRole for compatibility
  UserRole? get role => userRole;

  bool get isAdmin => userRole == UserRole.admin;
  bool get isManager => userRole == UserRole.manager;
  bool get isSecurity => userRole == UserRole.security;
  bool get isMaintenance => userRole == UserRole.maintenance;
  bool get isViewer => userRole == UserRole.viewer;

  bool get canLogin => isActive && (isApproved || approvalStatus == null);
  bool get needsApproval => !isApproved;
  bool get isDeactivated => !isActive;

  String get displayName => fullName.isNotEmpty ? fullName : email;
  String get name => fullName; // Alias for compatibility
  String get initials {
    if (fullName.isNotEmpty) {
      final parts = fullName.split(' ');
      if (parts.length >= 2) {
        return '${parts[0][0]}${parts[1][0]}'.toUpperCase();
      } else {
        return fullName[0].toUpperCase();
      }
    }
    return email[0].toUpperCase();
  }

  Color get roleColor => userRole?.primaryColor ?? Colors.grey;
  IconData get roleIcon => userRole?.icon ?? Icons.person;
  String get roleDisplayName => userRole?.name ?? 'Unknown';

  bool hasPermission(String permission) {
    // Check explicit permissions first
    if (permissions.contains(permission)) {
      return true;
    }

    // Fall back to role-based permissions
    return userRole?.hasPermission(permission) ?? false;
  }

  bool canAccessScreen(String screenPath) {
    return userRole?.canAccessScreen(screenPath) ?? false;
  }

  List<BottomNavigationBarItem> getBottomNavItems() {
    final items = userRole?.getBottomNavItems() ?? [
      const BottomNavigationBarItem(
        icon: Icon(Icons.dashboard),
        label: 'Dashboard',
      ),
    ];

    // Ensure minimum 2 items for BottomNavigationBar requirement
    if (items.length < 2) {
      final fallbackItems = [
        const BottomNavigationBarItem(
          icon: Icon(Icons.dashboard),
          label: 'Dashboard',
        ),
        const BottomNavigationBarItem(
          icon: Icon(Icons.person),
          label: 'Profile',
        ),
      ];
      return fallbackItems;
    }

    return items;
  }

  ThemeData getThemeData(BuildContext context) {
    return userRole?.getThemeData(context) ?? Theme.of(context);
  }

  String get statusDescription {
    if (!isActive) return 'Deactivated';
    if (!isApproved) return 'Pending Approval';
    return 'Active';
  }

  Color get statusColor {
    if (!isActive) return Colors.red;
    if (!isApproved) return Colors.orange;
    return Colors.green;
  }

  Duration? get timeSinceLastLogin {
    if (lastLogin == null) return null;
    return DateTime.now().difference(lastLogin!);
  }

  String get lastLoginDescription {
    if (lastLogin == null) return 'Never logged in';

    final duration = timeSinceLastLogin!;
    if (duration.inDays > 0) {
      return '${duration.inDays} days ago';
    } else if (duration.inHours > 0) {
      return '${duration.inHours} hours ago';
    } else if (duration.inMinutes > 0) {
      return '${duration.inMinutes} minutes ago';
    } else {
      return 'Just now';
    }
  }

  bool get isRecentlyActive {
    if (lastLogin == null) return false;
    return timeSinceLastLogin!.inDays <= 7;
  }

  // Helper methods for user management
  User approve(String approvedByUserId) {
    return copyWith(
      isApproved: true,
      approvedBy: approvedByUserId,
      approvedAt: DateTime.now(),
    );
  }

  User deactivate() {
    return copyWith(isActive: false);
  }

  User activate() {
    return copyWith(isActive: true);
  }

  User updateRole(String newRole) {
    return copyWith(primaryRole: newRole);
  }

  User updateLastLogin() {
    return copyWith(lastLogin: DateTime.now());
  }

  // Validation methods
  bool get isValid => email.isNotEmpty && fullName.isNotEmpty;

  List<String> get validationErrors {
    final errors = <String>[];
    if (email.isEmpty) errors.add('Email is required');
    if (fullName.isEmpty) errors.add('Full name is required');
    if (!email.contains('@')) errors.add('Invalid email format');
    if (fullName.length < 2) errors.add('Full name must be at least 2 characters');
    return errors;
  }

  // Factory methods
  static User createPendingUser({
    required String email,
    required String fullName,
    String? username,
    String? mobileNumber,
    String? phone,
    String? requestedRole,
  }) {
    return User(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      email: email,
      fullName: fullName,
      username: username,
      mobileNumber: mobileNumber,
      phone: phone,
      primaryRole: requestedRole ?? 'viewer',
      roles: [requestedRole ?? 'viewer'],
      permissions: const [],
      isActive: true,
      isApproved: false,
      createdAt: DateTime.now(),
    );
  }
}
