import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:srsr_property_management/features/dashboard/domain/dashboard_models.dart';
import 'package:srsr_property_management/features/dashboard/presentation/widgets/recent_alerts_widget.dart';
import 'package:srsr_property_management/shared/widgets/stat_card.dart';

void main() {
  group('Dashboard UI Tests', () {
    testWidgets('should display dashboard data without permission checks', (WidgetTester tester) async {
      // Create test data matching the backend response
      final testDashboardData = DashboardData(
        properties: const PropertyStats(
          total: 12,
          operational: 9,
          warning: 1,
          critical: 1,
        ),
        maintenanceIssues: const MaintenanceStats(
          total: 5,
          open: 4,
          inProgress: 1,
          critical: 1,
        ),
        recentAlerts: [
          RecentAlert(
            id: "41aa47b3-165b-40a0-9f3d-2bc6e12321b0",
            type: "payment",
            severity: "medium",
            message: "OTT service payment due: YouTube Premium",
            propertyName: "Guest House",
            timestamp: DateTime.parse("2025-05-31T13:28:16.020Z"),
          ),
          RecentAlert(
            id: "730222db-3168-4c14-bfec-7be3d10e802c",
            type: "maintenance",
            severity: "high",
            message: "high maintenance issue: Generator Fuel Low",
            propertyName: "Guest House",
            timestamp: DateTime.parse("2025-05-31T13:28:15.985Z"),
          ),
        ],
      );

      // Build a simple dashboard without permission checks
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Property Statistics
                    const Text(
                      'Property Overview',
                      style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 16),
                    GridView.count(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      crossAxisCount: 2,
                      crossAxisSpacing: 8,
                      mainAxisSpacing: 8,
                      childAspectRatio: 1.2,
                      children: [
                        StatCard(
                          title: 'Total Properties',
                          value: testDashboardData.properties.total.toString(),
                          icon: Icons.business,
                          color: Colors.blue,
                        ),
                        StatCard(
                          title: 'Operational',
                          value: testDashboardData.properties.operational.toString(),
                          icon: Icons.check_circle,
                          color: Colors.green,
                        ),
                        StatCard(
                          title: 'Warning',
                          value: testDashboardData.properties.warning.toString(),
                          icon: Icons.warning,
                          color: Colors.orange,
                        ),
                        StatCard(
                          title: 'Critical',
                          value: testDashboardData.properties.critical.toString(),
                          icon: Icons.error,
                          color: Colors.red,
                        ),
                      ],
                    ),
                    const SizedBox(height: 24),

                    // Maintenance Statistics
                    const Text(
                      'Maintenance Issues',
                      style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 16),
                    GridView.count(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      crossAxisCount: 2,
                      crossAxisSpacing: 8,
                      mainAxisSpacing: 8,
                      childAspectRatio: 1.2,
                      children: [
                        StatCard(
                          title: 'Total Issues',
                          value: testDashboardData.maintenanceIssues.total.toString(),
                          icon: Icons.build,
                          color: Colors.blue,
                        ),
                        StatCard(
                          title: 'Open',
                          value: testDashboardData.maintenanceIssues.open.toString(),
                          icon: Icons.pending,
                          color: Colors.orange,
                        ),
                        StatCard(
                          title: 'In Progress',
                          value: testDashboardData.maintenanceIssues.inProgress.toString(),
                          icon: Icons.engineering,
                          color: Colors.blue,
                        ),
                        StatCard(
                          title: 'Critical',
                          value: testDashboardData.maintenanceIssues.critical.toString(),
                          icon: Icons.priority_high,
                          color: Colors.red,
                        ),
                      ],
                    ),
                    const SizedBox(height: 24),

                    // Recent Alerts
                    const Text(
                      'Recent Alerts',
                      style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 16),
                    RecentAlertsWidget(alerts: testDashboardData.recentAlerts),
                  ],
                ),
              ),
            ),
          ),
        ),
      );

      // Verify that the widgets are displayed
      expect(find.text('Property Overview'), findsOneWidget);
      expect(find.text('Total Properties'), findsOneWidget);
      expect(find.text('12'), findsOneWidget); // Total properties value
      expect(find.text('9'), findsOneWidget);  // Operational properties value

      expect(find.text('Maintenance Issues'), findsOneWidget);
      expect(find.text('Total Issues'), findsOneWidget);
      expect(find.text('5'), findsOneWidget); // Total maintenance issues

      expect(find.text('Recent Alerts'), findsOneWidget);
      expect(find.text('OTT service payment due: YouTube Premium'), findsOneWidget);
      expect(find.text('Guest House'), findsAtLeastNWidgets(2)); // Property name appears twice
      expect(find.text('high maintenance issue: Generator Fuel Low'), findsOneWidget);

      // Verify alert severity chips
      expect(find.text('Medium'), findsOneWidget);
      expect(find.text('High'), findsOneWidget);
    });
  });
}
