import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import 'package:json_annotation/json_annotation.dart';
import '../../../core/constants/api_constants.dart';
import '../../../shared/models/api_response.dart';
import '../../../shared/models/user.dart';

part 'user_management_api_service.g.dart';

@RestApi()
abstract class UserManagementApiService {
  factory UserManagementApiService(Dio dio) = _UserManagementApiService;

  @GET(ApiConstants.users)
  Future<ApiResponse<List<User>>> getUsers({
    @Query('page') int? page,
    @Query('limit') int? limit,
    @Query('role') String? role,
    @Query('status') String? status,
    @Query('search') String? search,
  });

  @GET('${ApiConstants.users}/{id}')
  Future<ApiResponse<User>> getUserById(@Path('id') String id);

  @POST(ApiConstants.users)
  Future<HttpResponse<dynamic>> createUser(@Body() CreateUserRequest request);

  @PUT('${ApiConstants.users}/{id}')
  Future<ApiResponse<User>> updateUser(
    @Path('id') String id,
    @Body() UpdateUserRequest request,
  );

  @DELETE('${ApiConstants.users}/{id}')
  Future<ApiResponse<VoidResponse>> deleteUser(@Path('id') String id);

  @POST('${ApiConstants.users}/{id}/activate')
  Future<ApiResponse<User>> activateUser(@Path('id') String id);

  @POST('${ApiConstants.users}/{id}/deactivate')
  Future<ApiResponse<User>> deactivateUser(@Path('id') String id);

  @POST('${ApiConstants.users}/{id}/approve')
  Future<ApiResponse<User>> approveUser(@Path('id') String id);

  @POST('${ApiConstants.users}/{id}/reject')
  Future<ApiResponse<User>> rejectUser(@Path('id') String id);

  @POST('${ApiConstants.users}/{id}/roles')
  Future<ApiResponse<User>> assignRoles(
    @Path('id') String id,
    @Body() AssignRolesRequest request,
  );

  @DELETE('${ApiConstants.users}/{id}/roles/{roleId}')
  Future<ApiResponse<User>> removeRole(
    @Path('id') String id,
    @Path('roleId') String roleId,
  );

  @GET('${ApiConstants.users}/pending')
  Future<ApiResponse<List<User>>> getPendingUsers();

  @POST('${ApiConstants.users}/bulk-approve')
  Future<ApiResponse<List<User>>> bulkApproveUsers(@Body() BulkApproveRequest request);
}

@JsonSerializable()
class CreateUserRequest {
  final String email;
  @JsonKey(name: 'full_name')
  final String fullName;
  final String? username;
  @JsonKey(name: 'mobile_number')
  final String? mobileNumber;
  final String? phone;
  final String password;
  @JsonKey(name: 'primary_role')
  final String? primaryRole;
  final List<String>? roles;
  @JsonKey(name: 'is_active')
  final bool? isActive;

  const CreateUserRequest({
    required this.email,
    required this.fullName,
    this.username,
    this.mobileNumber,
    this.phone,
    required this.password,
    this.primaryRole,
    this.roles,
    this.isActive,
  });

  factory CreateUserRequest.fromJson(Map<String, dynamic> json) => _$CreateUserRequestFromJson(json);
  Map<String, dynamic> toJson() => _$CreateUserRequestToJson(this);
}

@JsonSerializable()
class UpdateUserRequest {
  final String? email;
  @JsonKey(name: 'full_name')
  final String? fullName;
  final String? username;
  @JsonKey(name: 'mobile_number')
  final String? mobileNumber;
  final String? phone;
  @JsonKey(name: 'primary_role')
  final String? primaryRole;
  final List<String>? roles;
  @JsonKey(name: 'is_active')
  final bool? isActive;

  const UpdateUserRequest({
    this.email,
    this.fullName,
    this.username,
    this.mobileNumber,
    this.phone,
    this.primaryRole,
    this.roles,
    this.isActive,
  });

  factory UpdateUserRequest.fromJson(Map<String, dynamic> json) => _$UpdateUserRequestFromJson(json);
  Map<String, dynamic> toJson() => _$UpdateUserRequestToJson(this);
}

@JsonSerializable()
class AssignRolesRequest {
  final List<String> roles;

  const AssignRolesRequest({required this.roles});

  factory AssignRolesRequest.fromJson(Map<String, dynamic> json) => _$AssignRolesRequestFromJson(json);
  Map<String, dynamic> toJson() => _$AssignRolesRequestToJson(this);
}

@JsonSerializable()
class BulkApproveRequest {
  @JsonKey(name: 'user_ids')
  final List<String> userIds;

  const BulkApproveRequest({required this.userIds});

  factory BulkApproveRequest.fromJson(Map<String, dynamic> json) => _$BulkApproveRequestFromJson(json);
  Map<String, dynamic> toJson() => _$BulkApproveRequestToJson(this);
}

@JsonSerializable()
class VoidResponse {
  final String message;

  const VoidResponse({required this.message});

  factory VoidResponse.fromJson(Map<String, dynamic> json) => _$VoidResponseFromJson(json);
  Map<String, dynamic> toJson() => _$VoidResponseToJson(this);
}
