import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../notifications/notification_providers.dart';
import 'monitoring_service.dart';

/// Provider for the monitoring service
final monitoringServiceProvider = Provider<MonitoringService>((ref) {
  final service = MonitoringService();
  final apiService = ref.read(notificationApiServiceProvider);
  service.initialize(apiService);
  return service;
});

/// Provider for monitoring status
final monitoringStatusProvider = StateProvider<bool>((ref) {
  final service = ref.read(monitoringServiceProvider);
  return service.isMonitoring;
});

/// Provider to start/stop monitoring
final monitoringControllerProvider = Provider<MonitoringController>((ref) {
  return MonitoringController(ref);
});

/// Controller for managing monitoring state
class MonitoringController {
  final Ref _ref;

  MonitoringController(this._ref);

  /// Start monitoring
  void startMonitoring() {
    final service = _ref.read(monitoringServiceProvider);
    service.startMonitoring();
    _ref.read(monitoringStatusProvider.notifier).state = true;
  }

  /// Stop monitoring
  void stopMonitoring() {
    final service = _ref.read(monitoringServiceProvider);
    service.stopMonitoring();
    _ref.read(monitoringStatusProvider.notifier).state = false;
  }

  /// Toggle monitoring
  void toggleMonitoring() {
    final isMonitoring = _ref.read(monitoringStatusProvider);
    if (isMonitoring) {
      stopMonitoring();
    } else {
      startMonitoring();
    }
  }

  /// Send fuel metrics
  Future<bool> sendFuelMetrics({
    required String propertyId,
    required double fuelLevel,
    required double fuelPercentage,
  }) async {
    final service = _ref.read(monitoringServiceProvider);
    return await service.sendFuelLevel(
      propertyId: propertyId,
      fuelLevel: fuelLevel,
      fuelPercentage: fuelPercentage,
    );
  }

  /// Send attendance metrics
  Future<bool> sendAttendanceMetrics({
    required String propertyId,
    required double attendancePercentage,
    required int presentCount,
    required int totalCount,
  }) async {
    final service = _ref.read(monitoringServiceProvider);
    return await service.sendAttendanceMetric(
      propertyId: propertyId,
      attendancePercentage: attendancePercentage,
      presentCount: presentCount,
      totalCount: totalCount,
    );
  }

  /// Send maintenance metrics
  Future<bool> sendMaintenanceMetrics({
    required String propertyId,
    required int openIssues,
    required int overdueIssues,
    required int criticalIssues,
  }) async {
    final service = _ref.read(monitoringServiceProvider);
    return await service.sendMaintenanceMetrics(
      propertyId: propertyId,
      openIssues: openIssues,
      overdueIssues: overdueIssues,
      criticalIssues: criticalIssues,
    );
  }

  /// Send custom metric
  Future<bool> sendCustomMetric({
    required String propertyId,
    required String serviceType,
    required String metricName,
    required double value,
    String? unit,
    DateTime? timestamp,
  }) async {
    final service = _ref.read(monitoringServiceProvider);
    return await service.sendMetric(
      propertyId: propertyId,
      serviceType: serviceType,
      metricName: metricName,
      value: value,
      unit: unit,
      timestamp: timestamp,
    );
  }
}
