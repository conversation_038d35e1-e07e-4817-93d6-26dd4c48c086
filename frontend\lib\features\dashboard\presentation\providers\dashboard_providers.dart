import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/network/dio_client.dart';
import '../../data/dashboard_api_service.dart';
import '../../domain/dashboard_models.dart';

// API Service Provider
final dashboardApiServiceProvider = Provider<DashboardApiService>((ref) {
  return DashboardApiService(DioClient.instance.dio);
});

// Dashboard Status Provider
final dashboardStatusProvider = FutureProvider<DashboardStatus>((ref) async {
  print('📊 DASHBOARD PROVIDER: Starting dashboard data fetch...');

  try {
    final apiService = ref.read(dashboardApiServiceProvider);
    print('📊 DASHBOARD PROVIDER: API service obtained');

    print('📊 DASHBOARD PROVIDER: Calling getDashboardStatus...');
    final result = await apiService.getDashboardStatus();
    print('📊 DASHBOARD PROVIDER: Dashboard data received successfully!');
    print('📊 DASHBOARD PROVIDER: Success: ${result.success}');
    print('📊 DASHBOARD PROVIDER: Properties total: ${result.data.properties.total}');
    print('📊 DASHBOARD PROVIDER: Maintenance issues: ${result.data.maintenanceIssues.total}');
    print('📊 DASHBOARD PROVIDER: Recent alerts: ${result.data.recentAlerts.length}');

    return result;
  } catch (e, stackTrace) {
    print('📊 DASHBOARD PROVIDER: ERROR occurred!');
    print('📊 DASHBOARD PROVIDER: Error type: ${e.runtimeType}');
    print('📊 DASHBOARD PROVIDER: Error message: $e');
    print('📊 DASHBOARD PROVIDER: Stack trace: $stackTrace');
    rethrow;
  }
});

// Auto-refresh provider (refreshes every 30 seconds)
final dashboardAutoRefreshProvider = StreamProvider<DashboardStatus>((ref) async* {
  while (true) {
    try {
      final apiService = ref.read(dashboardApiServiceProvider);
      final status = await apiService.getDashboardStatus();
      yield status;
    } catch (e) {
      // Handle error silently for auto-refresh
    }

    await Future.delayed(const Duration(seconds: 30));
  }
});

// Manual refresh provider
final dashboardRefreshProvider = StateProvider<int>((ref) => 0);

// Combined provider that uses manual refresh trigger
final dashboardDataProvider = FutureProvider<DashboardStatus>((ref) async {
  print('📊 DASHBOARD DATA PROVIDER: Starting dashboard data fetch...');

  // Watch the refresh trigger
  ref.watch(dashboardRefreshProvider);
  print('📊 DASHBOARD DATA PROVIDER: Refresh trigger watched');

  try {
    final apiService = ref.read(dashboardApiServiceProvider);
    print('📊 DASHBOARD DATA PROVIDER: API service obtained');

    print('📊 DASHBOARD DATA PROVIDER: Calling getDashboardStatus...');
    final result = await apiService.getDashboardStatus();
    print('📊 DASHBOARD DATA PROVIDER: Dashboard data received successfully!');
    print('📊 DASHBOARD DATA PROVIDER: Success: ${result.success}');
    print('📊 DASHBOARD DATA PROVIDER: Properties total: ${result.data.properties.total}');
    print('📊 DASHBOARD DATA PROVIDER: Maintenance issues: ${result.data.maintenanceIssues.total}');
    print('📊 DASHBOARD DATA PROVIDER: Recent alerts: ${result.data.recentAlerts.length}');

    return result;
  } catch (e, stackTrace) {
    print('📊 DASHBOARD DATA PROVIDER: ERROR occurred!');
    print('📊 DASHBOARD DATA PROVIDER: Error type: ${e.runtimeType}');
    print('📊 DASHBOARD DATA PROVIDER: Error message: $e');
    print('📊 DASHBOARD DATA PROVIDER: Stack trace: $stackTrace');
    rethrow;
  }
});
