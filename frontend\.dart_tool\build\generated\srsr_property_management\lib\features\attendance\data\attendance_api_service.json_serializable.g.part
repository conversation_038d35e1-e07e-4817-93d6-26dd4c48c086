// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SubmitPropertyAttendanceRequest _$SubmitPropertyAttendanceRequestFromJson(
        Map<String, dynamic> json) =>
    SubmitPropertyAttendanceRequest(
      propertyId: json['property_id'] as String,
      date: json['date'] as String,
      attendance: (json['attendance'] as List<dynamic>)
          .map((e) => AttendanceEntry.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$SubmitPropertyAttendanceRequestToJson(
        SubmitPropertyAttendanceRequest instance) =>
    <String, dynamic>{
      'property_id': instance.propertyId,
      'date': instance.date,
      'attendance': instance.attendance,
    };

UpdatePropertyAttendanceRequest _$UpdatePropertyAttendanceRequestFromJson(
        Map<String, dynamic> json) =>
    UpdatePropertyAttendanceRequest(
      status: json['status'] as String?,
      checkInTime: json['check_in_time'] as String?,
      checkOutTime: json['check_out_time'] as String?,
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$UpdatePropertyAttendanceRequestToJson(
        UpdatePropertyAttendanceRequest instance) =>
    <String, dynamic>{
      'status': instance.status,
      'check_in_time': instance.checkInTime,
      'check_out_time': instance.checkOutTime,
      'notes': instance.notes,
    };

SubmitSiteAttendanceRequest _$SubmitSiteAttendanceRequestFromJson(
        Map<String, dynamic> json) =>
    SubmitSiteAttendanceRequest(
      propertyId: json['property_id'] as String,
      date: json['date'] as String,
      attendance: (json['attendance'] as List<dynamic>)
          .map((e) => AttendanceEntry.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$SubmitSiteAttendanceRequestToJson(
        SubmitSiteAttendanceRequest instance) =>
    <String, dynamic>{
      'property_id': instance.propertyId,
      'date': instance.date,
      'attendance': instance.attendance,
    };

SubmitOfficeAttendanceRequest _$SubmitOfficeAttendanceRequestFromJson(
        Map<String, dynamic> json) =>
    SubmitOfficeAttendanceRequest(
      officeId: json['office_id'] as String,
      date: json['date'] as String,
      attendance: (json['attendance'] as List<dynamic>)
          .map((e) => AttendanceEntry.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$SubmitOfficeAttendanceRequestToJson(
        SubmitOfficeAttendanceRequest instance) =>
    <String, dynamic>{
      'office_id': instance.officeId,
      'date': instance.date,
      'attendance': instance.attendance,
    };

UpdateSiteAttendanceRequest _$UpdateSiteAttendanceRequestFromJson(
        Map<String, dynamic> json) =>
    UpdateSiteAttendanceRequest(
      status: json['status'] as String?,
      checkInTime: json['check_in_time'] as String?,
      checkOutTime: json['check_out_time'] as String?,
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$UpdateSiteAttendanceRequestToJson(
        UpdateSiteAttendanceRequest instance) =>
    <String, dynamic>{
      'status': instance.status,
      'check_in_time': instance.checkInTime,
      'check_out_time': instance.checkOutTime,
      'notes': instance.notes,
    };

UpdateOfficeAttendanceRequest _$UpdateOfficeAttendanceRequestFromJson(
        Map<String, dynamic> json) =>
    UpdateOfficeAttendanceRequest(
      status: json['status'] as String?,
      checkInTime: json['check_in_time'] as String?,
      checkOutTime: json['check_out_time'] as String?,
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$UpdateOfficeAttendanceRequestToJson(
        UpdateOfficeAttendanceRequest instance) =>
    <String, dynamic>{
      'status': instance.status,
      'check_in_time': instance.checkInTime,
      'check_out_time': instance.checkOutTime,
      'notes': instance.notes,
    };

AttendanceEntry _$AttendanceEntryFromJson(Map<String, dynamic> json) =>
    AttendanceEntry(
      userId: json['user_id'] as String,
      status: json['status'] as String,
      checkInTime: json['check_in_time'] as String?,
      checkOutTime: json['check_out_time'] as String?,
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$AttendanceEntryToJson(AttendanceEntry instance) =>
    <String, dynamic>{
      'user_id': instance.userId,
      'status': instance.status,
      'check_in_time': instance.checkInTime,
      'check_out_time': instance.checkOutTime,
      'notes': instance.notes,
    };

AttendanceSummary _$AttendanceSummaryFromJson(Map<String, dynamic> json) =>
    AttendanceSummary(
      totalEmployees: (json['total_employees'] as num).toInt(),
      presentCount: (json['present_count'] as num).toInt(),
      absentCount: (json['absent_count'] as num).toInt(),
      lateCount: (json['late_count'] as num).toInt(),
      attendancePercentage: (json['attendance_percentage'] as num).toDouble(),
      startDate: json['start_date'] as String,
      endDate: json['end_date'] as String,
    );

Map<String, dynamic> _$AttendanceSummaryToJson(AttendanceSummary instance) =>
    <String, dynamic>{
      'total_employees': instance.totalEmployees,
      'present_count': instance.presentCount,
      'absent_count': instance.absentCount,
      'late_count': instance.lateCount,
      'attendance_percentage': instance.attendancePercentage,
      'start_date': instance.startDate,
      'end_date': instance.endDate,
    };

UserAttendanceReport _$UserAttendanceReportFromJson(
        Map<String, dynamic> json) =>
    UserAttendanceReport(
      userId: json['user_id'] as String,
      userName: json['user_name'] as String,
      totalDays: (json['total_days'] as num).toInt(),
      presentDays: (json['present_days'] as num).toInt(),
      absentDays: (json['absent_days'] as num).toInt(),
      lateDays: (json['late_days'] as num).toInt(),
      attendancePercentage: (json['attendance_percentage'] as num).toDouble(),
      records: (json['records'] as List<dynamic>)
          .map((e) => AttendanceRecord.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$UserAttendanceReportToJson(
        UserAttendanceReport instance) =>
    <String, dynamic>{
      'user_id': instance.userId,
      'user_name': instance.userName,
      'total_days': instance.totalDays,
      'present_days': instance.presentDays,
      'absent_days': instance.absentDays,
      'late_days': instance.lateDays,
      'attendance_percentage': instance.attendancePercentage,
      'records': instance.records,
    };

VoidResponse _$VoidResponseFromJson(Map<String, dynamic> json) => VoidResponse(
      message: json['message'] as String,
    );

Map<String, dynamic> _$VoidResponseToJson(VoidResponse instance) =>
    <String, dynamic>{
      'message': instance.message,
    };
