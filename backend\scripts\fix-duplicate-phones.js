const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function fixDuplicatePhones() {
  try {
    console.log('Checking for duplicate phone numbers...');
    
    // Find users with duplicate phone numbers
    const users = await prisma.user.findMany({
      where: {
        phone: {
          not: null
        }
      },
      orderBy: {
        createdAt: 'asc'
      }
    });

    const phoneMap = new Map();
    const duplicates = [];

    for (const user of users) {
      if (user.phone) {
        if (phoneMap.has(user.phone)) {
          duplicates.push(user);
        } else {
          phoneMap.set(user.phone, user);
        }
      }
    }

    console.log(`Found ${duplicates.length} users with duplicate phone numbers`);

    // Set duplicate phone numbers to null
    for (const user of duplicates) {
      console.log(`Clearing phone number for user ${user.email} (${user.id})`);
      await prisma.user.update({
        where: { id: user.id },
        data: { phone: null }
      });
    }

    console.log('Fixed duplicate phone numbers');
  } catch (error) {
    console.error('Error fixing duplicate phone numbers:', error);
  } finally {
    await prisma.$disconnect();
  }
}

fixDuplicatePhones();
