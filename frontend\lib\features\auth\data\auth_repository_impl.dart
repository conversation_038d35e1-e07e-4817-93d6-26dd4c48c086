import 'package:dio/dio.dart';
import '../../../core/storage/storage_service.dart';
import '../../../shared/models/user.dart';
import '../domain/auth_repository.dart';
import 'auth_api_service.dart';

class AuthRepositoryImpl implements AuthRepository {
  final AuthApiService _apiService;

  AuthRepositoryImpl(this._apiService);

  @override
  Future<AuthResult> login(String email, String password) async {
    try {
      final request = LoginRequest.email(email: email, password: password);
      final response = await _apiService.login(request);

      if (response.success) {
        // Store token and user data
        await StorageService.saveToken(response.token);
        await StorageService.saveUserData(response.user.toJson());

        return AuthResult.success(response.token, response.user);
      } else {
        return AuthResult.failure('Login failed');
      }
    } on DioException catch (e) {
      String errorMessage = 'Login failed';

      if (e.response?.statusCode == 401) {
        errorMessage = 'Invalid email or password';
      } else if (e.response?.data != null && e.response!.data['error'] != null) {
        errorMessage = e.response!.data['error'];
      } else if (e.type == DioExceptionType.connectionTimeout ||
                 e.type == DioExceptionType.receiveTimeout) {
        errorMessage = 'Connection timeout. Please check your internet connection.';
      } else if (e.type == DioExceptionType.connectionError) {
        errorMessage = 'No internet connection';
      }

      return AuthResult.failure(errorMessage);
    } catch (e) {
      return AuthResult.failure('An unexpected error occurred');
    }
  }

  @override
  Future<AuthResult> loginWithIdentifier({
    required String identifier,
    required String password,
    required String loginType,
  }) async {
    try {
      print('🔐 Repository: Attempting login with $loginType: $identifier');
      // Use new multi-field login
      final request = LoginRequest.multiField(
        identifier: identifier,
        password: password,
        loginType: loginType,
      );
      print('🔐 Repository: Making API call...');
      final response = await _apiService.login(request);
      print('🔐 Repository: API response received - success: ${response.success}');

      if (response.success) {
        print('🔐 Repository: Storing token and user data...');
        // Store token and user data
        await StorageService.saveToken(response.token);
        await StorageService.saveUserData(response.user.toJson());
        print('🔐 Repository: Token and user data stored successfully');

        return AuthResult.success(response.token, response.user);
      } else {
        print('🔐 Repository: Login failed - response not successful');
        return AuthResult.failure('Login failed');
      }
    } on DioException catch (e) {
      print('🔐 Repository: DioException - ${e.response?.statusCode}: ${e.message}');
      print('🔐 Repository: Response data: ${e.response?.data}');
      String errorMessage = 'Login failed';

      if (e.response?.statusCode == 401) {
        errorMessage = 'Invalid credentials';
      } else if (e.response?.data != null && e.response!.data['error'] != null) {
        errorMessage = e.response!.data['error'];
      } else if (e.type == DioExceptionType.connectionTimeout ||
                 e.type == DioExceptionType.receiveTimeout) {
        errorMessage = 'Connection timeout. Please check your internet connection.';
      } else if (e.type == DioExceptionType.connectionError) {
        errorMessage = 'No internet connection';
      }

      return AuthResult.failure(errorMessage);
    } catch (e) {
      print('🔐 Repository: Exception: $e');
      return AuthResult.failure('An unexpected error occurred: $e');
    }
  }

  @override
  Future<AuthResult> register(String email, String password, String fullName, String? phone) async {
    try {
      final request = RegisterRequest(
        email: email,
        password: password,
        fullName: fullName,
        phone: phone,
      );

      final response = await _apiService.register(request);

      if (response.success) {
        return AuthResult(success: true);
      } else {
        return AuthResult.failure('Registration failed');
      }
    } on DioException catch (e) {
      String errorMessage = 'Registration failed';

      if (e.response?.statusCode == 409) {
        errorMessage = 'Email already exists';
      } else if (e.response?.data != null && e.response!.data['error'] != null) {
        errorMessage = e.response!.data['error'];
      } else if (e.type == DioExceptionType.connectionTimeout ||
                 e.type == DioExceptionType.receiveTimeout) {
        errorMessage = 'Connection timeout. Please check your internet connection.';
      } else if (e.type == DioExceptionType.connectionError) {
        errorMessage = 'No internet connection';
      }

      return AuthResult.failure(errorMessage);
    } catch (e) {
      return AuthResult.failure('An unexpected error occurred');
    }
  }

  @override
  Future<User> getCurrentUser() async {
    try {
      final response = await _apiService.getCurrentUser();
      if (response.data != null) {
        return response.data!;
      } else {
        throw Exception('User data is null');
      }
    } on DioException catch (e) {
      print('🔐 getCurrentUser DioException: ${e.response?.statusCode} - ${e.message}');
      if (e.response?.statusCode == 401) {
        await logout();
        throw Exception('Session expired. Please login again.');
      }
      throw Exception('Failed to get user profile: ${e.message}');
    } catch (e) {
      print('🔐 getCurrentUser Exception: $e');
      throw Exception('An unexpected error occurred: $e');
    }
  }

  @override
  Future<void> logout() async {
    await StorageService.clearToken();
    await StorageService.clearUserData();
  }

  @override
  bool get isLoggedIn => StorageService.isLoggedIn;
}
