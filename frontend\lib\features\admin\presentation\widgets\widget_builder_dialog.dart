import 'package:flutter/material.dart';
import '../../../../core/constants/app_constants.dart';

enum WidgetBuilderMode {
  visual,
  code,
  template,
}

class WidgetBuilderDialog extends StatefulWidget {
  final WidgetBuilderMode mode;

  const WidgetBuilderDialog({super.key, required this.mode});

  @override
  State<WidgetBuilderDialog> createState() => _WidgetBuilderDialogState();
}

class _WidgetBuilderDialogState extends State<WidgetBuilderDialog> {
  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.9,
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          children: [
            _buildHeader(),
            const SizedBox(height: AppConstants.defaultPadding),
            Expanded(
              child: _buildContent(),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            _buildFooter(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Icon(
          _getModeIcon(),
          color: Theme.of(context).primaryColor,
        ),
        const SizedBox(width: 8),
        Text(
          _getModeTitle(),
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const Spacer(),
        IconButton(
          icon: const Icon(Icons.close),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ],
    );
  }

  Widget _buildContent() {
    switch (widget.mode) {
      case WidgetBuilderMode.visual:
        return _buildVisualBuilder();
      case WidgetBuilderMode.code:
        return _buildCodeBuilder();
      case WidgetBuilderMode.template:
        return _buildTemplateBuilder();
    }
  }

  Widget _buildVisualBuilder() {
    return Row(
      children: [
        // Widget Palette
        Expanded(
          flex: 1,
          child: Card(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  padding: const EdgeInsets.all(AppConstants.defaultPadding),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(8),
                      topRight: Radius.circular(8),
                    ),
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.palette, size: 16),
                      const SizedBox(width: 8),
                      Text(
                        'Widget Palette',
                        style: Theme.of(context).textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: ListView(
                    padding: const EdgeInsets.all(AppConstants.smallPadding),
                    children: [
                      _buildPaletteItem('Stat Card', Icons.analytics, Colors.blue),
                      _buildPaletteItem('Chart', Icons.bar_chart, Colors.green),
                      _buildPaletteItem('Table', Icons.table_chart, Colors.orange),
                      _buildPaletteItem('Button', Icons.smart_button, Colors.red),
                      _buildPaletteItem('Text', Icons.text_fields, Colors.purple),
                      _buildPaletteItem('Image', Icons.image, Colors.pink),
                      _buildPaletteItem('List', Icons.list, Colors.indigo),
                      _buildPaletteItem('Grid', Icons.grid_view, Colors.teal),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),

        const SizedBox(width: AppConstants.defaultPadding),

        // Canvas
        Expanded(
          flex: 2,
          child: Card(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  padding: const EdgeInsets.all(AppConstants.defaultPadding),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(8),
                      topRight: Radius.circular(8),
                    ),
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.design_services, size: 16),
                      const SizedBox(width: 8),
                      Text(
                        'Design Canvas',
                        style: Theme.of(context).textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const Spacer(),
                      IconButton(
                        icon: const Icon(Icons.clear_all, size: 16),
                        onPressed: () {},
                        tooltip: 'Clear canvas',
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Container(
                    width: double.infinity,
                    margin: const EdgeInsets.all(AppConstants.defaultPadding),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey[300]!, style: BorderStyle.solid),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.drag_indicator, size: 48, color: Colors.grey),
                          SizedBox(height: 16),
                          Text(
                            'Drag widgets here to build your custom widget',
                            style: TextStyle(color: Colors.grey),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),

        const SizedBox(width: AppConstants.defaultPadding),

        // Properties Panel
        Expanded(
          flex: 1,
          child: Card(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  padding: const EdgeInsets.all(AppConstants.defaultPadding),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(8),
                      topRight: Radius.circular(8),
                    ),
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.settings, size: 16),
                      const SizedBox(width: 8),
                      Text(
                        'Properties',
                        style: Theme.of(context).textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
                const Expanded(
                  child: Padding(
                    padding: EdgeInsets.all(AppConstants.defaultPadding),
                    child: Center(
                      child: Text(
                        'Select a widget to edit its properties',
                        style: TextStyle(color: Colors.grey),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCodeBuilder() {
    return Column(
      children: [
        // Code Editor Toolbar
        Container(
          padding: const EdgeInsets.all(AppConstants.smallPadding),
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(8),
              topRight: Radius.circular(8),
            ),
          ),
          child: Row(
            children: [
              const Icon(Icons.code, size: 16),
              const SizedBox(width: 8),
              Text(
                'Widget Code Editor',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              IconButton(
                icon: const Icon(Icons.play_arrow, size: 16),
                onPressed: () {},
                tooltip: 'Preview',
              ),
              IconButton(
                icon: const Icon(Icons.save, size: 16),
                onPressed: () {},
                tooltip: 'Save',
              ),
            ],
          ),
        ),

        // Code Editor
        Expanded(
          child: Container(
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey[300]!),
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(8),
                bottomRight: Radius.circular(8),
              ),
            ),
            child: const TextField(
              maxLines: null,
              expands: true,
              decoration: InputDecoration(
                hintText: '''// Write your custom widget code here
import 'package:flutter/material.dart';

class CustomWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      child: Text('Hello World'),
    );
  }
}''',
                border: InputBorder.none,
                contentPadding: EdgeInsets.all(16),
              ),
              style: TextStyle(
                fontFamily: 'monospace',
                fontSize: 14,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTemplateBuilder() {
    return GridView.count(
      crossAxisCount: 3,
      crossAxisSpacing: AppConstants.defaultPadding,
      mainAxisSpacing: AppConstants.defaultPadding,
      children: [
        _buildTemplateCard(
          'Dashboard Card',
          'A card widget for displaying key metrics',
          Icons.dashboard,
          Colors.blue,
        ),
        _buildTemplateCard(
          'Data Table',
          'A table widget for displaying tabular data',
          Icons.table_chart,
          Colors.green,
        ),
        _buildTemplateCard(
          'Chart Widget',
          'A chart widget for data visualization',
          Icons.bar_chart,
          Colors.orange,
        ),
        _buildTemplateCard(
          'Form Builder',
          'A form widget for data input',
          Icons.check_box,
          Colors.purple,
        ),
        _buildTemplateCard(
          'List View',
          'A list widget for displaying items',
          Icons.list,
          Colors.red,
        ),
        _buildTemplateCard(
          'Action Button',
          'A button widget for user actions',
          Icons.smart_button,
          Colors.teal,
        ),
      ],
    );
  }

  Widget _buildPaletteItem(String name, IconData icon, Color color) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppConstants.smallPadding),
      child: ListTile(
        dense: true,
        leading: Icon(icon, color: color, size: 20),
        title: Text(
          name,
          style: Theme.of(context).textTheme.bodySmall,
        ),
        onTap: () {
          // TODO: Add widget to canvas
        },
      ),
    );
  }

  Widget _buildTemplateCard(String title, String description, IconData icon, Color color) {
    return Card(
      child: InkWell(
        onTap: () {
          // TODO: Load template
        },
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, size: 48, color: color),
              const SizedBox(height: AppConstants.smallPadding),
              Text(
                title,
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                description,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFooter() {
    return Row(
      children: [
        if (widget.mode == WidgetBuilderMode.visual) ...[
          TextButton.icon(
            onPressed: () {},
            icon: const Icon(Icons.preview),
            label: const Text('Preview'),
          ),
          const SizedBox(width: AppConstants.smallPadding),
        ],
        const Spacer(),
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        const SizedBox(width: AppConstants.smallPadding),
        ElevatedButton(
          onPressed: () {
            // TODO: Save widget
            Navigator.of(context).pop();
          },
          child: const Text('Save Widget'),
        ),
      ],
    );
  }

  IconData _getModeIcon() {
    switch (widget.mode) {
      case WidgetBuilderMode.visual:
        return Icons.design_services;
      case WidgetBuilderMode.code:
        return Icons.code;
      case WidgetBuilderMode.template:
        return Icons.dashboard_outlined;
    }
  }

  String _getModeTitle() {
    switch (widget.mode) {
      case WidgetBuilderMode.visual:
        return 'Visual Widget Builder';
      case WidgetBuilderMode.code:
        return 'Code Widget Builder';
      case WidgetBuilderMode.template:
        return 'Template Widget Builder';
    }
  }
}
