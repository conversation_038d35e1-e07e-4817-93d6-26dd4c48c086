import { NextRequest } from 'next/server';
import { requireAuth } from '@/lib/auth';
import { createApiResponse, getRequestBody, handleError, corsHeaders } from '@/lib/utils';
import { prisma } from '@/lib/prisma';
import { validateRequest } from '@/lib/validation';
import Joi from 'joi';

const createWidgetPermissionSchema = Joi.object({
  screen_name: Joi.string().required(),
  widget_name: Joi.string().required(),
  required_permissions: Joi.array().items(Joi.string()).default([]),
  allowed_roles: Joi.array().items(Joi.string()).default([]),
  is_visible: Joi.boolean().default(true),
  is_enabled: Joi.boolean().default(true),
});

async function getWidgetPermissionsHandler(request: NextRequest, context: any, currentUser: any) {
  try {
    // Check if user has admin permissions
    const hasAdminRole = currentUser.userRoles?.some((ur: any) => ur.role.name === 'admin');
    if (!hasAdminRole) {
      return Response.json(
        createApiResponse(null, 'Insufficient permissions', 'FORBIDDEN'),
        { status: 403 }
      );
    }

    const url = new URL(request.url);
    const screenName = url.searchParams.get('screen_name');

    const whereClause = screenName ? { screenName } : {};

    const widgetPermissions = await prisma.widgetPermission.findMany({
      where: whereClause,
      orderBy: [
        { screenName: 'asc' },
        { widgetName: 'asc' },
      ],
    });

    return Response.json(
      createApiResponse({
        permissions: widgetPermissions.map(permission => ({
          id: permission.id,
          screen_name: permission.screenName,
          widget_name: permission.widgetName,
          required_permissions: permission.requiredPermissions,
          allowed_roles: permission.allowedRoles,
          is_visible: permission.isVisible,
          is_enabled: permission.isEnabled,
          created_at: permission.createdAt,
          updated_at: permission.updatedAt,
        })),
        count: widgetPermissions.length,
        screen_name: screenName,
      }),
      {
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to get widget permissions');
  }
}

async function createWidgetPermissionHandler(request: NextRequest, context: any, currentUser: any) {
  try {
    // Check if user has admin permissions
    const hasAdminRole = currentUser.userRoles?.some((ur: any) => ur.role.name === 'admin');
    if (!hasAdminRole) {
      return Response.json(
        createApiResponse(null, 'Insufficient permissions', 'FORBIDDEN'),
        { status: 403 }
      );
    }

    const body = await getRequestBody(request);
    
    // Validate request body
    const validation = validateRequest(createWidgetPermissionSchema, body);
    if (!validation.isValid) {
      return Response.json(
        createApiResponse(null, 'Validation failed', 'VALIDATION_ERROR'),
        { status: 400 }
      );
    }

    const { 
      screen_name, 
      widget_name, 
      required_permissions, 
      allowed_roles, 
      is_visible, 
      is_enabled 
    } = validation.data;

    // Check if widget permission already exists
    const existingPermission = await prisma.widgetPermission.findUnique({
      where: { 
        screenName_widgetName: {
          screenName: screen_name,
          widgetName: widget_name,
        }
      },
    });

    if (existingPermission) {
      return Response.json(
        createApiResponse(null, 'Widget permission already exists', 'CONFLICT'),
        { status: 409 }
      );
    }

    const widgetPermission = await prisma.widgetPermission.create({
      data: {
        screenName: screen_name,
        widgetName: widget_name,
        requiredPermissions: required_permissions,
        allowedRoles: allowed_roles,
        isVisible: is_visible,
        isEnabled: is_enabled,
      },
    });

    return Response.json(
      createApiResponse({
        permission: {
          id: widgetPermission.id,
          screen_name: widgetPermission.screenName,
          widget_name: widgetPermission.widgetName,
          required_permissions: widgetPermission.requiredPermissions,
          allowed_roles: widgetPermission.allowedRoles,
          is_visible: widgetPermission.isVisible,
          is_enabled: widgetPermission.isEnabled,
          created_at: widgetPermission.createdAt,
          updated_at: widgetPermission.updatedAt,
        },
      }),
      {
        status: 201,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to create widget permission');
  }
}

// GET /api/permissions/widgets - Get all widget permissions (optionally filtered by screen)
export const GET = requireAuth(getWidgetPermissionsHandler);

// POST /api/permissions/widgets - Create widget permission
export const POST = requireAuth(createWidgetPermissionHandler);

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: corsHeaders(),
  });
}
