import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../network/api_client.dart';
import 'notification_api_service.dart';
import 'notification_service.dart';
import 'sse_notification_service.dart';

// API Service Provider
final notificationApiServiceProvider = Provider<NotificationApiService>((ref) {
  final apiClient = ref.read(apiClientProvider);
  return NotificationApiService(apiClient);
});

// Local Notification Service Provider
final notificationServiceProvider = Provider<NotificationService>((ref) {
  return NotificationService();
});

// SSE Notification Service Provider
final sseNotificationServiceProvider = Provider<SSENotificationService>((ref) {
  return SSENotificationService();
});

// Server Notifications Provider
final serverNotificationsProvider = FutureProvider<List<ServerNotification>>((ref) async {
  final apiService = ref.read(notificationApiServiceProvider);
  final result = await apiService.getUnreadNotifications();
  
  return result.when(
    success: (notifications) => notifications,
    error: (error) => throw Exception(error),
  );
});

// Active Alerts Provider
final activeAlertsProvider = FutureProvider<List<ThresholdAlert>>((ref) async {
  final apiService = ref.read(notificationApiServiceProvider);
  final result = await apiService.getActiveAlerts();
  
  return result.when(
    success: (alerts) => alerts,
    error: (error) => throw Exception(error),
  );
});

// Property-specific Active Alerts Provider
final propertyActiveAlertsProvider = FutureProvider.family<List<ThresholdAlert>, String>((ref, propertyId) async {
  final apiService = ref.read(notificationApiServiceProvider);
  final result = await apiService.getActiveAlerts(propertyId: propertyId);
  
  return result.when(
    success: (alerts) => alerts,
    error: (error) => throw Exception(error),
  );
});

// SSE Connection State Provider
final sseConnectionStateProvider = StateProvider<bool>((ref) {
  return false;
});

// Real-time Notifications Stream Provider
final realTimeNotificationsProvider = StreamProvider<ServerNotification>((ref) {
  final sseService = ref.read(sseNotificationServiceProvider);
  return sseService.notificationStream;
});

// Notification State Notifier
class NotificationNotifier extends StateNotifier<AsyncValue<List<ServerNotification>>> {
  final NotificationApiService _apiService;
  final SSENotificationService _sseService;

  NotificationNotifier(this._apiService, this._sseService) : super(const AsyncValue.loading()) {
    _initialize();
  }

  Future<void> _initialize() async {
    await loadNotifications();
    await _sseService.initialize();
    
    // Listen to real-time notifications
    _sseService.notificationStream.listen((notification) {
      _addNotification(notification);
    });
  }

  Future<void> loadNotifications() async {
    state = const AsyncValue.loading();
    
    final result = await _apiService.getUnreadNotifications();
    
    state = result.when(
      success: (notifications) => AsyncValue.data(notifications),
      error: (error) => AsyncValue.error(error, StackTrace.current),
    );
  }

  void _addNotification(ServerNotification notification) {
    state.whenData((notifications) {
      final updatedNotifications = [notification, ...notifications];
      state = AsyncValue.data(updatedNotifications);
    });
  }

  Future<void> markAsRead(String notificationId) async {
    final result = await _apiService.markAsRead(notificationId);
    
    result.when(
      success: (_) {
        state.whenData((notifications) {
          final updatedNotifications = notifications
              .where((n) => n.id != notificationId)
              .toList();
          state = AsyncValue.data(updatedNotifications);
        });
      },
      error: (error) {
        // Handle error - could show a snackbar
      },
    );
  }

  Future<void> refresh() async {
    await loadNotifications();
  }
}

// Notification State Provider
final notificationProvider = StateNotifierProvider<NotificationNotifier, AsyncValue<List<ServerNotification>>>((ref) {
  final apiService = ref.read(notificationApiServiceProvider);
  final sseService = ref.read(sseNotificationServiceProvider);
  return NotificationNotifier(apiService, sseService);
});

// Monitoring State Notifier
class MonitoringNotifier extends StateNotifier<AsyncValue<List<ThresholdAlert>>> {
  final NotificationApiService _apiService;

  MonitoringNotifier(this._apiService) : super(const AsyncValue.loading()) {
    loadAlerts();
  }

  Future<void> loadAlerts() async {
    state = const AsyncValue.loading();
    
    final result = await _apiService.getActiveAlerts();
    
    state = result.when(
      success: (alerts) => AsyncValue.data(alerts),
      error: (error) => AsyncValue.error(error, StackTrace.current),
    );
  }

  Future<bool> monitorMetric({
    required String propertyId,
    required String serviceType,
    required String metricName,
    required double value,
    String? unit,
    DateTime? timestamp,
  }) async {
    final result = await _apiService.monitorMetric(
      propertyId: propertyId,
      serviceType: serviceType,
      metricName: metricName,
      value: value,
      unit: unit,
      timestamp: timestamp,
    );

    return result.when(
      success: (alerts) {
        // Refresh alerts if new ones were generated
        if (alerts.isNotEmpty) {
          loadAlerts();
        }
        return true;
      },
      error: (error) {
        state = AsyncValue.error(error, StackTrace.current);
        return false;
      },
    );
  }

  Future<bool> monitorMultipleMetrics(List<MonitoringDataPoint> metrics) async {
    final result = await _apiService.monitorMultipleMetrics(metrics);

    return result.when(
      success: (alerts) {
        // Refresh alerts if new ones were generated
        if (alerts.isNotEmpty) {
          loadAlerts();
        }
        return true;
      },
      error: (error) {
        state = AsyncValue.error(error, StackTrace.current);
        return false;
      },
    );
  }

  Future<void> refresh() async {
    await loadAlerts();
  }
}

// Monitoring State Provider
final monitoringProvider = StateNotifierProvider<MonitoringNotifier, AsyncValue<List<ThresholdAlert>>>((ref) {
  final apiService = ref.read(notificationApiServiceProvider);
  return MonitoringNotifier(apiService);
});

// Unread Notification Count Provider
final unreadNotificationCountProvider = Provider<int>((ref) {
  final notificationsAsync = ref.watch(notificationProvider);
  
  return notificationsAsync.when(
    data: (notifications) => notifications.length,
    loading: () => 0,
    error: (_, __) => 0,
  );
});

// Critical Alerts Count Provider
final criticalAlertsCountProvider = Provider<int>((ref) {
  final alertsAsync = ref.watch(monitoringProvider);
  
  return alertsAsync.when(
    data: (alerts) => alerts.where((alert) => 
        alert.severity == 'critical' && !alert.isResolved).length,
    loading: () => 0,
    error: (_, __) => 0,
  );
});

// Combined Notification Badge Count Provider
final notificationBadgeCountProvider = Provider<int>((ref) {
  final unreadCount = ref.watch(unreadNotificationCountProvider);
  final criticalCount = ref.watch(criticalAlertsCountProvider);
  
  return unreadCount + criticalCount;
});
