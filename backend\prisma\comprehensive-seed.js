const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting comprehensive database seeding from database.sql...');

  // Create all roles
  const roles = [
    { name: 'admin', description: 'System Administrator with full access', isSystemRole: true },
    { name: 'property_manager', description: 'Property Manager with property management access', isSystemRole: false },
    { name: 'maintenance_staff', description: 'Maintenance Staff with limited access', isSystemRole: false },
    { name: 'security_guard', description: 'Security Guard with security-related access', isSystemRole: false },
    { name: 'househelp', description: 'House Help with basic property access', isSystemRole: false },
    { name: 'office_manager', description: 'Office Manager with office management access', isSystemRole: false },
    { name: 'site_supervisor', description: 'Site Supervisor with site management access', isSystemRole: false },
  ];

  const createdRoles = {};
  for (const roleData of roles) {
    const role = await prisma.role.upsert({
      where: { name: roleData.name },
      update: {},
      create: roleData,
    });
    createdRoles[roleData.name] = role;
  }
  console.log('✅ All roles created');

  // Create all permissions
  const permissions = [
    { name: 'view_dashboard', description: 'View main dashboard', resource: 'dashboard', action: 'read' },
    { name: 'manage_users', description: 'Manage system users', resource: 'users', action: 'write' },
    { name: 'manage_roles', description: 'Manage user roles', resource: 'roles', action: 'write' },
    { name: 'manage_permissions', description: 'Manage permissions', resource: 'permissions', action: 'write' },
    { name: 'view_properties', description: 'View properties', resource: 'properties', action: 'read' },
    { name: 'manage_properties', description: 'Manage properties', resource: 'properties', action: 'write' },
    { name: 'view_maintenance', description: 'View maintenance issues', resource: 'maintenance', action: 'read' },
    { name: 'manage_maintenance', description: 'Manage maintenance issues', resource: 'maintenance', action: 'write' },
    { name: 'view_attendance', description: 'View attendance records', resource: 'attendance', action: 'read' },
    { name: 'manage_attendance', description: 'Manage attendance records', resource: 'attendance', action: 'write' },
    { name: 'view_security', description: 'View security logs', resource: 'security', action: 'read' },
    { name: 'manage_security', description: 'Manage security logs', resource: 'security', action: 'write' },
    { name: 'view_reports', description: 'View system reports', resource: 'reports', action: 'read' },
    { name: 'manage_thresholds', description: 'Manage system thresholds', resource: 'thresholds', action: 'write' },
    { name: 'view_function_processes', description: 'View function processes', resource: 'function_processes', action: 'read' },
    { name: 'manage_function_processes', description: 'Manage function processes', resource: 'function_processes', action: 'write' },
  ];

  const createdPermissions = {};
  for (const permData of permissions) {
    const permission = await prisma.permission.upsert({
      where: { name: permData.name },
      update: {},
      create: permData,
    });
    createdPermissions[permData.name] = permission;
  }
  console.log('✅ All permissions created');

  // Create role-permission mappings
  // Admin gets all permissions
  for (const permission of Object.values(createdPermissions)) {
    await prisma.rolePermission.upsert({
      where: {
        roleId_permissionId: {
          roleId: createdRoles.admin.id,
          permissionId: permission.id,
        },
      },
      update: {},
      create: {
        roleId: createdRoles.admin.id,
        permissionId: permission.id,
      },
    });
  }

  // Property Manager permissions
  const propertyManagerPerms = ['view_dashboard', 'view_properties', 'manage_properties', 'view_maintenance', 'manage_maintenance', 'view_reports'];
  for (const permName of propertyManagerPerms) {
    await prisma.rolePermission.upsert({
      where: {
        roleId_permissionId: {
          roleId: createdRoles.property_manager.id,
          permissionId: createdPermissions[permName].id,
        },
      },
      update: {},
      create: {
        roleId: createdRoles.property_manager.id,
        permissionId: createdPermissions[permName].id,
      },
    });
  }

  // Maintenance Staff permissions
  const maintenancePerms = ['view_dashboard', 'view_properties', 'view_maintenance', 'manage_maintenance'];
  for (const permName of maintenancePerms) {
    await prisma.rolePermission.upsert({
      where: {
        roleId_permissionId: {
          roleId: createdRoles.maintenance_staff.id,
          permissionId: createdPermissions[permName].id,
        },
      },
      update: {},
      create: {
        roleId: createdRoles.maintenance_staff.id,
        permissionId: createdPermissions[permName].id,
      },
    });
  }

  // Security Guard permissions
  const securityPerms = ['view_dashboard', 'view_properties', 'view_security', 'manage_security'];
  for (const permName of securityPerms) {
    await prisma.rolePermission.upsert({
      where: {
        roleId_permissionId: {
          roleId: createdRoles.security_guard.id,
          permissionId: createdPermissions[permName].id,
        },
      },
      update: {},
      create: {
        roleId: createdRoles.security_guard.id,
        permissionId: createdPermissions[permName].id,
      },
    });
  }

  // Househelp permissions
  const househelpPerms = ['view_dashboard', 'view_properties', 'view_maintenance'];
  for (const permName of househelpPerms) {
    await prisma.rolePermission.upsert({
      where: {
        roleId_permissionId: {
          roleId: createdRoles.househelp.id,
          permissionId: createdPermissions[permName].id,
        },
      },
      update: {},
      create: {
        roleId: createdRoles.househelp.id,
        permissionId: createdPermissions[permName].id,
      },
    });
  }

  console.log('✅ Role-permission mappings created');

  // Create all users from database.sql
  const hashedPassword = await bcrypt.hash('admin123', 12); // Same password for all test users

  const users = [
    { email: '<EMAIL>', fullName: 'System Administrator', phone: '+91-9876543210', role: 'admin' },
    { email: '<EMAIL>', fullName: 'Property Manager', phone: '+91-9876543211', role: 'property_manager' },
    { email: '<EMAIL>', fullName: 'Maintenance Staff', phone: '+91-9876543212', role: 'maintenance_staff' },
    { email: '<EMAIL>', fullName: 'Security Guard', phone: '+91-9876543213', role: 'security_guard' },
    { email: '<EMAIL>', fullName: 'House Help', phone: '+91-9876543214', role: 'househelp' },
    { email: '<EMAIL>', fullName: 'Office Manager', phone: '+91-9876543215', role: 'office_manager' },
    { email: '<EMAIL>', fullName: 'Site Supervisor', phone: '+91-9876543216', role: 'site_supervisor' },
    { email: '<EMAIL>', fullName: 'Construction Worker 1', phone: '+91-9876543217', role: 'househelp' },
    { email: '<EMAIL>', fullName: 'Construction Worker 2', phone: '+91-9876543218', role: 'househelp' },
    { email: '<EMAIL>', fullName: 'Office Staff 1', phone: '+91-9876543219', role: 'househelp' },
  ];

  const createdUsers = {};
  for (const userData of users) {
    const user = await prisma.user.upsert({
      where: { email: userData.email },
      update: {},
      create: {
        email: userData.email,
        passwordHash: hashedPassword,
        fullName: userData.fullName,
        phone: userData.phone,
        isActive: true,
      },
    });
    createdUsers[userData.email] = user;

    // Assign role to user
    await prisma.userRole.upsert({
      where: {
        userId_roleId: {
          userId: user.id,
          roleId: createdRoles[userData.role].id,
        },
      },
      update: {},
      create: {
        userId: user.id,
        roleId: createdRoles[userData.role].id,
        assignedBy: createdUsers['<EMAIL>']?.id || user.id,
      },
    });
  }

  console.log('✅ All users created with role assignments');

  // Create properties from database.sql - CONSOLIDATED STRUCTURE
  const properties = [
    // Residential properties (no changes)
    { name: 'Main House', type: 'RESIDENTIAL', address: '123 Main Street, Bangalore', description: 'Primary residential property with all amenities', imageUrl: '/modern-house.png' },
    { name: 'Guest House', type: 'RESIDENTIAL', address: '125 Main Street, Bangalore', description: 'Guest accommodation facility', imageUrl: '/cozy-guest-house.png' },
    { name: 'Staff Quarters', type: 'RESIDENTIAL', address: '127 Main Street, Bangalore', description: 'Staff accommodation facility', imageUrl: '/modern-house.png' },

    // Office properties (consolidated from old offices table)
    { name: 'Head Office Main Floor', type: 'OFFICE', address: '456 Business District, Bangalore', description: 'Main office building - Ground Floor', imageUrl: '/modern-office-building.png', capacity: 50, department: 'Administration', location: 'Ground Floor' },
    { name: 'Head Office Second Floor', type: 'OFFICE', address: '456 Business District, Bangalore', description: 'Main office building - Second Floor', imageUrl: '/modern-office-building.png', capacity: 30, department: 'Operations', location: 'Second Floor' },
    { name: 'Branch A Operations', type: 'OFFICE', address: '789 Tech Park, Bangalore', description: 'Branch office location A', imageUrl: '/branch-office-building.png', capacity: 25, department: 'Operations', location: 'Main Floor' },
    { name: 'Branch B Operations', type: 'OFFICE', address: '321 Industrial Area, Bangalore', description: 'Branch office location B', imageUrl: '/branch-office-building.png', capacity: 20, department: 'Operations', location: 'Main Floor' },
  ];

  // Add construction sites as properties (consolidated from old sites table)
  const constructionSites = [
    { name: 'Residential Complex A', type: 'CONSTRUCTION_SITE', parentPropertyName: 'Head Office Main Floor', address: 'Whitefield, Bangalore', description: 'Residential construction project', projectType: 'Residential Construction', startDate: '2024-01-15', expectedEndDate: '2025-06-30', hourlyRateStandard: 350.00, location: 'Whitefield' },
    { name: 'Commercial Plaza B', type: 'CONSTRUCTION_SITE', parentPropertyName: 'Head Office Main Floor', address: 'Electronic City, Bangalore', description: 'Commercial construction project', projectType: 'Commercial Construction', startDate: '2024-03-01', expectedEndDate: '2025-12-31', hourlyRateStandard: 400.00, location: 'Electronic City' },
    { name: 'Infrastructure Project C', type: 'CONSTRUCTION_SITE', parentPropertyName: 'Branch A Operations', address: 'Hebbal, Bangalore', description: 'Infrastructure development project', projectType: 'Infrastructure', startDate: '2024-02-01', expectedEndDate: '2025-08-15', hourlyRateStandard: 450.00, location: 'Hebbal' },
  ];

  const createdProperties = {};
  for (const propData of properties) {
    const property = await prisma.property.create({
      data: {
        name: propData.name,
        type: propData.type,
        address: propData.address,
        description: propData.description,
        imageUrl: propData.imageUrl,
        location: propData.location,

        // Office-specific fields
        ...(propData.type === 'OFFICE' && {
          capacity: propData.capacity,
          department: propData.department,
        }),

        isActive: true,
      },
    });
    createdProperties[propData.name] = property;
  }

  console.log('✅ All properties created');

  // Create construction sites as properties with parent relationships
  for (const siteData of constructionSites) {
    const parentProperty = createdProperties[siteData.parentPropertyName];

    if (parentProperty) {
      const site = await prisma.property.create({
        data: {
          name: siteData.name,
          type: siteData.type,
          parentPropertyId: parentProperty.id,
          address: siteData.address,
          description: siteData.description,
          location: siteData.location,

          // Site-specific fields
          projectType: siteData.projectType,
          startDate: new Date(siteData.startDate),
          expectedEndDate: new Date(siteData.expectedEndDate),
          hourlyRateStandard: siteData.hourlyRateStandard,

          isActive: true,
        },
      });
      createdProperties[siteData.name] = site;
    }
  }

  console.log('✅ All construction sites created as properties');

  // Create property services from database.sql
  const propertyServices = [
    { propertyName: 'Main House', serviceType: 'ELECTRICITY', status: 'OPERATIONAL', notes: 'Generator fuel at 75%' },
    { propertyName: 'Main House', serviceType: 'WATER', status: 'OPERATIONAL', notes: 'All systems normal' },
    { propertyName: 'Main House', serviceType: 'INTERNET', status: 'OPERATIONAL', notes: 'Primary connection stable' },
    { propertyName: 'Main House', serviceType: 'SECURITY', status: 'OPERATIONAL', notes: 'All cameras functional' },
    { propertyName: 'Main House', serviceType: 'OTT', status: 'OPERATIONAL', notes: 'All subscriptions active' },
    { propertyName: 'Guest House', serviceType: 'ELECTRICITY', status: 'WARNING', notes: 'Generator fuel low at 25%' },
    { propertyName: 'Guest House', serviceType: 'WATER', status: 'OPERATIONAL', notes: 'Pump working normally' },
    { propertyName: 'Guest House', serviceType: 'INTERNET', status: 'OPERATIONAL', notes: 'Backup connection active' },
    { propertyName: 'Guest House', serviceType: 'SECURITY', status: 'OPERATIONAL', notes: 'Security system active' },
    { propertyName: 'Staff Quarters', serviceType: 'ELECTRICITY', status: 'OPERATIONAL', notes: 'Grid power stable' },
    { propertyName: 'Staff Quarters', serviceType: 'WATER', status: 'CRITICAL', notes: 'Water pump failure' },
    { propertyName: 'Head Office', serviceType: 'ELECTRICITY', status: 'OPERATIONAL', notes: 'UPS systems normal' },
    { propertyName: 'Head Office', serviceType: 'INTERNET', status: 'OPERATIONAL', notes: 'High-speed connection' },
    { propertyName: 'Head Office', serviceType: 'SECURITY', status: 'OPERATIONAL', notes: 'Access control active' },
  ];

  for (const serviceData of propertyServices) {
    const property = createdProperties[serviceData.propertyName];
    if (property) {
      await prisma.propertyService.create({
        data: {
          propertyId: property.id,
          serviceType: serviceData.serviceType,
          status: serviceData.status,
          notes: serviceData.notes,
          lastChecked: new Date(),
        },
      });
    }
  }

  console.log('✅ Property services created');

  // CONSOLIDATED: Create unified property members (replaces old office_members and site_members)
  const propertyMembers = [
    // Office staff members
    { propertyName: 'Head Office Main Floor', userEmail: '<EMAIL>', role: 'office_manager', position: 'Office Manager', department: 'Administration', startDate: '2024-01-01' },
    { propertyName: 'Head Office Main Floor', userEmail: '<EMAIL>', role: 'admin_staff', position: 'Administrative Assistant', department: 'Administration', startDate: '2024-01-15' },
    { propertyName: 'Branch A Operations', userEmail: '<EMAIL>', role: 'branch_manager', position: 'Branch Manager', department: 'Operations', startDate: '2024-02-01' },

    // Construction site members
    { propertyName: 'Residential Complex A', userEmail: '<EMAIL>', role: 'site_supervisor', position: 'Site Supervisor', hourlyRate: 500.00, startDate: '2024-01-15' },
    { propertyName: 'Residential Complex A', userEmail: '<EMAIL>', role: 'construction_worker', position: 'Construction Worker', hourlyRate: 300.00, startDate: '2024-01-20' },
    { propertyName: 'Residential Complex A', userEmail: '<EMAIL>', role: 'construction_worker', position: 'Construction Worker', hourlyRate: 300.00, startDate: '2024-01-20' },
    { propertyName: 'Commercial Plaza B', userEmail: '<EMAIL>', role: 'site_supervisor', position: 'Site Supervisor', hourlyRate: 500.00, startDate: '2024-03-01' },
    { propertyName: 'Commercial Plaza B', userEmail: '<EMAIL>', role: 'construction_worker', position: 'Construction Worker', hourlyRate: 300.00, startDate: '2024-03-05' },
  ];

  for (const memberData of propertyMembers) {
    const property = createdProperties[memberData.propertyName];
    const user = createdUsers[memberData.userEmail];

    if (property && user) {
      await prisma.propertyMember.create({
        data: {
          propertyId: property.id,
          userId: user.id,
          role: memberData.role,
          position: memberData.position,
          department: memberData.department,
          hourlyRate: memberData.hourlyRate,
          startDate: new Date(memberData.startDate),
          isActive: true,
        },
      });
    }
  }

  console.log('✅ Property members created (unified office and site members)');

  // Create maintenance issues from database.sql
  const maintenanceIssues = [
    { propertyName: 'Staff Quarters', serviceType: 'water', title: 'Water Pump Failure', description: 'Main water pump has stopped working, no water supply to the building', priority: 'CRITICAL', status: 'OPEN', department: 'Plumbing', reportedBy: '<EMAIL>', assignedTo: '<EMAIL>', dueDate: '2025-01-30' },
    { propertyName: 'Guest House', serviceType: 'electricity', title: 'Generator Fuel Low', description: 'Generator fuel level is critically low, needs immediate refilling', priority: 'HIGH', status: 'IN_PROGRESS', department: 'Electrical', reportedBy: '<EMAIL>', assignedTo: '<EMAIL>', dueDate: '2025-01-29' },
    { propertyName: 'Main House', serviceType: 'security', title: 'CCTV Camera Malfunction', description: 'Camera #3 in the garden area is not recording properly', priority: 'MEDIUM', status: 'OPEN', department: 'Security', reportedBy: '<EMAIL>', assignedTo: '<EMAIL>', dueDate: '2025-02-05' },
    { propertyName: 'Head Office', serviceType: 'internet', title: 'Slow Internet Speed', description: 'Internet connection is slower than usual in the second floor', priority: 'LOW', status: 'OPEN', department: 'IT', reportedBy: '<EMAIL>', assignedTo: '<EMAIL>', dueDate: '2025-02-10' },
  ];

  for (const issueData of maintenanceIssues) {
    const property = createdProperties[issueData.propertyName];
    const reporter = createdUsers[issueData.reportedBy];
    const assignee = createdUsers[issueData.assignedTo];

    if (property && reporter) {
      await prisma.maintenanceIssue.create({
        data: {
          propertyId: property.id,
          serviceType: issueData.serviceType,
          title: issueData.title,
          description: issueData.description,
          priority: issueData.priority,
          status: issueData.status,
          department: issueData.department,
          reportedBy: reporter.id,
          assignedTo: assignee?.id,
          dueDate: new Date(issueData.dueDate),
        },
      });
    }
  }

  console.log('✅ Maintenance issues created');

  // CONSOLIDATED: Create unified property attendance (replaces old office_attendance and site_attendance)
  const propertyAttendanceRecords = [
    // Office attendance
    { propertyName: 'Head Office Main Floor', userEmail: '<EMAIL>', date: '2025-01-22', checkInTime: '09:00:00', checkOutTime: '18:00:00', hoursWorked: 8.0, recordedBy: '<EMAIL>' },
    { propertyName: 'Head Office Main Floor', userEmail: '<EMAIL>', date: '2025-01-23', checkInTime: '09:15:00', checkOutTime: '18:00:00', hoursWorked: 7.75, recordedBy: '<EMAIL>' },
    { propertyName: 'Head Office Main Floor', userEmail: '<EMAIL>', date: '2025-01-24', checkInTime: '09:00:00', checkOutTime: '18:00:00', hoursWorked: 8.0, recordedBy: '<EMAIL>' },

    // Construction site attendance
    { propertyName: 'Residential Complex A', userEmail: '<EMAIL>', date: '2025-01-22', checkInTime: '08:00:00', checkOutTime: '17:00:00', hoursWorked: 8.0, recordedBy: '<EMAIL>' },
    { propertyName: 'Residential Complex A', userEmail: '<EMAIL>', date: '2025-01-22', checkInTime: '08:15:00', checkOutTime: '17:00:00', hoursWorked: 7.75, recordedBy: '<EMAIL>' },
    { propertyName: 'Residential Complex A', userEmail: '<EMAIL>', date: '2025-01-23', checkInTime: '08:00:00', checkOutTime: '17:00:00', hoursWorked: 8.0, recordedBy: '<EMAIL>' },
    { propertyName: 'Residential Complex A', userEmail: '<EMAIL>', date: '2025-01-23', checkInTime: '08:00:00', checkOutTime: '16:30:00', hoursWorked: 7.5, recordedBy: '<EMAIL>' },
    { propertyName: 'Residential Complex A', userEmail: '<EMAIL>', date: '2025-01-24', checkInTime: '08:00:00', checkOutTime: '17:00:00', hoursWorked: 8.0, recordedBy: '<EMAIL>' },
    { propertyName: 'Residential Complex A', userEmail: '<EMAIL>', date: '2025-01-24', checkInTime: '08:30:00', checkOutTime: '17:00:00', hoursWorked: 7.5, recordedBy: '<EMAIL>' },
  ];

  for (const attendanceData of propertyAttendanceRecords) {
    const property = createdProperties[attendanceData.propertyName];
    const user = createdUsers[attendanceData.userEmail];
    const recorder = createdUsers[attendanceData.recordedBy];

    if (property && user && recorder) {
      await prisma.propertyAttendance.create({
        data: {
          propertyId: property.id,
          userId: user.id,
          date: new Date(attendanceData.date),
          checkInTime: attendanceData.checkInTime,
          checkOutTime: attendanceData.checkOutTime,
          hoursWorked: attendanceData.hoursWorked,
          recordedBy: recorder.id,
        },
      });
    }
  }

  console.log('✅ Property attendance records created (unified office and site attendance)');

  // Create generator fuel logs from database.sql
  const generatorFuelLogs = [
    { propertyName: 'Main House', fuelLevelLiters: 750.00, consumptionRate: 12.5, runtimeHours: 156.5, efficiencyPercentage: 85.2, lastMaintenance: '2024-12-15', nextMaintenance: '2025-03-15', recordedBy: '<EMAIL>' },
    { propertyName: 'Guest House', fuelLevelLiters: 250.00, consumptionRate: 15.0, runtimeHours: 89.2, efficiencyPercentage: 82.1, lastMaintenance: '2024-11-20', nextMaintenance: '2025-02-20', recordedBy: '<EMAIL>' },
    { propertyName: 'Staff Quarters', fuelLevelLiters: 500.00, consumptionRate: 10.8, runtimeHours: 203.7, efficiencyPercentage: 87.5, lastMaintenance: '2025-01-10', nextMaintenance: '2025-04-10', recordedBy: '<EMAIL>' },
  ];

  for (const fuelData of generatorFuelLogs) {
    const property = createdProperties[fuelData.propertyName];
    const recorder = createdUsers[fuelData.recordedBy];

    if (property && recorder) {
      await prisma.generatorFuelLog.create({
        data: {
          propertyId: property.id,
          fuelLevelLiters: fuelData.fuelLevelLiters,
          consumptionRate: fuelData.consumptionRate,
          runtimeHours: fuelData.runtimeHours,
          efficiencyPercentage: fuelData.efficiencyPercentage,
          lastMaintenance: new Date(fuelData.lastMaintenance),
          nextMaintenance: new Date(fuelData.nextMaintenance),
          notes: 'Generator running smoothly, fuel consumption within normal range',
          recordedBy: recorder.id,
        },
      });
    }
  }

  console.log('✅ Generator fuel logs created');

  // Create diesel additions from database.sql
  const dieselAdditions = [
    { propertyName: 'Main House', quantityLiters: 500.00, costPerLiter: 85.50, totalCost: 42750.00, supplier: 'Bharat Petroleum', receiptNumber: 'BP2025001234', addedBy: '<EMAIL>' },
    { propertyName: 'Guest House', quantityLiters: 300.00, costPerLiter: 86.00, totalCost: 25800.00, supplier: 'Indian Oil', receiptNumber: 'IO2025001567', addedBy: '<EMAIL>' },
    { propertyName: 'Staff Quarters', quantityLiters: 400.00, costPerLiter: 85.75, totalCost: 34300.00, supplier: 'Hindustan Petroleum', receiptNumber: 'HP2025001890', addedBy: '<EMAIL>' },
  ];

  for (const dieselData of dieselAdditions) {
    const property = createdProperties[dieselData.propertyName];
    const addedBy = createdUsers[dieselData.addedBy];

    if (property && addedBy) {
      await prisma.dieselAddition.create({
        data: {
          propertyId: property.id,
          quantityLiters: dieselData.quantityLiters,
          costPerLiter: dieselData.costPerLiter,
          totalCost: dieselData.totalCost,
          supplier: dieselData.supplier,
          receiptNumber: dieselData.receiptNumber,
          addedBy: addedBy.id,
        },
      });
    }
  }

  console.log('✅ Diesel additions created');

  // Create OTT services from database.sql
  const ottServices = [
    { propertyName: 'Main House', serviceName: 'Netflix', subscriptionType: 'Premium', monthlyCost: 799.00, renewalDate: '2025-02-15', status: 'ACTIVE', notes: '4K streaming plan' },
    { propertyName: 'Main House', serviceName: 'Amazon Prime Video', subscriptionType: 'Annual', monthlyCost: 1499.00, renewalDate: '2025-08-20', status: 'ACTIVE', notes: 'Annual subscription' },
    { propertyName: 'Main House', serviceName: 'Disney+ Hotstar', subscriptionType: 'Super', monthlyCost: 899.00, renewalDate: '2025-03-10', status: 'ACTIVE', notes: 'Sports and movies' },
    { propertyName: 'Guest House', serviceName: 'Netflix', subscriptionType: 'Basic', monthlyCost: 199.00, renewalDate: '2025-02-28', status: 'ACTIVE', notes: 'Basic plan' },
    { propertyName: 'Guest House', serviceName: 'YouTube Premium', subscriptionType: 'Individual', monthlyCost: 129.00, renewalDate: '2025-02-05', status: 'ACTIVE', notes: 'Ad-free experience' },
  ];

  for (const ottData of ottServices) {
    const property = createdProperties[ottData.propertyName];

    if (property) {
      await prisma.ottService.create({
        data: {
          propertyId: property.id,
          serviceName: ottData.serviceName,
          subscriptionType: ottData.subscriptionType,
          monthlyCost: ottData.monthlyCost,
          renewalDate: new Date(ottData.renewalDate),
          status: ottData.status,
          notes: ottData.notes,
        },
      });
    }
  }

  console.log('✅ OTT services created');

  // Create security guard logs from database.sql
  const securityGuardLogs = [
    { propertyName: 'Main House', guardName: 'Rajesh Kumar', shiftStart: '2025-01-28T22:00:00+05:30', shiftEnd: '2025-01-29T06:00:00+05:30', patrolRounds: 4, incidentsReported: 0, visitorsLogged: 2, notes: 'Quiet night, two late visitors logged' },
    { propertyName: 'Guest House', guardName: 'Suresh Babu', shiftStart: '2025-01-28T18:00:00+05:30', shiftEnd: '2025-01-29T02:00:00+05:30', patrolRounds: 3, incidentsReported: 1, visitorsLogged: 1, notes: 'Minor incident: stray dog in premises, safely removed' },
    { propertyName: 'Main House', guardName: 'Rajesh Kumar', shiftStart: '2025-01-29T22:00:00+05:30', shiftEnd: '2025-01-30T06:00:00+05:30', patrolRounds: 4, incidentsReported: 0, visitorsLogged: 0, notes: 'All systems normal, no incidents' },
    { propertyName: 'Head Office', guardName: 'Mohan Singh', shiftStart: '2025-01-29T20:00:00+05:30', shiftEnd: '2025-01-30T08:00:00+05:30', patrolRounds: 6, incidentsReported: 0, visitorsLogged: 3, notes: 'Office security normal, late working staff logged' },
  ];

  for (const guardData of securityGuardLogs) {
    const property = createdProperties[guardData.propertyName];

    if (property) {
      await prisma.securityGuardLog.create({
        data: {
          propertyId: property.id,
          guardName: guardData.guardName,
          shiftStart: new Date(guardData.shiftStart),
          shiftEnd: new Date(guardData.shiftEnd),
          patrolRounds: guardData.patrolRounds,
          incidentsReported: guardData.incidentsReported,
          visitorsLogged: guardData.visitorsLogged,
          notes: guardData.notes,
        },
      });
    }
  }

  console.log('✅ Security guard logs created');

  // Create uptime reports from database.sql
  const uptimeReports = [
    { propertyName: 'Main House', serviceType: 'ELECTRICITY', date: '2025-01-28', uptimePercentage: 99.5, downtimeMinutes: 7, incidentsCount: 1, notes: 'Brief power fluctuation at 14:30' },
    { propertyName: 'Main House', serviceType: 'INTERNET', date: '2025-01-28', uptimePercentage: 100.0, downtimeMinutes: 0, incidentsCount: 0, notes: 'Stable connection throughout the day' },
    { propertyName: 'Guest House', serviceType: 'ELECTRICITY', date: '2025-01-28', uptimePercentage: 95.2, downtimeMinutes: 69, incidentsCount: 2, notes: 'Generator maintenance caused downtime' },
    { propertyName: 'Guest House', serviceType: 'INTERNET', date: '2025-01-28', uptimePercentage: 98.8, downtimeMinutes: 17, incidentsCount: 1, notes: 'Router restart required once' },
    { propertyName: 'Head Office', serviceType: 'ELECTRICITY', date: '2025-01-28', uptimePercentage: 100.0, downtimeMinutes: 0, incidentsCount: 0, notes: 'UPS systems working perfectly' },
    { propertyName: 'Head Office', serviceType: 'INTERNET', date: '2025-01-28', uptimePercentage: 99.9, downtimeMinutes: 1, incidentsCount: 0, notes: 'Excellent connectivity' },
  ];

  for (const uptimeData of uptimeReports) {
    const property = createdProperties[uptimeData.propertyName];

    if (property) {
      await prisma.uptimeReport.create({
        data: {
          propertyId: property.id,
          serviceType: uptimeData.serviceType,
          date: new Date(uptimeData.date),
          uptimePercentage: uptimeData.uptimePercentage,
          downtimeMinutes: uptimeData.downtimeMinutes,
          incidentsCount: uptimeData.incidentsCount,
          notes: uptimeData.notes,
        },
      });
    }
  }

  console.log('✅ Uptime reports created');

  // Create function processes from database.sql
  const functionProcesses = [
    { name: 'Daily Status Check', description: 'Automated daily status check for all property services', category: 'Monitoring', inputParameters: { properties: 'all', services: 'all' }, outputParameters: { status_summary: 'object', alerts: 'array' }, executionFrequency: 'daily', status: 'ACTIVE' },
    { name: 'Fuel Level Monitor', description: 'Monitor generator fuel levels and trigger alerts', category: 'Monitoring', inputParameters: { threshold_warning: 30, threshold_critical: 15 }, outputParameters: { fuel_levels: 'array', alerts: 'array' }, executionFrequency: 'hourly', status: 'ACTIVE' },
    { name: 'Maintenance Escalation', description: 'Escalate overdue maintenance issues', category: 'Maintenance', inputParameters: { overdue_hours: 24, escalation_levels: 3 }, outputParameters: { escalated_issues: 'array' }, executionFrequency: 'hourly', status: 'ACTIVE' },
    { name: 'Attendance Report Generator', description: 'Generate daily attendance reports', category: 'Reporting', inputParameters: { date: 'string', location_type: 'string' }, outputParameters: { attendance_summary: 'object' }, executionFrequency: 'daily', status: 'ACTIVE' },
  ];

  const createdFunctionProcesses = {};
  for (const processData of functionProcesses) {
    const functionProcess = await prisma.functionProcess.create({
      data: {
        name: processData.name,
        description: processData.description,
        category: processData.category,
        inputParameters: processData.inputParameters,
        outputParameters: processData.outputParameters,
        executionFrequency: processData.executionFrequency,
        status: processData.status,
      },
    });
    createdFunctionProcesses[processData.name] = functionProcess;
  }

  console.log('✅ Function processes created');

  // Create function process logs from database.sql
  const functionProcessLogs = [
    { processName: 'Daily Status Check', status: 'SUCCESS', inputData: { date: '2025-01-29', properties: 'all' }, outputData: { total_properties: 6, operational: 4, warning: 1, critical: 1 }, executionDurationMs: 2340 },
    { processName: 'Fuel Level Monitor', status: 'SUCCESS', inputData: { threshold_warning: 30, threshold_critical: 15 }, outputData: { alerts: [{ property: 'Guest House', fuel_level: 25, status: 'warning' }] }, executionDurationMs: 1250 },
    { processName: 'Maintenance Escalation', status: 'SUCCESS', inputData: { overdue_hours: 24 }, outputData: { escalated_issues: [{ issue_id: 'maintenance_001', escalation_level: 1 }] }, executionDurationMs: 890 },
    { processName: 'Attendance Report Generator', status: 'SUCCESS', inputData: { date: '2025-01-29' }, outputData: { total_attendance: 15, sites: 8, offices: 7 }, executionDurationMs: 1560 },
  ];

  for (const logData of functionProcessLogs) {
    const functionProcess = createdFunctionProcesses[logData.processName];

    if (functionProcess) {
      await prisma.functionProcessLog.create({
        data: {
          functionProcessId: functionProcess.id,
          status: logData.status,
          inputData: logData.inputData,
          outputData: logData.outputData,
          executionDurationMs: logData.executionDurationMs,
        },
      });
    }
  }

  console.log('✅ Function process logs created');

  // Create threshold configurations from database.sql
  const thresholdConfigs = [
    { serviceType: 'electricity', metricName: 'generator_fuel_level', warningThreshold: 30.0, criticalThreshold: 15.0, unit: 'percentage', description: 'Generator fuel level thresholds' },
    { serviceType: 'electricity', metricName: 'ups_battery_level', warningThreshold: 25.0, criticalThreshold: 10.0, unit: 'percentage', description: 'UPS battery level thresholds' },
    { serviceType: 'water', metricName: 'tank_level', warningThreshold: 20.0, criticalThreshold: 10.0, unit: 'percentage', description: 'Water tank level thresholds' },
    { serviceType: 'water', metricName: 'pressure', warningThreshold: 15.0, criticalThreshold: 10.0, unit: 'psi', description: 'Water pressure thresholds' },
    { serviceType: 'internet', metricName: 'uptime', warningThreshold: 95.0, criticalThreshold: 90.0, unit: 'percentage', description: 'Internet uptime thresholds' },
    { serviceType: 'internet', metricName: 'speed', warningThreshold: 50.0, criticalThreshold: 25.0, unit: 'mbps', description: 'Internet speed thresholds' },
    { serviceType: 'security', metricName: 'camera_uptime', warningThreshold: 95.0, criticalThreshold: 85.0, unit: 'percentage', description: 'Security camera uptime thresholds' },
    { serviceType: 'maintenance', metricName: 'response_time', warningThreshold: 4.0, criticalThreshold: 8.0, unit: 'hours', description: 'Maintenance response time thresholds' },
  ];

  for (const thresholdData of thresholdConfigs) {
    await prisma.thresholdConfig.create({
      data: {
        serviceType: thresholdData.serviceType,
        metricName: thresholdData.metricName,
        warningThreshold: thresholdData.warningThreshold,
        criticalThreshold: thresholdData.criticalThreshold,
        unit: thresholdData.unit,
        description: thresholdData.description,
        isActive: true,
      },
    });
  }

  console.log('✅ Threshold configurations created');

  // Create alerts for dashboard
  const alerts = [
    { propertyName: 'Staff Quarters', type: 'maintenance', severity: 'critical', title: 'Water Pump Failure', message: 'Main water pump has stopped working, no water supply to the building', isResolved: false },
    { propertyName: 'Guest House', type: 'fuel', severity: 'warning', title: 'Low Generator Fuel', message: 'Generator fuel level is at 25%, needs refilling soon', isResolved: false },
    { propertyName: 'Main House', type: 'security', severity: 'medium', title: 'CCTV Camera Issue', message: 'Camera #3 in garden area is not recording properly', isResolved: false },
    { propertyName: 'Head Office Main Floor', type: 'internet', severity: 'low', title: 'Slow Internet Speed', message: 'Internet connection slower than usual on second floor', isResolved: false },
    { propertyName: 'Residential Complex A', type: 'attendance', severity: 'medium', title: 'Late Attendance', message: 'Worker2 has been consistently late this week', isResolved: false },
  ];

  for (const alertData of alerts) {
    const property = createdProperties[alertData.propertyName];
    if (property) {
      await prisma.alert.create({
        data: {
          propertyId: property.id,
          type: alertData.type,
          severity: alertData.severity,
          title: alertData.title,
          message: alertData.message,
          isResolved: alertData.isResolved,
          createdAt: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000), // Random time in last 24 hours
        },
      });
    }
  }

  console.log('✅ Alerts created for dashboard');

  // Create notifications for dashboard
  const notifications = [
    { type: 'maintenance', title: 'Critical Water Issue', message: 'Water pump failure at Staff Quarters requires immediate attention', priority: 'high', targetRoles: ['admin', 'maintenance_staff'], propertyName: 'Staff Quarters' },
    { type: 'fuel', title: 'Fuel Refill Required', message: 'Generator fuel at Guest House is running low (25%)', priority: 'medium', targetRoles: ['admin', 'maintenance_staff'], propertyName: 'Guest House' },
    { type: 'security', title: 'Security Camera Malfunction', message: 'CCTV Camera #3 needs repair at Main House', priority: 'medium', targetRoles: ['admin', 'security_guard'], propertyName: 'Main House' },
    { type: 'attendance', title: 'Attendance Alert', message: 'Multiple late arrivals detected at construction sites', priority: 'low', targetRoles: ['admin', 'site_supervisor'], propertyName: 'Residential Complex A' },
    { type: 'system', title: 'Daily Report Ready', message: 'Daily system status report has been generated', priority: 'low', targetRoles: ['admin'], propertyName: null },
  ];

  for (const notifData of notifications) {
    const property = notifData.propertyName ? createdProperties[notifData.propertyName] : null;
    await prisma.notification.create({
      data: {
        type: notifData.type,
        title: notifData.title,
        message: notifData.message,
        priority: notifData.priority,
        targetRoles: notifData.targetRoles,
        targetUsers: [],
        propertyId: property?.id,
        isRead: false,
        createdAt: new Date(Date.now() - Math.random() * 12 * 60 * 60 * 1000), // Random time in last 12 hours
      },
    });
  }

  console.log('✅ Notifications created for dashboard');

  // Create monitoring data for dashboard charts
  const monitoringData = [];
  const allProperties = Object.values(createdProperties);
  const serviceTypes = ['electricity', 'water', 'internet', 'security'];
  const metrics = {
    electricity: ['fuel_level', 'consumption_rate', 'efficiency'],
    water: ['tank_level', 'pressure', 'flow_rate'],
    internet: ['uptime', 'speed', 'latency'],
    security: ['camera_uptime', 'incidents', 'patrol_rounds']
  };

  // Generate monitoring data for the last 7 days
  for (let i = 0; i < 7; i++) {
    const date = new Date();
    date.setDate(date.getDate() - i);

    for (const property of allProperties.slice(0, 5)) { // First 5 properties
      for (const serviceType of serviceTypes) {
        for (const metric of metrics[serviceType]) {
          let value;
          switch (metric) {
            case 'fuel_level':
              value = 20 + Math.random() * 60; // 20-80%
              break;
            case 'consumption_rate':
              value = 8 + Math.random() * 10; // 8-18 L/hr
              break;
            case 'efficiency':
              value = 75 + Math.random() * 20; // 75-95%
              break;
            case 'tank_level':
              value = 15 + Math.random() * 70; // 15-85%
              break;
            case 'pressure':
              value = 12 + Math.random() * 8; // 12-20 PSI
              break;
            case 'flow_rate':
              value = 5 + Math.random() * 15; // 5-20 L/min
              break;
            case 'uptime':
              value = 95 + Math.random() * 5; // 95-100%
              break;
            case 'speed':
              value = 40 + Math.random() * 60; // 40-100 Mbps
              break;
            case 'latency':
              value = 5 + Math.random() * 15; // 5-20 ms
              break;
            case 'camera_uptime':
              value = 90 + Math.random() * 10; // 90-100%
              break;
            case 'incidents':
              value = Math.floor(Math.random() * 3); // 0-2 incidents
              break;
            case 'patrol_rounds':
              value = 3 + Math.floor(Math.random() * 4); // 3-6 rounds
              break;
            default:
              value = Math.random() * 100;
          }

          monitoringData.push({
            propertyId: property.id,
            serviceType,
            metricName: metric,
            value: parseFloat(value.toFixed(2)),
            unit: getMetricUnit(metric),
            timestamp: date,
          });
        }
      }
    }
  }

  // Insert monitoring data in batches
  const batchSize = 100;
  for (let i = 0; i < monitoringData.length; i += batchSize) {
    const batch = monitoringData.slice(i, i + batchSize);
    await prisma.monitoringData.createMany({
      data: batch,
    });
  }

  console.log(`✅ Monitoring data created (${monitoringData.length} records)`);

  // Create threshold alerts based on monitoring data
  const thresholdAlerts = [
    { propertyName: 'Guest House', serviceType: 'electricity', metricName: 'fuel_level', currentValue: 25.0, thresholdValue: 30.0, thresholdType: 'min', severity: 'warning', message: 'Generator fuel level below warning threshold' },
    { propertyName: 'Staff Quarters', serviceType: 'water', metricName: 'tank_level', currentValue: 8.0, thresholdValue: 10.0, thresholdType: 'min', severity: 'critical', message: 'Water tank level critically low' },
    { propertyName: 'Head Office Second Floor', serviceType: 'internet', metricName: 'speed', currentValue: 35.0, thresholdValue: 50.0, thresholdType: 'min', severity: 'warning', message: 'Internet speed below expected threshold' },
  ];

  for (const alertData of thresholdAlerts) {
    const property = createdProperties[alertData.propertyName];
    if (property) {
      await prisma.thresholdAlert.create({
        data: {
          propertyId: property.id,
          serviceType: alertData.serviceType,
          metricName: alertData.metricName,
          currentValue: alertData.currentValue,
          thresholdValue: alertData.thresholdValue,
          thresholdType: alertData.thresholdType,
          severity: alertData.severity,
          message: alertData.message,
          isResolved: false,
          createdAt: new Date(Date.now() - Math.random() * 6 * 60 * 60 * 1000), // Random time in last 6 hours
        },
      });
    }
  }

  console.log('✅ Threshold alerts created');

  // Create user notifications for dashboard
  for (const user of Object.values(createdUsers)) {
    if (user.email === '<EMAIL>') {
      // Admin gets all notifications
      const allNotifications = await prisma.notification.findMany();
      for (const notification of allNotifications) {
        await prisma.userNotification.create({
          data: {
            userId: user.id,
            notificationId: notification.id,
            isRead: Math.random() > 0.7, // 30% read, 70% unread
            readAt: Math.random() > 0.7 ? new Date() : null,
          },
        });
      }
    } else {
      // Other users get role-specific notifications
      const userRoles = await prisma.userRole.findMany({
        where: { userId: user.id },
        include: { role: true },
      });

      const userRoleNames = userRoles.map(ur => ur.role.name);
      const relevantNotifications = await prisma.notification.findMany({
        where: {
          targetRoles: {
            hasSome: userRoleNames,
          },
        },
      });

      for (const notification of relevantNotifications) {
        await prisma.userNotification.create({
          data: {
            userId: user.id,
            notificationId: notification.id,
            isRead: Math.random() > 0.8, // 20% read, 80% unread
            readAt: Math.random() > 0.8 ? new Date() : null,
          },
        });
      }
    }
  }

  console.log('✅ User notifications created');

  function getMetricUnit(metric) {
    const units = {
      fuel_level: 'percentage',
      consumption_rate: 'L/hr',
      efficiency: 'percentage',
      tank_level: 'percentage',
      pressure: 'PSI',
      flow_rate: 'L/min',
      uptime: 'percentage',
      speed: 'Mbps',
      latency: 'ms',
      camera_uptime: 'percentage',
      incidents: 'count',
      patrol_rounds: 'count'
    };
    return units[metric] || 'unit';
  }

  console.log('🎉 COMPREHENSIVE database seeding completed successfully!');
  console.log('');
  console.log('📊 CONSOLIDATED Summary (offices & sites merged into properties):');
  console.log(`- Roles: ${Object.keys(createdRoles).length}`);
  console.log(`- Permissions: ${Object.keys(createdPermissions).length}`);
  console.log(`- Users: ${Object.keys(createdUsers).length}`);
  console.log(`- Properties: ${Object.keys(createdProperties).length} (3 residential + 4 office + 3 construction_site)`);
  console.log(`- Property Services: ${propertyServices.length}`);
  console.log(`- Property Members: ${propertyMembers.length} (unified office & site members)`);
  console.log(`- Property Attendance: ${propertyAttendanceRecords.length} (unified office & site attendance)`);
  console.log(`- Generator Fuel Logs: ${generatorFuelLogs.length}`);
  console.log(`- Diesel Additions: ${dieselAdditions.length}`);
  console.log(`- OTT Services: ${ottServices.length}`);
  console.log(`- Security Guard Logs: ${securityGuardLogs.length}`);
  console.log(`- Uptime Reports: ${uptimeReports.length}`);
  console.log(`- Maintenance Issues: ${maintenanceIssues.length}`);
  console.log(`- Function Processes: ${functionProcesses.length}`);
  console.log(`- Function Process Logs: ${functionProcessLogs.length}`);
  console.log(`- Threshold Configurations: ${thresholdConfigs.length}`);
  console.log(`- Alerts: ${alerts.length}`);
  console.log(`- Notifications: ${notifications.length}`);
  console.log(`- Monitoring Data Points: ${monitoringData.length}`);
  console.log(`- Threshold Alerts: ${thresholdAlerts.length}`);
  console.log('');
  console.log('🔐 Test Credentials (All users have password: admin123):');
  console.log('- <EMAIL> (System Administrator)');
  console.log('- <EMAIL> (Property Manager)');
  console.log('- <EMAIL> (Maintenance Staff)');
  console.log('- <EMAIL> (Security Guard)');
  console.log('- <EMAIL> (House Help)');
  console.log('- <EMAIL> (Office Manager)');
  console.log('- <EMAIL> (Site Supervisor)');
  console.log('- <EMAIL> (Construction Worker 1)');
  console.log('- <EMAIL> (Construction Worker 2)');
  console.log('- <EMAIL> (Office Staff 1)');
  console.log('');
  console.log('🏢 CONSOLIDATED Properties Structure:');
  console.log('- Main House (Residential) - Full services, fuel logs, OTT, security');
  console.log('- Guest House (Residential) - Warning status, low fuel, basic OTT');
  console.log('- Staff Quarters (Residential) - Critical water issue, maintenance needed');
  console.log('- Head Office Main Floor (Office) - Office operations, staff attendance');
  console.log('- Head Office Second Floor (Office) - Operations department');
  console.log('- Branch A Operations (Office) - Branch operations');
  console.log('- Branch B Operations (Office) - Branch operations');
  console.log('- Residential Complex A (Construction Site) - Under Head Office Main Floor');
  console.log('- Commercial Plaza B (Construction Site) - Under Head Office Main Floor');
  console.log('- Infrastructure Project C (Construction Site) - Under Branch A Operations');
  console.log('');
  console.log('📈 UNIFIED Business Data Available:');
  console.log('- Single properties table with type-specific fields');
  console.log('- Unified member management across all property types');
  console.log('- Consolidated attendance tracking system');
  console.log('- Hierarchical property relationships (sites → offices)');
  console.log('- Real fuel consumption rates and efficiency metrics');
  console.log('- Actual supplier costs and receipt tracking');
  console.log('- Detailed security patrol and incident logs');
  console.log('- Service uptime percentages and downtime tracking');
  console.log('- Construction site attendance with hourly rates');
  console.log('- Office staff attendance and management hierarchy');
  console.log('- Maintenance issues with proper escalation');
  console.log('- OTT subscription management with renewal tracking');
  console.log('');
  console.log('🚀 SCALABILITY BENEFITS:');
  console.log('- 57% fewer tables (7 → 3 tables)');
  console.log('- Simplified resource management');
  console.log('- Unified API endpoints');
  console.log('- Better query performance');
  console.log('- Easy to add new property types');
  console.log('');
  console.log('✨ Consolidated structure implemented successfully!');
}

main()
  .catch((e) => {
    console.error('❌ Error during comprehensive seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
