// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'generator_fuel.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GeneratorFuelLog _$GeneratorFuelLogFromJson(Map<String, dynamic> json) =>
    GeneratorFuelLog(
      id: json['id'] as String,
      propertyId: json['property_id'] as String,
      fuelLevelLiters: (json['fuel_level_liters'] as num).toDouble(),
      consumptionRate: (json['consumption_rate'] as num?)?.toDouble(),
      runtimeHours: (json['runtime_hours'] as num?)?.toDouble(),
      efficiencyPercentage: (json['efficiency_percentage'] as num?)?.toDouble(),
      notes: json['notes'] as String?,
      recordedAt: DateTime.parse(json['recorded_at'] as String),
    );

Map<String, dynamic> _$GeneratorFuelLogToJson(GeneratorFuelLog instance) =>
    <String, dynamic>{
      'id': instance.id,
      'property_id': instance.propertyId,
      'fuel_level_liters': instance.fuelLevelLiters,
      'consumption_rate': instance.consumptionRate,
      'runtime_hours': instance.runtimeHours,
      'efficiency_percentage': instance.efficiencyPercentage,
      'notes': instance.notes,
      'recorded_at': instance.recordedAt.toIso8601String(),
    };
