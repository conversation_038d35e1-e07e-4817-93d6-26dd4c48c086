#!/usr/bin/env node

const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function resetAllPasswords() {
  try {
    console.log('🔐 Starting password reset for all users...\n');

    // The new password to set for all users
    const newPassword = '123456';
    
    // Hash the password using the same method as your backend (salt rounds: 12)
    console.log('🔒 Hashing password...');
    const hashedPassword = await bcrypt.hash(newPassword, 12);
    console.log('✅ Password hashed successfully\n');

    // Get all users first to show what we're updating
    const users = await prisma.user.findMany({
      select: {
        id: true,
        email: true,
        fullName: true,
        isActive: true,
      },
    });

    console.log(`📊 Found ${users.length} users in the database:`);
    users.forEach((user, index) => {
      console.log(`${index + 1}. ${user.email} (${user.fullName}) - ${user.isActive ? 'Active' : 'Inactive'}`);
    });
    console.log('');

    // Ask for confirmation
    const readline = require('readline');
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
    });

    const confirmation = await new Promise((resolve) => {
      rl.question(`⚠️  Are you sure you want to reset passwords for ALL ${users.length} users to "${newPassword}"? (yes/no): `, (answer) => {
        rl.close();
        resolve(answer.toLowerCase());
      });
    });

    if (confirmation !== 'yes' && confirmation !== 'y') {
      console.log('❌ Password reset cancelled.');
      return;
    }

    // Update all user passwords
    console.log('🔄 Updating passwords...');
    const result = await prisma.user.updateMany({
      data: {
        passwordHash: hashedPassword,
        updatedAt: new Date(),
      },
    });

    console.log(`✅ Successfully updated passwords for ${result.count} users`);
    console.log(`🔑 All users can now login with password: "${newPassword}"`);
    console.log('\n📝 Users and their emails:');
    
    users.forEach((user, index) => {
      console.log(`${index + 1}. Email: ${user.email} | Password: ${newPassword}`);
    });

  } catch (error) {
    console.error('❌ Error resetting passwords:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
resetAllPasswords();
