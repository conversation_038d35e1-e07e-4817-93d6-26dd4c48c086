import 'package:flutter_test/flutter_test.dart';
import 'package:srsr_property_management/core/constants/property_type_constants.dart';
import 'package:srsr_property_management/shared/models/property.dart';

void main() {
  group('Property Type Tests', () {
    test('should handle all property types from backend', () {
      // Test residential
      final residential = PropertyTypeConstants.fromString('residential');
      expect(residential, PropertyType.residential);
      expect(PropertyTypeConstants.typeToString(residential), 'residential');

      // Test office
      final office = PropertyTypeConstants.fromString('office');
      expect(office, PropertyType.office);
      expect(PropertyTypeConstants.typeToString(office), 'office');

      // Test construction_site (the problematic one)
      final constructionSite = PropertyTypeConstants.fromString('construction_site');
      expect(constructionSite, PropertyType.constructionSite);
      expect(PropertyTypeConstants.typeToString(constructionSite), 'construction_site');
    });

    test('should provide display names for all property types', () {
      expect(PropertyTypeConstants.getDisplayName(PropertyType.residential), 'Residential');
      expect(PropertyTypeConstants.getDisplayName(PropertyType.office), 'Office');
      expect(PropertyTypeConstants.getDisplayName(PropertyType.constructionSite), 'Construction Site');
    });

    test('should provide default services for construction sites', () {
      final services = PropertyTypeConstants.getDefaultServices(PropertyType.constructionSite);
      expect(services, isNotEmpty);
      expect(services, contains('electricity'));
      expect(services, contains('security'));
      expect(services, contains('safety_equipment'));
    });

    test('should provide construction site specific features', () {
      final features = PropertyTypeConstants.getAvailableFeatures(PropertyType.constructionSite);
      expect(features, contains('project_management'));
      expect(features, contains('equipment_tracking'));
      expect(features, contains('safety_monitoring'));
    });

    test('should handle property model with construction site type', () {
      final property = Property(
        id: 'test-id',
        name: 'Test Construction Site',
        type: 'construction_site',
        address: 'Test Address',
        description: 'Test Description',
        imageUrl: null,
        isActive: true,
        services: [],
        createdAt: DateTime.now(),
      );

      expect(property.isConstructionSite, true);
      expect(property.isResidential, false);
      expect(property.isOffice, false);
      expect(property.displayType, 'Construction Site');
      expect(property.primaryColor.value, 0xFFE65100); // Orange color
    });

    test('should throw error for invalid property type', () {
      expect(
        () => PropertyTypeConstants.fromString('invalid_type'),
        throwsA(isA<ArgumentError>()),
      );
    });
  });
}
