import 'package:flutter/material.dart';

enum AttendanceFilter {
  all,
  present,
  absent,
  late,
  earlyLeave,
}

class AttendanceFilterChips extends StatelessWidget {
  final AttendanceFilter selectedFilter;
  final Function(AttendanceFilter) onFilterChanged;

  const AttendanceFilterChips({
    super.key,
    required this.selectedFilter,
    required this.onFilterChanged,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: AttendanceFilter.values.map((filter) {
          return Padding(
            padding: const EdgeInsets.only(right: 8.0),
            child: FilterChip(
              label: Text(_getFilterLabel(filter)),
              selected: selectedFilter == filter,
              onSelected: (selected) {
                if (selected) {
                  onFilterChanged(filter);
                }
              },
              selectedColor: Theme.of(context).primaryColor.withValues(alpha:0.2),
              checkmarkColor: Theme.of(context).primaryColor,
            ),
          );
        }).toList(),
      ),
    );
  }

  String _getFilterLabel(AttendanceFilter filter) {
    switch (filter) {
      case AttendanceFilter.all:
        return 'All';
      case AttendanceFilter.present:
        return 'Present';
      case AttendanceFilter.absent:
        return 'Absent';
      case AttendanceFilter.late:
        return 'Late';
      case AttendanceFilter.earlyLeave:
        return 'Early Leave';
    }
  }
}

class AttendanceFilterChip extends StatelessWidget {
  final String label;
  final bool isSelected;
  final VoidCallback onTap;
  final Color? selectedColor;

  const AttendanceFilterChip({
    super.key,
    required this.label,
    required this.isSelected,
    required this.onTap,
    this.selectedColor,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected 
              ? (selectedColor ?? Theme.of(context).primaryColor)
              : Colors.grey.shade200,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected 
                ? (selectedColor ?? Theme.of(context).primaryColor)
                : Colors.grey.shade300,
          ),
        ),
        child: Text(
          label,
          style: TextStyle(
            color: isSelected ? Colors.white : Colors.black87,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
          ),
        ),
      ),
    );
  }
}
