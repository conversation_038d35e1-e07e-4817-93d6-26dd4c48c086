import 'package:flutter/material.dart';
import 'package:reactive_forms/reactive_forms.dart';
import '../../data/role_management_api_service.dart';

class CreateRoleDialog extends StatefulWidget {
  final Function(CreateRoleRequest)? onRoleCreated;

  const CreateRoleDialog({
    super.key,
    this.onRoleCreated,
  });

  @override
  State<CreateRoleDialog> createState() => _CreateRoleDialogState();
}

class _CreateRoleDialogState extends State<CreateRoleDialog> {
  late FormGroup form;
  bool isLoading = false;

  @override
  void initState() {
    super.initState();
    form = FormGroup({
      'name': FormControl<String>(
        validators: [
          Validators.required,
          Validators.minLength(2),
          Validators.maxLength(50),
        ],
      ),
      'description': FormControl<String>(
        validators: [
          Validators.maxLength(200),
        ],
      ),
      'isActive': FormControl<bool>(value: true),
    });
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Create New Role'),
      content: SizedBox(
        width: 400,
        child: ReactiveForm(
          formGroup: form,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Role Name
              ReactiveTextField<String>(
                formControlName: 'name',
                decoration: const InputDecoration(
                  labelText: 'Role Name *',
                  hintText: 'Enter role name',
                  border: OutlineInputBorder(),
                ),
                validationMessages: {
                  'required': (error) => 'Role name is required',
                  'minLength': (error) => 'Role name must be at least 2 characters',
                  'maxLength': (error) => 'Role name cannot exceed 50 characters',
                },
              ),

              const SizedBox(height: 16),

              // Description
              ReactiveTextField<String>(
                formControlName: 'description',
                decoration: const InputDecoration(
                  labelText: 'Description',
                  hintText: 'Enter role description',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
                validationMessages: {
                  'maxLength': (error) => 'Description cannot exceed 200 characters',
                },
              ),

              const SizedBox(height: 16),

              // Active Status
              ReactiveCheckboxListTile(
                formControlName: 'isActive',
                title: const Text('Active Role'),
                subtitle: const Text('Role will be available for assignment'),
                controlAffinity: ListTileControlAffinity.leading,
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: isLoading ? null : _createRole,
          child: isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('Create Role'),
        ),
      ],
    );
  }

  void _createRole() async {
    if (form.invalid) {
      form.markAllAsTouched();
      return;
    }

    setState(() {
      isLoading = true;
    });

    try {
      final formValue = form.value;
      final request = CreateRoleRequest(
        name: formValue['name'] as String,
        description: formValue['description'] as String?,
        isSystemRole: false,
        permissions: [], // Permissions will be assigned separately
      );

      widget.onRoleCreated?.call(request);

      if (mounted) {
        Navigator.of(context).pop(request);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Role created successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to create role: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    }
  }

  @override
  void dispose() {
    form.dispose();
    super.dispose();
  }
}

// Show dialog helper function
Future<CreateRoleRequest?> showCreateRoleDialog(
  BuildContext context, {
  Function(CreateRoleRequest)? onRoleCreated,
}) {
  return showDialog<CreateRoleRequest>(
    context: context,
    builder: (context) => CreateRoleDialog(onRoleCreated: onRoleCreated),
  );
}
