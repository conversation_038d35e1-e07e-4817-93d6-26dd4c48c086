import { NextRequest } from 'next/server';
import { requireAuth } from '@/lib/auth';
import { createApiResponse, getRequestBody, handleError, corsHeaders } from '@/lib/utils';
import { prisma } from '@/lib/prisma';
import { validateRequest } from '@/lib/validation';
import Joi from 'joi';

const createScreenSchema = Joi.object({
  name: Joi.string().required(),
  title: Joi.string().required(),
  route: Joi.string().required(),
  description: Joi.string().allow(''),
  icon: Joi.string().allow(''),
  requiredPermissions: Joi.array().items(Joi.string()).default([]),
  allowedRoles: Joi.array().items(Joi.string()).default([]),
  widgets: Joi.array().items(Joi.object()).default([]),
  layout: Joi.object().default({}),
  isActive: Joi.boolean().default(true),
});

const updateScreenSchema = Joi.object({
  name: Joi.string(),
  title: Joi.string(),
  route: Joi.string(),
  description: Joi.string().allow(''),
  icon: Joi.string().allow(''),
  requiredPermissions: Joi.array().items(Joi.string()),
  allowedRoles: Joi.array().items(Joi.string()),
  widgets: Joi.array().items(Joi.object()),
  layout: Joi.object(),
  isActive: Joi.boolean(),
});

async function getScreensHandler(request: NextRequest, context: any, currentUser: any) {
  try {
    // Check if user has admin permissions
    const hasAdminRole = currentUser.roles?.includes('admin');
    if (!hasAdminRole) {
      return Response.json(
        createApiResponse(null, 'Insufficient permissions', 'FORBIDDEN'),
        { status: 403 }
      );
    }

    const url = new URL(request.url);
    const isActive = url.searchParams.get('isActive');

    const whereClause: any = {};
    if (isActive !== null) {
      whereClause.isActive = isActive === 'true';
    }

    // For now, we'll return predefined screens based on the existing app structure
    // In a real implementation, these would come from a database table
    const predefinedScreens = [
      {
        id: 'dashboard',
        name: 'dashboard',
        title: 'Dashboard',
        route: '/dashboard',
        description: 'Main dashboard with overview statistics',
        icon: 'dashboard',
        requiredPermissions: ['view_dashboard'],
        allowedRoles: ['admin', 'property_manager', 'maintenance_staff', 'security_guard', 'househelp'],
        widgets: [
          {
            id: 'property_stats',
            name: 'property_stats',
            type: 'statCard',
            title: 'Property Statistics',
            properties: {
              dataSource: 'properties',
              metrics: ['total', 'active', 'maintenance']
            },
            requiredPermissions: ['view_properties'],
            allowedRoles: ['admin', 'property_manager'],
            position: { x: 0, y: 0, width: 1, height: 1 },
            styling: { color: 'blue' },
            isVisible: true,
            order: 1,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          },
          {
            id: 'maintenance_stats',
            name: 'maintenance_stats',
            type: 'statCard',
            title: 'Maintenance Statistics',
            properties: {
              dataSource: 'maintenance',
              metrics: ['pending', 'in_progress', 'completed']
            },
            requiredPermissions: ['view_maintenance'],
            allowedRoles: ['admin', 'property_manager', 'maintenance_staff'],
            position: { x: 1, y: 0, width: 1, height: 1 },
            styling: { color: 'orange' },
            isVisible: true,
            order: 2,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          }
        ],
        layout: {
          type: 'grid',
          columns: 2,
          spacing: 16
        },
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: currentUser.userId,
      },
      {
        id: 'properties',
        name: 'properties',
        title: 'Properties',
        route: '/properties',
        description: 'Property management and overview',
        icon: 'business',
        requiredPermissions: ['view_properties'],
        allowedRoles: ['admin', 'property_manager'],
        widgets: [
          {
            id: 'property_list',
            name: 'property_list',
            type: 'table',
            title: 'Property List',
            properties: {
              dataSource: 'properties',
              columns: ['name', 'type', 'status', 'location']
            },
            requiredPermissions: ['view_properties'],
            allowedRoles: ['admin', 'property_manager'],
            position: { x: 0, y: 0, width: 2, height: 2 },
            styling: {},
            isVisible: true,
            order: 1,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          }
        ],
        layout: {
          type: 'flex',
          direction: 'column'
        },
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: currentUser.userId,
      },
      {
        id: 'maintenance',
        name: 'maintenance',
        title: 'Maintenance',
        route: '/maintenance',
        description: 'Maintenance request management',
        icon: 'build',
        requiredPermissions: ['view_maintenance'],
        allowedRoles: ['admin', 'property_manager', 'maintenance_staff'],
        widgets: [
          {
            id: 'maintenance_requests',
            name: 'maintenance_requests',
            type: 'table',
            title: 'Maintenance Requests',
            properties: {
              dataSource: 'maintenance_requests',
              columns: ['title', 'priority', 'status', 'assigned_to', 'created_at']
            },
            requiredPermissions: ['view_maintenance'],
            allowedRoles: ['admin', 'property_manager', 'maintenance_staff'],
            position: { x: 0, y: 0, width: 2, height: 2 },
            styling: {},
            isVisible: true,
            order: 1,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          }
        ],
        layout: {
          type: 'flex',
          direction: 'column'
        },
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: currentUser.userId,
      },
      {
        id: 'attendance',
        name: 'attendance',
        title: 'Attendance',
        route: '/attendance',
        description: 'Staff attendance tracking',
        icon: 'access_time',
        requiredPermissions: ['view_attendance'],
        allowedRoles: ['admin', 'property_manager'],
        widgets: [
          {
            id: 'attendance_overview',
            name: 'attendance_overview',
            type: 'chart',
            title: 'Attendance Overview',
            properties: {
              chartType: 'line',
              dataSource: 'attendance',
              xAxis: 'date',
              yAxis: 'count'
            },
            requiredPermissions: ['view_attendance'],
            allowedRoles: ['admin', 'property_manager'],
            position: { x: 0, y: 0, width: 2, height: 1 },
            styling: {},
            isVisible: true,
            order: 1,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          }
        ],
        layout: {
          type: 'grid',
          columns: 2,
          spacing: 16
        },
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: currentUser.userId,
      }
    ];

    // Filter screens based on user permissions
    const filteredScreens = predefinedScreens.filter(screen => {
      // Admin users get access to all screens
      if (currentUser.roles?.includes('admin')) {
        return true;
      }

      // Check if user has allowed roles
      const hasRole = screen.allowedRoles.some(role =>
        currentUser.roles?.includes(role)
      );

      return hasRole;
    });

    return Response.json(
      createApiResponse(filteredScreens),
      {
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to fetch screens');
  }
}

async function createScreenHandler(request: NextRequest, context: any, currentUser: any) {
  try {
    // Check if user has admin permissions
    const hasAdminRole = currentUser.roles?.includes('admin');
    if (!hasAdminRole) {
      return Response.json(
        createApiResponse(null, 'Insufficient permissions', 'FORBIDDEN'),
        { status: 403 }
      );
    }

    const body = await getRequestBody(request);

    // Validate request body
    const validation = validateRequest(createScreenSchema, body);
    if (!validation.isValid) {
      return Response.json(
        createApiResponse(null, 'Validation failed', 'VALIDATION_ERROR'),
        { status: 400 }
      );
    }

    const screenData = validation.data;

    // Create new screen object
    const newScreen = {
      id: `custom_${Date.now()}`,
      ...screenData,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      createdBy: currentUser.userId,
    };

    // In a real implementation, this would be saved to a database
    // For now, we'll just return the created screen

    return Response.json(
      createApiResponse(newScreen),
      {
        status: 201,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to create screen');
  }
}

// GET /admin/screens - Get all screens
export const GET = requireAuth(getScreensHandler);

// POST /admin/screens - Create screen
export const POST = requireAuth(createScreenHandler);

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: corsHeaders(),
  });
}
