const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function fixAdminPermissions() {
  try {
    console.log('🔧 Checking admin user permissions...');

    // Find the admin user
    const adminUser = await prisma.user.findFirst({
      where: {
        email: '<EMAIL>'
      },
      include: {
        roles: {
          include: {
            role: {
              include: {
                permissions: {
                  include: {
                    permission: true
                  }
                }
              }
            }
          }
        },
        permissions: {
          include: {
            permission: true
          }
        }
      }
    });

    if (!adminUser) {
      console.log('❌ Admin user not found!');
      return;
    }

    console.log(`✅ Found admin user: ${adminUser.email}`);
    console.log(`📋 User roles: ${adminUser.roles.map(ur => ur.role.name).join(', ')}`);
    console.log(`🔑 Direct permissions: ${adminUser.permissions.length}`);

    // Get all permissions from roles
    const rolePermissions = adminUser.roles.flatMap(ur => 
      ur.role.permissions.map(rp => rp.permission)
    );
    console.log(`🎭 Role-based permissions: ${rolePermissions.length}`);

    // Get all available permissions
    const allPermissions = await prisma.permission.findMany();
    console.log(`📚 Total available permissions: ${allPermissions.length}`);

    // Check if admin role has all permissions
    const adminRole = await prisma.role.findFirst({
      where: { name: 'admin' },
      include: {
        permissions: {
          include: {
            permission: true
          }
        }
      }
    });

    if (!adminRole) {
      console.log('❌ Admin role not found!');
      return;
    }

    console.log(`🎭 Admin role has ${adminRole.permissions.length} permissions`);

    // If admin role doesn't have all permissions, assign them
    if (adminRole.permissions.length < allPermissions.length) {
      console.log('🔧 Admin role missing permissions, fixing...');

      // Get permissions not assigned to admin role
      const assignedPermissionIds = adminRole.permissions.map(rp => rp.permission.id);
      const missingPermissions = allPermissions.filter(p => !assignedPermissionIds.includes(p.id));

      console.log(`➕ Adding ${missingPermissions.length} missing permissions to admin role`);

      // Add missing permissions to admin role
      for (const permission of missingPermissions) {
        await prisma.rolePermission.create({
          data: {
            roleId: adminRole.id,
            permissionId: permission.id
          }
        });
        console.log(`  ✅ Added permission: ${permission.name}`);
      }
    }

    // Also assign all permissions directly to admin user as backup
    console.log('🔧 Ensuring admin user has direct permissions...');

    const userPermissionIds = adminUser.permissions.map(up => up.permission.id);
    const missingUserPermissions = allPermissions.filter(p => !userPermissionIds.includes(p.id));

    if (missingUserPermissions.length > 0) {
      console.log(`➕ Adding ${missingUserPermissions.length} direct permissions to admin user`);

      for (const permission of missingUserPermissions) {
        await prisma.userPermission.create({
          data: {
            userId: adminUser.id,
            permissionId: permission.id
          }
        });
        console.log(`  ✅ Added direct permission: ${permission.name}`);
      }
    }

    // Verify the fix
    const updatedUser = await prisma.user.findFirst({
      where: { email: '<EMAIL>' },
      include: {
        permissions: {
          include: {
            permission: true
          }
        },
        roles: {
          include: {
            role: {
              include: {
                permissions: {
                  include: {
                    permission: true
                  }
                }
              }
            }
          }
        }
      }
    });

    const totalUserPermissions = updatedUser.permissions.length;
    const totalRolePermissions = updatedUser.roles.flatMap(ur => ur.role.permissions).length;

    console.log('\n🎉 Admin permissions fixed!');
    console.log(`✅ Direct permissions: ${totalUserPermissions}`);
    console.log(`✅ Role-based permissions: ${totalRolePermissions}`);
    console.log(`✅ Total available permissions: ${allPermissions.length}`);

    // List some key dashboard permissions
    const dashboardPermissions = updatedUser.permissions
      .map(up => up.permission.name)
      .filter(name => name.includes('dashboard') || name.includes('view_') || name.includes('properties') || name.includes('maintenance'));

    console.log('\n📊 Key dashboard permissions:');
    dashboardPermissions.slice(0, 10).forEach(perm => {
      console.log(`  ✅ ${perm}`);
    });

  } catch (error) {
    console.error('❌ Error fixing admin permissions:', error);
  } finally {
    await prisma.$disconnect();
  }
}

fixAdminPermissions();
