import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:reactive_forms/reactive_forms.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/utils/app_utils.dart';
import '../providers/auth_providers.dart';
import '../widgets/registration_dialog.dart';

class LoginScreen extends ConsumerStatefulWidget {
  const LoginScreen({super.key});

  @override
  ConsumerState<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends ConsumerState<LoginScreen> {
  late FormGroup form;
  bool _isPasswordVisible = false;
  String _loginType = 'email'; // email, username, mobile

  @override
  void initState() {
    super.initState();
    form = FormGroup({
      'identifier': FormControl<String>(validators: [Validators.required]),
      'password': FormControl<String>(validators: [Validators.required, Validators.minLength(6)]),
    });
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authStateProvider);

    ref.listen<AuthState>(authStateProvider, (previous, next) {
      if (next.error != null) {
        AppUtils.showErrorSnackBar(context, next.error!);
        ref.read(authStateProvider.notifier).clearError();
      }

      if (next.isAuthenticated) {
        context.go('/dashboard');
      }
    });

    return Scaffold(
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: ReactiveForm(
            formGroup: form,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Logo and Title
                const Icon(
                  Icons.business,
                  size: 80,
                  color: Colors.blue,
                ),
                const SizedBox(height: AppConstants.defaultPadding),
                Text(
                  AppConstants.appName,
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: AppConstants.defaultPadding),
                Text(
                  'Property Management System',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: Colors.grey[600],
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: AppConstants.largePadding * 2),

                // Login Type Selector
                Container(
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey[300]!),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: _buildLoginTypeButton('email', 'Email', Icons.email),
                      ),
                      Expanded(
                        child: _buildLoginTypeButton('username', 'Username', Icons.person),
                      ),
                      Expanded(
                        child: _buildLoginTypeButton('mobile', 'Mobile', Icons.phone),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: AppConstants.defaultPadding),

                // Dynamic Login Field
                ReactiveTextField<String>(
                  formControlName: 'identifier',
                  keyboardType: _getKeyboardType(),
                  decoration: InputDecoration(
                    labelText: _getFieldLabel(),
                    hintText: _getFieldHint(),
                    prefixIcon: Icon(_getFieldIcon()),
                    border: const OutlineInputBorder(),
                  ),
                  validationMessages: {
                    ValidationMessage.required: (_) => '${_getFieldLabel()} is required',
                  },
                ),
                const SizedBox(height: AppConstants.defaultPadding),

                // Password Field
                ReactiveTextField<String>(
                  formControlName: 'password',
                  obscureText: !_isPasswordVisible,
                  decoration: InputDecoration(
                    labelText: 'Password',
                    hintText: 'Enter your password',
                    prefixIcon: const Icon(Icons.lock),
                    border: const OutlineInputBorder(),
                    suffixIcon: IconButton(
                      icon: Icon(
                        _isPasswordVisible ? Icons.visibility_off : Icons.visibility,
                      ),
                      onPressed: () {
                        setState(() {
                          _isPasswordVisible = !_isPasswordVisible;
                        });
                      },
                    ),
                  ),
                  validationMessages: {
                    ValidationMessage.required: (_) => 'Password is required',
                    ValidationMessage.minLength: (_) => 'Password must be at least 6 characters',
                  },
                ),
                const SizedBox(height: AppConstants.largePadding),

                // Login Button
                ReactiveFormConsumer(
                  builder: (context, form, child) {
                    return ElevatedButton(
                      onPressed: authState.isLoading || form.invalid ? null : _handleLogin,
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                      child: authState.isLoading
                          ? const SizedBox(
                              height: 20,
                              width: 20,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                          : const Text('Login'),
                    );
                  },
                ),

                const SizedBox(height: AppConstants.defaultPadding),

                // Test Login Button (for debugging)
                OutlinedButton(
                  onPressed: authState.isLoading ? null : _handleTestLogin,
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                  child: const Text('Test Login (<EMAIL>)'),
                ),
                const SizedBox(height: AppConstants.defaultPadding),

                // Divider
                Row(
                  children: [
                    const Expanded(child: Divider()),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Text(
                        'OR',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                    ),
                    const Expanded(child: Divider()),
                  ],
                ),
                const SizedBox(height: AppConstants.defaultPadding),

                // Register Button
                OutlinedButton.icon(
                  onPressed: () => _showRegistrationDialog(),
                  icon: const Icon(Icons.person_add),
                  label: const Text('Create New Account'),
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                ),
                const SizedBox(height: AppConstants.smallPadding),

                // Help Text
                Text(
                  'New users need admin approval after registration',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLoginTypeButton(String type, String label, IconData icon) {
    final isSelected = _loginType == type;
    return InkWell(
      onTap: () {
        setState(() {
          _loginType = type;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12),
        decoration: BoxDecoration(
          color: isSelected ? Theme.of(context).primaryColor : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color: isSelected ? Colors.white : Colors.grey[600],
              size: 20,
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                color: isSelected ? Colors.white : Colors.grey[600],
                fontSize: 12,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }

  TextInputType _getKeyboardType() {
    switch (_loginType) {
      case 'email':
        return TextInputType.emailAddress;
      case 'mobile':
        return TextInputType.phone;
      default:
        return TextInputType.text;
    }
  }

  String _getFieldLabel() {
    switch (_loginType) {
      case 'email':
        return 'Email';
      case 'username':
        return 'Username';
      case 'mobile':
        return 'Mobile Number';
      default:
        return 'Email';
    }
  }

  String _getFieldHint() {
    switch (_loginType) {
      case 'email':
        return 'Enter your email address';
      case 'username':
        return 'Enter your username';
      case 'mobile':
        return 'Enter your mobile number';
      default:
        return 'Enter your email address';
    }
  }

  IconData _getFieldIcon() {
    switch (_loginType) {
      case 'email':
        return Icons.email;
      case 'username':
        return Icons.person;
      case 'mobile':
        return Icons.phone;
      default:
        return Icons.email;
    }
  }

  void _handleLogin() {
    if (form.valid) {
      final identifier = form.control('identifier').value as String;
      final password = form.control('password').value as String;

      ref.read(authStateProvider.notifier).loginWithIdentifier(
        identifier: identifier.trim(),
        password: password,
        loginType: _loginType,
      );
    } else {
      form.markAllAsTouched();
    }
  }

  void _handleTestLogin() {
    print('🔐 Test login button pressed');
    ref.read(authStateProvider.notifier).loginWithIdentifier(
      identifier: '<EMAIL>',
      password: 'admin123',
      loginType: 'email',
    );
  }

  void _showRegistrationDialog() {
    showDialog(
      context: context,
      builder: (context) => const RegistrationDialog(),
    );
  }
}
