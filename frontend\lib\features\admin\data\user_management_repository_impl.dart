import 'package:dio/dio.dart';
import '../../../shared/models/user.dart';
import '../domain/user_management_repository.dart';
import 'user_management_api_service.dart';

class UserManagementRepositoryImpl implements UserManagementRepository {
  final UserManagementApiService _apiService;

  UserManagementRepositoryImpl(this._apiService);

  @override
  Future<List<User>> getUsers({
    int? page,
    int? limit,
    String? role,
    String? status,
    String? search,
  }) async {
    try {
      final response = await _apiService.getUsers(
        page: page,
        limit: limit,
        role: role,
        status: status,
        search: search,
      );

      if (response.success && response.data != null) {
        return response.data!;
      } else {
        throw Exception(response.error ?? 'Failed to fetch users');
      }
    } on DioException catch (e) {
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      throw Exception('Failed to fetch users: $e');
    }
  }

  @override
  Future<User> getUserById(String id) async {
    try {
      final response = await _apiService.getUserById(id);

      if (response.success && response.data != null) {
        return response.data!;
      } else {
        throw Exception(response.error ?? 'Failed to fetch user');
      }
    } on DioException catch (e) {
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      throw Exception('Failed to fetch user: $e');
    }
  }

  @override
  Future<User> createUser(CreateUserRequest request) async {
    try {
      final response = await _apiService.createUser(request);

      if (response.response.statusCode == 201 && response.data != null) {
        // Handle nested response structure from backend
        final responseData = response.data as Map<String, dynamic>;

        // Check if it's a successful API response
        if (responseData['success'] == true && responseData['data'] != null) {
          final data = responseData['data'] as Map<String, dynamic>;

          if (data.containsKey('user')) {
            // Extract user data from nested structure
            final userData = data['user'] as Map<String, dynamic>;
            return User.fromJson(userData);
          } else {
            // Fallback: try to parse the entire data as User
            return User.fromJson(data);
          }
        } else {
          throw Exception(responseData['error'] ?? 'Failed to create user');
        }
      } else {
        throw Exception('Failed to create user: HTTP ${response.response.statusCode}');
      }
    } on DioException catch (e) {
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      throw Exception('Failed to create user: $e');
    }
  }

  @override
  Future<User> updateUser(String id, UpdateUserRequest request) async {
    try {
      final response = await _apiService.updateUser(id, request);

      if (response.success && response.data != null) {
        return response.data!;
      } else {
        throw Exception(response.error ?? 'Failed to update user');
      }
    } on DioException catch (e) {
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      throw Exception('Failed to update user: $e');
    }
  }

  @override
  Future<void> deleteUser(String id) async {
    try {
      final response = await _apiService.deleteUser(id);

      if (!response.success) {
        throw Exception(response.error ?? 'Failed to delete user');
      }
    } on DioException catch (e) {
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      throw Exception('Failed to delete user: $e');
    }
  }

  @override
  Future<User> activateUser(String id) async {
    try {
      final response = await _apiService.activateUser(id);

      if (response.success && response.data != null) {
        return response.data!;
      } else {
        throw Exception(response.error ?? 'Failed to activate user');
      }
    } on DioException catch (e) {
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      throw Exception('Failed to activate user: $e');
    }
  }

  @override
  Future<User> deactivateUser(String id) async {
    try {
      final response = await _apiService.deactivateUser(id);

      if (response.success && response.data != null) {
        return response.data!;
      } else {
        throw Exception(response.error ?? 'Failed to deactivate user');
      }
    } on DioException catch (e) {
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      throw Exception('Failed to deactivate user: $e');
    }
  }

  @override
  Future<User> approveUser(String id) async {
    try {
      final response = await _apiService.approveUser(id);

      if (response.success && response.data != null) {
        return response.data!;
      } else {
        throw Exception(response.error ?? 'Failed to approve user');
      }
    } on DioException catch (e) {
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      throw Exception('Failed to approve user: $e');
    }
  }

  @override
  Future<User> rejectUser(String id) async {
    try {
      final response = await _apiService.rejectUser(id);

      if (response.success && response.data != null) {
        return response.data!;
      } else {
        throw Exception(response.error ?? 'Failed to reject user');
      }
    } on DioException catch (e) {
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      throw Exception('Failed to reject user: $e');
    }
  }

  @override
  Future<User> assignRoles(String id, AssignRolesRequest request) async {
    try {
      final response = await _apiService.assignRoles(id, request);

      if (response.success && response.data != null) {
        return response.data!;
      } else {
        throw Exception(response.error ?? 'Failed to assign roles');
      }
    } on DioException catch (e) {
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      throw Exception('Failed to assign roles: $e');
    }
  }

  @override
  Future<User> removeRole(String id, String roleId) async {
    try {
      final response = await _apiService.removeRole(id, roleId);

      if (response.success && response.data != null) {
        return response.data!;
      } else {
        throw Exception(response.error ?? 'Failed to remove role');
      }
    } on DioException catch (e) {
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      throw Exception('Failed to remove role: $e');
    }
  }

  @override
  Future<List<User>> getPendingUsers() async {
    try {
      final response = await _apiService.getPendingUsers();

      if (response.success && response.data != null) {
        return response.data!;
      } else {
        throw Exception(response.error ?? 'Failed to fetch pending users');
      }
    } on DioException catch (e) {
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      throw Exception('Failed to fetch pending users: $e');
    }
  }

  @override
  Future<List<User>> bulkApproveUsers(BulkApproveRequest request) async {
    try {
      final response = await _apiService.bulkApproveUsers(request);

      if (response.success && response.data != null) {
        return response.data!;
      } else {
        throw Exception(response.error ?? 'Failed to approve users');
      }
    } on DioException catch (e) {
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      throw Exception('Failed to approve users: $e');
    }
  }

  @override
  Future<List<User>> getAllUsers() async {
    try {
      // Get all users without pagination
      return await getUsers();
    } catch (e) {
      throw Exception('Failed to get all users: $e');
    }
  }

  @override
  Future<User> updateUserRole(String userId, String roleId) async {
    try {
      final request = UpdateUserRequest(primaryRole: roleId);
      return await updateUser(userId, request);
    } catch (e) {
      throw Exception('Failed to update user role: $e');
    }
  }
}
