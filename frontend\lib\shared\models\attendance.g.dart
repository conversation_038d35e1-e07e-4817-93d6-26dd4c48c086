// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'attendance.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AttendanceRecord _$AttendanceRecordFromJson(Map<String, dynamic> json) =>
    AttendanceRecord(
      id: json['id'] as String,
      propertyId: json['property_id'] as String?,
      officeId: json['office_id'] as String?,
      userId: json['user_id'] as String,
      workerName: json['worker_name'] as String,
      workerRole: json['worker_role'] as String?,
      date: DateTime.parse(json['date'] as String),
      checkInTime: json['check_in_time'] as String?,
      checkOutTime: json['check_out_time'] as String?,
      hoursWorked: (json['hours_worked'] as num?)?.toDouble(),
      notes: json['notes'] as String?,
      status: json['status'] as String,
      recordedBy: json['recorded_by'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
    );

Map<String, dynamic> _$AttendanceRecordToJson(AttendanceRecord instance) =>
    <String, dynamic>{
      'id': instance.id,
      'property_id': instance.propertyId,
      'office_id': instance.officeId,
      'user_id': instance.userId,
      'worker_name': instance.workerName,
      'worker_role': instance.workerRole,
      'date': instance.date.toIso8601String(),
      'check_in_time': instance.checkInTime,
      'check_out_time': instance.checkOutTime,
      'hours_worked': instance.hoursWorked,
      'notes': instance.notes,
      'status': instance.status,
      'recorded_by': instance.recordedBy,
      'created_at': instance.createdAt.toIso8601String(),
    };

AttendanceStats _$AttendanceStatsFromJson(Map<String, dynamic> json) =>
    AttendanceStats(
      totalCount: (json['totalCount'] as num).toInt(),
      presentCount: (json['presentCount'] as num).toInt(),
      absentCount: (json['absentCount'] as num).toInt(),
      lateCount: (json['lateCount'] as num).toInt(),
      halfDayCount: (json['halfDayCount'] as num).toInt(),
      attendanceRate: (json['attendanceRate'] as num).toDouble(),
      averageHours: (json['averageHours'] as num).toDouble(),
      date: DateTime.parse(json['date'] as String),
    );

Map<String, dynamic> _$AttendanceStatsToJson(AttendanceStats instance) =>
    <String, dynamic>{
      'totalCount': instance.totalCount,
      'presentCount': instance.presentCount,
      'absentCount': instance.absentCount,
      'lateCount': instance.lateCount,
      'halfDayCount': instance.halfDayCount,
      'attendanceRate': instance.attendanceRate,
      'averageHours': instance.averageHours,
      'date': instance.date.toIso8601String(),
    };
