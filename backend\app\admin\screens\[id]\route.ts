import { NextRequest } from 'next/server';
import { requireAuth } from '@/lib/auth';
import { createApiResponse, getRequestBody, handleError, corsHeaders } from '@/lib/utils';
import { validateRequest } from '@/lib/validation';
import Joi from 'joi';

const updateScreenSchema = Joi.object({
  name: Joi.string(),
  title: Joi.string(),
  route: Joi.string(),
  description: Joi.string().allow(''),
  icon: Joi.string().allow(''),
  requiredPermissions: Joi.array().items(Joi.string()),
  allowedRoles: Joi.array().items(Joi.string()),
  widgets: Joi.array().items(Joi.object()),
  layout: Joi.object(),
  isActive: Joi.boolean(),
});

async function getScreenHandler(
  request: NextRequest,
  context: { params: { id: string } },
  currentUser: any
) {
  try {
    // Check if user has admin permissions
    const hasAdminRole = currentUser.roles?.includes('admin');
    if (!hasAdminRole) {
      return Response.json(
        createApiResponse(null, 'Insufficient permissions', 'FORBIDDEN'),
        { status: 403 }
      );
    }

    const { id } = context.params;

    // For now, return a mock screen based on the ID
    // In a real implementation, this would fetch from database
    const mockScreen = {
      id,
      name: id,
      title: id.charAt(0).toUpperCase() + id.slice(1),
      route: `/${id}`,
      description: `${id} screen description`,
      icon: 'dashboard',
      requiredPermissions: ['view_dashboard'],
      allowedRoles: ['admin'],
      widgets: [],
      layout: { type: 'grid', columns: 2 },
      isActive: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      createdBy: currentUser.userId,
    };

    return Response.json(
      createApiResponse(mockScreen),
      {
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to fetch screen');
  }
}

async function updateScreenHandler(
  request: NextRequest,
  context: { params: { id: string } },
  currentUser: any
) {
  try {
    // Check if user has admin permissions
    const hasAdminRole = currentUser.roles?.includes('admin');
    if (!hasAdminRole) {
      return Response.json(
        createApiResponse(null, 'Insufficient permissions', 'FORBIDDEN'),
        { status: 403 }
      );
    }

    const { id } = context.params;
    const body = await getRequestBody(request);
    
    // Validate request body
    const validation = validateRequest(updateScreenSchema, body);
    if (!validation.isValid) {
      return Response.json(
        createApiResponse(null, 'Validation failed', 'VALIDATION_ERROR'),
        { status: 400 }
      );
    }

    // In a real implementation, this would update the database record
    const updatedScreen = {
      id,
      ...validation.data,
      updatedAt: new Date().toISOString(),
    };

    return Response.json(
      createApiResponse(updatedScreen),
      {
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to update screen');
  }
}

async function deleteScreenHandler(
  request: NextRequest,
  context: { params: { id: string } },
  currentUser: any
) {
  try {
    // Check if user has admin permissions
    const hasAdminRole = currentUser.roles?.includes('admin');
    if (!hasAdminRole) {
      return Response.json(
        createApiResponse(null, 'Insufficient permissions', 'FORBIDDEN'),
        { status: 403 }
      );
    }

    const { id } = context.params;

    // In a real implementation, this would delete from database
    // For now, just return success

    return Response.json(
      createApiResponse({ message: 'Screen deleted successfully' }),
      {
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to delete screen');
  }
}

// GET /admin/screens/[id] - Get specific screen
export const GET = requireAuth(getScreenHandler);

// PUT /admin/screens/[id] - Update screen
export const PUT = requireAuth(updateScreenHandler);

// DELETE /admin/screens/[id] - Delete screen
export const DELETE = requireAuth(deleteScreenHandler);

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: corsHeaders(),
  });
}
