import { NextRequest } from 'next/server';
import { requireAuth } from '@/lib/auth';
import { createApiResponse, getRequestBody, handleError, corsHeaders } from '@/lib/utils';
import { prisma } from '@/lib/prisma';
import { validateRequest } from '@/lib/validation';
import Joi from 'joi';

const createWidgetSchema = Joi.object({
  name: Joi.string().required(),
  type: Joi.string().required(),
  screen_name: Joi.string().required(),
  config: Joi.object().default({}),
  position: Joi.object({
    x: Joi.number().default(0),
    y: Joi.number().default(0),
    width: Joi.number().default(1),
    height: Joi.number().default(1),
  }).default({}),
  is_visible: Joi.boolean().default(true),
  is_enabled: Joi.boolean().default(true),
  required_permissions: Joi.array().items(Joi.string()).default([]),
  allowed_roles: Joi.array().items(Joi.string()).default([]),
});

const updateWidgetSchema = Joi.object({
  name: Joi.string(),
  type: Joi.string(),
  screen_name: Joi.string(),
  config: Joi.object(),
  position: Joi.object({
    x: Joi.number(),
    y: Joi.number(),
    width: Joi.number(),
    height: Joi.number(),
  }),
  is_visible: Joi.boolean(),
  is_enabled: Joi.boolean(),
  required_permissions: Joi.array().items(Joi.string()),
  allowed_roles: Joi.array().items(Joi.string()),
});

async function getWidgetsHandler(request: NextRequest, context: any, currentUser: any) {
  try {
    // Check if user has admin permissions
    const hasAdminRole = currentUser.roles?.includes('admin');
    if (!hasAdminRole) {
      return Response.json(
        createApiResponse(null, 'Insufficient permissions', 'FORBIDDEN'),
        { status: 403 }
      );
    }

    const url = new URL(request.url);
    const screenName = url.searchParams.get('screen_name');

    const whereClause = screenName ? { screenName } : {};

    const widgets = await prisma.widgetPermission.findMany({
      where: whereClause,
      orderBy: { createdAt: 'desc' },
    });

    // Transform to match frontend expectations
    const transformedWidgets = widgets.map(widget => ({
      id: widget.id,
      name: widget.widgetName,
      type: widget.widgetName.includes('chart') ? 'chart' :
            widget.widgetName.includes('stat') ? 'statCard' :
            widget.widgetName.includes('table') ? 'table' : 'custom',
      screen_name: widget.screenName,
      config: {},
      position: { x: 0, y: 0, width: 1, height: 1 },
      is_visible: widget.isVisible,
      is_enabled: widget.isEnabled,
      required_permissions: widget.requiredPermissions || [],
      allowed_roles: widget.allowedRoles || [],
      created_at: widget.createdAt,
      updated_at: widget.updatedAt,
    }));

    return Response.json(
      createApiResponse(transformedWidgets),
      {
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to fetch widgets');
  }
}

async function createWidgetHandler(request: NextRequest, context: any, currentUser: any) {
  try {
    // Check if user has admin permissions
    const hasAdminRole = currentUser.roles?.includes('admin');
    if (!hasAdminRole) {
      return Response.json(
        createApiResponse(null, 'Insufficient permissions', 'FORBIDDEN'),
        { status: 403 }
      );
    }

    const body = await getRequestBody(request);

    // Validate request body
    const validation = validateRequest(createWidgetSchema, body);
    if (!validation.isValid) {
      return Response.json(
        createApiResponse(null, 'Validation failed', 'VALIDATION_ERROR'),
        { status: 400 }
      );
    }

    const {
      name,
      type,
      screen_name,
      config,
      position,
      is_visible,
      is_enabled,
      required_permissions,
      allowed_roles
    } = validation.data;

    // Create widget permission entry
    const widget = await prisma.widgetPermission.create({
      data: {
        screenName: screen_name,
        widgetName: name,
        requiredPermissions: required_permissions,
        allowedRoles: allowed_roles,
        isVisible: is_visible,
        isEnabled: is_enabled,
      },
    });

    // Transform response to match frontend expectations
    const transformedWidget = {
      id: widget.id,
      name: widget.widgetName,
      type,
      screen_name: widget.screenName,
      config,
      position,
      is_visible: widget.isVisible,
      is_enabled: widget.isEnabled,
      required_permissions: widget.requiredPermissions || [],
      allowed_roles: widget.allowedRoles || [],
      created_at: widget.createdAt,
      updated_at: widget.updatedAt,
    };

    return Response.json(
      createApiResponse(transformedWidget),
      {
        status: 201,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to create widget');
  }
}

// GET /admin/widgets - Get all widgets
export const GET = requireAuth(getWidgetsHandler);

// POST /admin/widgets - Create widget
export const POST = requireAuth(createWidgetHandler);

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: corsHeaders(),
  });
}
