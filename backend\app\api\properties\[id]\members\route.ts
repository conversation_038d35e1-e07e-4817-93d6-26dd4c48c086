import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth, requireRole } from '@/lib/auth';
import { createApiResponse, getRequestBody, handleError, corsHeaders } from '@/lib/utils';
import { validateRequest } from '@/lib/validation';
import Joi from 'joi';

const createPropertyMemberSchema = Joi.object({
  user_id: Joi.string().uuid().required(),
  role: Joi.string().required(),
  position: Joi.string().optional(),
  department: Joi.string().optional(),
  hourly_rate: Joi.number().positive().optional(),
  start_date: Joi.date().optional(),
  end_date: Joi.date().optional(),
});

async function getPropertyMembersHandler(
  request: NextRequest,
  context: { params: { id: string } },
  currentUser: any
) {
  try {
    const { id } = context.params;

    // Verify property exists
    const property = await prisma.property.findUnique({
      where: { id },
      select: { id: true, name: true, type: true },
    });

    if (!property) {
      return Response.json(
        createApiResponse(null, 'Property not found', 'NOT_FOUND'),
        { status: 404 }
      );
    }

    // Get property members
    const members = await prisma.propertyMember.findMany({
      where: {
        propertyId: id,
        isActive: true,
      },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            fullName: true,
            phone: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    // Transform data to match API response format
    const transformedMembers = members.map(member => ({
      id: member.id,
      property_id: member.propertyId,
      user_id: member.userId,
      role: member.role,
      position: member.position,
      department: member.department,
      hourly_rate: member.hourlyRate,
      start_date: member.startDate,
      end_date: member.endDate,
      is_active: member.isActive,
      created_at: member.createdAt,
      user: {
        id: member.user.id,
        email: member.user.email,
        full_name: member.user.fullName,
        phone: member.user.phone,
      },
    }));

    return Response.json(
      createApiResponse({
        members: transformedMembers,
        property: {
          id: property.id,
          name: property.name,
          type: property.type.toLowerCase(),
        },
      }),
      { 
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to fetch property members');
  }
}

async function createPropertyMemberHandler(
  request: NextRequest,
  context: { params: { id: string } },
  currentUser: any
) {
  try {
    const { id } = context.params;
    const body = await getRequestBody(request);
    
    // Validate request body
    const validation = validateRequest(createPropertyMemberSchema, body);
    if (!validation.isValid) {
      return Response.json(
        createApiResponse(null, 'Validation failed', 'VALIDATION_ERROR'),
        { status: 400 }
      );
    }

    // Verify property exists
    const property = await prisma.property.findUnique({
      where: { id },
    });

    if (!property) {
      return Response.json(
        createApiResponse(null, 'Property not found', 'NOT_FOUND'),
        { status: 404 }
      );
    }

    // Verify user exists
    const user = await prisma.user.findUnique({
      where: { id: validation.data.user_id },
    });

    if (!user) {
      return Response.json(
        createApiResponse(null, 'User not found', 'USER_NOT_FOUND'),
        { status: 404 }
      );
    }

    // Check if user is already a member of this property
    const existingMember = await prisma.propertyMember.findUnique({
      where: {
        propertyId_userId: {
          propertyId: id,
          userId: validation.data.user_id,
        },
      },
    });

    if (existingMember) {
      return Response.json(
        createApiResponse(null, 'User is already a member of this property', 'DUPLICATE_MEMBER'),
        { status: 409 }
      );
    }

    const { 
      user_id, 
      role, 
      position, 
      department, 
      hourly_rate, 
      start_date, 
      end_date 
    } = validation.data;

    // Create property member
    const member = await prisma.propertyMember.create({
      data: {
        propertyId: id,
        userId: user_id,
        role,
        position,
        department,
        hourlyRate: hourly_rate,
        startDate: start_date ? new Date(start_date) : null,
        endDate: end_date ? new Date(end_date) : null,
        isActive: true,
      },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            fullName: true,
            phone: true,
          },
        },
      },
    });

    return Response.json(
      createApiResponse({
        message: 'Property member added successfully',
        member: {
          id: member.id,
          property_id: member.propertyId,
          user_id: member.userId,
          role: member.role,
          position: member.position,
          department: member.department,
          hourly_rate: member.hourlyRate,
          start_date: member.startDate,
          end_date: member.endDate,
          is_active: member.isActive,
          created_at: member.createdAt,
          user: {
            id: member.user.id,
            email: member.user.email,
            full_name: member.user.fullName,
            phone: member.user.phone,
          },
        },
      }),
      { 
        status: 201,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to create property member');
  }
}

export const GET = requireAuth(getPropertyMembersHandler);
export const POST = requireRole(['admin', 'property_manager'])(createPropertyMemberHandler);

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: corsHeaders(),
  });
}
