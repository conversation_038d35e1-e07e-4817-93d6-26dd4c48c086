import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/network/api_client.dart';
import '../../data/screen_management_api_service.dart';
import '../../data/models/custom_screen.dart';

// API Service Provider
final screenManagementApiServiceProvider = Provider<ScreenManagementApiService>((ref) {
  final apiClient = ref.read(apiClientProvider);
  return ScreenManagementApiService(apiClient);
});

// Custom Screens Provider
final customScreensProvider = FutureProvider<List<CustomScreen>>((ref) async {
  final apiService = ref.read(screenManagementApiServiceProvider);
  final result = await apiService.getScreens();
  
  return result.when(
    success: (screens) => screens,
    error: (error) => throw Exception(error),
  );
});

// Custom Widgets Provider
final customWidgetsProvider = FutureProvider<List<CustomWidget>>((ref) async {
  final apiService = ref.read(screenManagementApiServiceProvider);
  final result = await apiService.getWidgets();
  
  return result.when(
    success: (widgets) => widgets,
    error: (error) => throw Exception(error),
  );
});

// Widget Types Provider
final widgetTypesProvider = FutureProvider<List<Map<String, dynamic>>>((ref) async {
  final apiService = ref.read(screenManagementApiServiceProvider);
  final result = await apiService.getWidgetTypes();
  
  return result.when(
    success: (types) => types,
    error: (error) => throw Exception(error),
  );
});

// Screen Management State Notifier
class ScreenManagementNotifier extends StateNotifier<AsyncValue<List<CustomScreen>>> {
  final ScreenManagementApiService _apiService;

  ScreenManagementNotifier(this._apiService) : super(const AsyncValue.loading()) {
    loadScreens();
  }

  Future<void> loadScreens() async {
    state = const AsyncValue.loading();
    
    final result = await _apiService.getScreens();
    
    state = result.when(
      success: (screens) => AsyncValue.data(screens),
      error: (error) => AsyncValue.error(error, StackTrace.current),
    );
  }

  Future<bool> createScreen(Map<String, dynamic> screenData) async {
    final result = await _apiService.createScreen(screenData);
    
    return result.when(
      success: (screen) {
        // Refresh the list
        loadScreens();
        return true;
      },
      error: (error) {
        state = AsyncValue.error(error, StackTrace.current);
        return false;
      },
    );
  }

  Future<bool> updateScreen(String id, Map<String, dynamic> screenData) async {
    final result = await _apiService.updateScreen(id, screenData);
    
    return result.when(
      success: (screen) {
        // Refresh the list
        loadScreens();
        return true;
      },
      error: (error) {
        state = AsyncValue.error(error, StackTrace.current);
        return false;
      },
    );
  }

  Future<bool> deleteScreen(String id) async {
    final result = await _apiService.deleteScreen(id);
    
    return result.when(
      success: (_) {
        // Refresh the list
        loadScreens();
        return true;
      },
      error: (error) {
        state = AsyncValue.error(error, StackTrace.current);
        return false;
      },
    );
  }

  Future<bool> validateRoute(String route) async {
    final result = await _apiService.validateRoute(route);
    
    return result.when(
      success: (isValid) => isValid,
      error: (_) => false,
    );
  }
}

// Widget Management State Notifier
class WidgetManagementNotifier extends StateNotifier<AsyncValue<List<CustomWidget>>> {
  final ScreenManagementApiService _apiService;

  WidgetManagementNotifier(this._apiService) : super(const AsyncValue.loading()) {
    loadWidgets();
  }

  Future<void> loadWidgets() async {
    state = const AsyncValue.loading();
    
    final result = await _apiService.getWidgets();
    
    state = result.when(
      success: (widgets) => AsyncValue.data(widgets),
      error: (error) => AsyncValue.error(error, StackTrace.current),
    );
  }

  Future<bool> createWidget(Map<String, dynamic> widgetData) async {
    final result = await _apiService.createWidget(widgetData);
    
    return result.when(
      success: (widget) {
        // Refresh the list
        loadWidgets();
        return true;
      },
      error: (error) {
        state = AsyncValue.error(error, StackTrace.current);
        return false;
      },
    );
  }

  Future<bool> updateWidget(String id, Map<String, dynamic> widgetData) async {
    final result = await _apiService.updateWidget(id, widgetData);
    
    return result.when(
      success: (widget) {
        // Refresh the list
        loadWidgets();
        return true;
      },
      error: (error) {
        state = AsyncValue.error(error, StackTrace.current);
        return false;
      },
    );
  }

  Future<bool> deleteWidget(String id) async {
    final result = await _apiService.deleteWidget(id);
    
    return result.when(
      success: (_) {
        // Refresh the list
        loadWidgets();
        return true;
      },
      error: (error) {
        state = AsyncValue.error(error, StackTrace.current);
        return false;
      },
    );
  }
}

// State Notifier Providers
final screenManagementProvider = StateNotifierProvider<ScreenManagementNotifier, AsyncValue<List<CustomScreen>>>((ref) {
  final apiService = ref.read(screenManagementApiServiceProvider);
  return ScreenManagementNotifier(apiService);
});

final widgetManagementProvider = StateNotifierProvider<WidgetManagementNotifier, AsyncValue<List<CustomWidget>>>((ref) {
  final apiService = ref.read(screenManagementApiServiceProvider);
  return WidgetManagementNotifier(apiService);
});

// Individual Screen Provider
final screenProvider = FutureProvider.family<CustomScreen, String>((ref, id) async {
  final apiService = ref.read(screenManagementApiServiceProvider);
  final result = await apiService.getScreen(id);
  
  return result.when(
    success: (screen) => screen,
    error: (error) => throw Exception(error),
  );
});

// Filtered Screens Provider
final filteredScreensProvider = Provider.family<AsyncValue<List<CustomScreen>>, String>((ref, filter) {
  final screensAsync = ref.watch(customScreensProvider);
  
  return screensAsync.when(
    data: (screens) {
      if (filter.isEmpty) {
        return AsyncValue.data(screens);
      }
      
      final filtered = screens.where((screen) =>
        screen.name.toLowerCase().contains(filter.toLowerCase()) ||
        screen.title.toLowerCase().contains(filter.toLowerCase()) ||
        screen.route.toLowerCase().contains(filter.toLowerCase())
      ).toList();
      
      return AsyncValue.data(filtered);
    },
    loading: () => const AsyncValue.loading(),
    error: (error, stack) => AsyncValue.error(error, stack),
  );
});

// Filtered Widgets Provider
final filteredWidgetsProvider = Provider.family<AsyncValue<List<CustomWidget>>, String>((ref, filter) {
  final widgetsAsync = ref.watch(customWidgetsProvider);
  
  return widgetsAsync.when(
    data: (widgets) {
      if (filter.isEmpty) {
        return AsyncValue.data(widgets);
      }
      
      final filtered = widgets.where((widget) =>
        widget.name.toLowerCase().contains(filter.toLowerCase()) ||
        widget.title.toLowerCase().contains(filter.toLowerCase()) ||
        widget.type.toLowerCase().contains(filter.toLowerCase())
      ).toList();
      
      return AsyncValue.data(filtered);
    },
    loading: () => const AsyncValue.loading(),
    error: (error, stack) => AsyncValue.error(error, stack),
  );
});
