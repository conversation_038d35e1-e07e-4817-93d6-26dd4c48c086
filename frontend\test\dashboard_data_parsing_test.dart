import 'package:flutter_test/flutter_test.dart';
import 'package:srsr_property_management/features/dashboard/domain/dashboard_models.dart';

void main() {
  group('Dashboard Data Parsing Tests', () {
    test('should parse dashboard status from backend response', () {
      // This is the actual JSON structure from the backend logs
      final jsonResponse = {
        "success": true,
        "data": {
          "properties": {
            "total": 12,
            "operational": 9,
            "warning": 1,
            "critical": 1
          },
          "maintenance_issues": {
            "total": 5,
            "open": 4,
            "in_progress": 1,
            "critical": 1
          },
          "recent_alerts": [
            {
              "id": "41aa47b3-165b-40a0-9f3d-2bc6e12321b0",
              "type": "payment",
              "severity": "medium",
              "message": "OTT service payment due: YouTube Premium",
              "property_name": "Guest House",
              "timestamp": "2025-05-31T13:28:16.020Z"
            },
            {
              "id": "7c540742-cc70-42a7-abc2-124588a30c37",
              "type": "payment",
              "severity": "medium",
              "message": "OTT service payment due: Disney+ Hotstar",
              "property_name": "Main House",
              "timestamp": "2025-05-31T13:28:16.019Z"
            },
            {
              "id": "730222db-3168-4c14-bfec-7be3d10e802c",
              "type": "maintenance",
              "severity": "high",
              "message": "high maintenance issue: Generator Fuel Low",
              "property_name": "Guest House",
              "timestamp": "2025-05-31T13:28:15.985Z"
            }
          ]
        }
      };

      // This should not throw an exception
      expect(() => DashboardStatus.fromJson(jsonResponse), returnsNormally);

      final dashboardStatus = DashboardStatus.fromJson(jsonResponse);
      
      // Verify basic structure
      expect(dashboardStatus.success, true);
      expect(dashboardStatus.data, isNotNull);

      // Verify property stats
      expect(dashboardStatus.data.properties.total, 12);
      expect(dashboardStatus.data.properties.operational, 9);
      expect(dashboardStatus.data.properties.warning, 1);
      expect(dashboardStatus.data.properties.critical, 1);

      // Verify maintenance stats
      expect(dashboardStatus.data.maintenanceIssues.total, 5);
      expect(dashboardStatus.data.maintenanceIssues.open, 4);
      expect(dashboardStatus.data.maintenanceIssues.inProgress, 1);
      expect(dashboardStatus.data.maintenanceIssues.critical, 1);

      // Verify recent alerts
      expect(dashboardStatus.data.recentAlerts.length, 3);
      
      final firstAlert = dashboardStatus.data.recentAlerts[0];
      expect(firstAlert.id, "41aa47b3-165b-40a0-9f3d-2bc6e12321b0");
      expect(firstAlert.type, "payment");
      expect(firstAlert.severity, "medium");
      expect(firstAlert.message, "OTT service payment due: YouTube Premium");
      expect(firstAlert.propertyName, "Guest House");
      expect(firstAlert.timestamp, DateTime.parse("2025-05-31T13:28:16.020Z"));

      final maintenanceAlert = dashboardStatus.data.recentAlerts[2];
      expect(maintenanceAlert.type, "maintenance");
      expect(maintenanceAlert.severity, "high");
      expect(maintenanceAlert.message, "high maintenance issue: Generator Fuel Low");
      expect(maintenanceAlert.propertyName, "Guest House");
    });

    test('should handle empty alerts list', () {
      final jsonResponse = {
        "success": true,
        "data": {
          "properties": {
            "total": 0,
            "operational": 0,
            "warning": 0,
            "critical": 0
          },
          "maintenance_issues": {
            "total": 0,
            "open": 0,
            "in_progress": 0,
            "critical": 0
          },
          "recent_alerts": []
        }
      };

      final dashboardStatus = DashboardStatus.fromJson(jsonResponse);
      expect(dashboardStatus.data.recentAlerts, isEmpty);
    });

    test('should handle various alert types and severities', () {
      final jsonResponse = {
        "success": true,
        "data": {
          "properties": {"total": 1, "operational": 1, "warning": 0, "critical": 0},
          "maintenance_issues": {"total": 0, "open": 0, "in_progress": 0, "critical": 0},
          "recent_alerts": [
            {
              "id": "1",
              "type": "fuel",
              "severity": "critical",
              "message": "Fuel level critical",
              "property_name": "Site A",
              "timestamp": "2025-05-31T13:28:16.020Z"
            },
            {
              "id": "2",
              "type": "security",
              "severity": "low",
              "message": "Security check completed",
              "property_name": "Site B",
              "timestamp": "2025-05-31T13:28:16.020Z"
            }
          ]
        }
      };

      final dashboardStatus = DashboardStatus.fromJson(jsonResponse);
      expect(dashboardStatus.data.recentAlerts.length, 2);
      
      expect(dashboardStatus.data.recentAlerts[0].type, "fuel");
      expect(dashboardStatus.data.recentAlerts[0].severity, "critical");
      
      expect(dashboardStatus.data.recentAlerts[1].type, "security");
      expect(dashboardStatus.data.recentAlerts[1].severity, "low");
    });
  });
}
