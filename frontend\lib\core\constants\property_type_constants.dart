enum PropertyType {
  residential,
  office,
  constructionSite,
}

class PropertyTypeConstants {
  // Property Type Definitions
  static const Map<PropertyType, String> typeNames = {
    PropertyType.residential: 'Residential',
    PropertyType.office: 'Office',
    PropertyType.constructionSite: 'Construction Site',
  };

  static const Map<PropertyType, String> typeDescriptions = {
    PropertyType.residential: 'Residential properties including homes, apartments, and housing complexes',
    PropertyType.office: 'Office properties including corporate buildings, co-working spaces, and business centers',
    PropertyType.constructionSite: 'Construction sites and development projects with project management features',
  };

  // Property Type Specific Services
  static const Map<PropertyType, List<String>> defaultServices = {
    PropertyType.residential: [
      'electricity',
      'water',
      'internet',
      'security',
      'ott',
      'generator',
      'maintenance',
    ],
    PropertyType.office: [
      'electricity',
      'water',
      'internet',
      'security',
      'ott',
      'generator',
      'maintenance',
      'hvac',
      'fire_safety',
      'access_control',
    ],
    PropertyType.constructionSite: [
      'electricity',
      'water',
      'security',
      'generator',
      'maintenance',
      'safety_equipment',
      'material_storage',
      'equipment_monitoring',
    ],
  };

  // Property Type Specific Features
  static const Map<PropertyType, List<String>> availableFeatures = {
    PropertyType.residential: [
      'fuel_monitoring',
      'security_logs',
      'maintenance_tracking',
      'ott_services',
      'uptime_reports',
      'diesel_additions',
    ],
    PropertyType.office: [
      'fuel_monitoring',
      'security_logs',
      'maintenance_tracking',
      'ott_services',
      'uptime_reports',
      'diesel_additions',
      'office_management',
      'attendance_tracking',
      'meeting_rooms',
      'visitor_management',
    ],
    PropertyType.constructionSite: [
      'fuel_monitoring',
      'security_logs',
      'maintenance_tracking',
      'diesel_additions',
      'project_management',
      'attendance_tracking',
      'equipment_tracking',
      'safety_monitoring',
      'material_management',
      'progress_reporting',
    ],
  };

  // Service Priority by Property Type
  static const Map<PropertyType, Map<String, int>> servicePriority = {
    PropertyType.residential: {
      'electricity': 1,
      'water': 2,
      'security': 3,
      'internet': 4,
      'ott': 5,
      'generator': 6,
      'maintenance': 7,
    },
    PropertyType.office: {
      'electricity': 1,
      'internet': 2,
      'security': 3,
      'hvac': 4,
      'water': 5,
      'access_control': 6,
      'fire_safety': 7,
      'generator': 8,
      'ott': 9,
      'maintenance': 10,
    },
    PropertyType.constructionSite: {
      'safety_equipment': 1,
      'security': 2,
      'electricity': 3,
      'generator': 4,
      'water': 5,
      'equipment_monitoring': 6,
      'material_storage': 7,
      'maintenance': 8,
    },
  };

  // Maintenance Categories by Property Type
  static const Map<PropertyType, List<String>> maintenanceCategories = {
    PropertyType.residential: [
      'electrical',
      'plumbing',
      'security',
      'internet',
      'generator',
      'general',
    ],
    PropertyType.office: [
      'electrical',
      'plumbing',
      'hvac',
      'security',
      'internet',
      'fire_safety',
      'access_control',
      'generator',
      'office_equipment',
      'general',
    ],
    PropertyType.constructionSite: [
      'electrical',
      'plumbing',
      'security',
      'generator',
      'safety_equipment',
      'construction_equipment',
      'material_handling',
      'site_infrastructure',
      'general',
    ],
  };

  // Default Thresholds by Property Type
  static const Map<PropertyType, Map<String, dynamic>> defaultThresholds = {
    PropertyType.residential: {
      'fuel_level_critical': 20.0, // percentage
      'fuel_level_warning': 40.0,
      'power_backup_critical': 6.0, // hours
      'power_backup_warning': 12.0,
      'maintenance_overdue_days': 7,
      'security_check_interval': 24, // hours
    },
    PropertyType.office: {
      'fuel_level_critical': 25.0, // percentage
      'fuel_level_warning': 50.0,
      'power_backup_critical': 8.0, // hours
      'power_backup_warning': 16.0,
      'maintenance_overdue_days': 3,
      'security_check_interval': 12, // hours
      'hvac_temperature_min': 20.0, // celsius
      'hvac_temperature_max': 26.0,
      'office_capacity_warning': 80.0, // percentage
    },
    PropertyType.constructionSite: {
      'fuel_level_critical': 15.0, // percentage
      'fuel_level_warning': 30.0,
      'power_backup_critical': 4.0, // hours
      'power_backup_warning': 8.0,
      'maintenance_overdue_days': 2,
      'security_check_interval': 8, // hours
      'safety_inspection_interval': 24, // hours
      'equipment_check_interval': 12, // hours
      'material_stock_warning': 20.0, // percentage
    },
  };

  // UI Colors by Property Type
  static const Map<PropertyType, Map<String, int>> typeColors = {
    PropertyType.residential: {
      'primary': 0xFF2E7D32, // Green
      'secondary': 0xFF66BB6A,
      'accent': 0xFF4CAF50,
    },
    PropertyType.office: {
      'primary': 0xFF1565C0, // Blue
      'secondary': 0xFF42A5F5,
      'accent': 0xFF2196F3,
    },
    PropertyType.constructionSite: {
      'primary': 0xFFE65100, // Orange
      'secondary': 0xFFFF9800,
      'accent': 0xFFFF6F00,
    },
  };

  // Icons by Property Type
  static const Map<PropertyType, Map<String, int>> typeIcons = {
    PropertyType.residential: {
      'main': 0xe318, // Icons.home
      'services': 0xe3ab, // Icons.home_repair_service
      'maintenance': 0xe3ac, // Icons.handyman
    },
    PropertyType.office: {
      'main': 0xe1af, // Icons.business
      'services': 0xe1b0, // Icons.business_center
      'maintenance': 0xe3ac, // Icons.handyman
    },
    PropertyType.constructionSite: {
      'main': 0xe1c6, // Icons.construction
      'services': 0xe1c7, // Icons.engineering
      'maintenance': 0xe1c8, // Icons.build
    },
  };

  // Helper Methods
  static PropertyType fromString(String type) {
    switch (type.toLowerCase()) {
      case 'residential':
        return PropertyType.residential;
      case 'office':
        return PropertyType.office;
      case 'construction_site':
        return PropertyType.constructionSite;
      default:
        throw ArgumentError('Invalid property type: $type');
    }
  }

  static String typeToString(PropertyType type) {
    switch (type) {
      case PropertyType.residential:
        return 'residential';
      case PropertyType.office:
        return 'office';
      case PropertyType.constructionSite:
        return 'construction_site';
    }
  }

  static String getDisplayName(PropertyType type) {
    return typeNames[type] ?? 'Unknown';
  }

  static String getDescription(PropertyType type) {
    return typeDescriptions[type] ?? '';
  }

  static List<String> getDefaultServices(PropertyType type) {
    return defaultServices[type] ?? [];
  }

  static List<String> getAvailableFeatures(PropertyType type) {
    return availableFeatures[type] ?? [];
  }

  static Map<String, int> getServicePriority(PropertyType type) {
    return servicePriority[type] ?? {};
  }

  static List<String> getMaintenanceCategories(PropertyType type) {
    return maintenanceCategories[type] ?? [];
  }

  static Map<String, dynamic> getDefaultThresholds(PropertyType type) {
    return defaultThresholds[type] ?? {};
  }

  static bool hasFeature(PropertyType type, String feature) {
    return getAvailableFeatures(type).contains(feature);
  }

  static bool hasService(PropertyType type, String service) {
    return getDefaultServices(type).contains(service);
  }

  static int getServicePriorityValue(PropertyType type, String service) {
    return getServicePriority(type)[service] ?? 999;
  }
}
