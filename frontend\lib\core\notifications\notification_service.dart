/// Comprehensive notification service for SRSR Property Management
/// Handles local notifications, alerts, and real-time updates

import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:permission_handler/permission_handler.dart';
import '../business_logic/status_calculator.dart';

enum NotificationType {
  maintenance,
  fuelLow,
  securityAlert,
  attendanceReminder,
  systemUpdate,
  propertyStatus,
  thresholdAlert,
  alertResolution,
}

enum NotificationPriority {
  low,
  normal,
  high,
  critical,
}

class AppNotification {
  final String id;
  final String title;
  final String body;
  final NotificationType type;
  final NotificationPriority priority;
  final DateTime timestamp;
  final Map<String, dynamic>? data;
  final bool isRead;

  const AppNotification({
    required this.id,
    required this.title,
    required this.body,
    required this.type,
    required this.priority,
    required this.timestamp,
    this.data,
    this.isRead = false,
  });

  AppNotification copyWith({
    String? id,
    String? title,
    String? body,
    NotificationType? type,
    NotificationPriority? priority,
    DateTime? timestamp,
    Map<String, dynamic>? data,
    bool? isRead,
  }) {
    return AppNotification(
      id: id ?? this.id,
      title: title ?? this.title,
      body: body ?? this.body,
      type: type ?? this.type,
      priority: priority ?? this.priority,
      timestamp: timestamp ?? this.timestamp,
      data: data ?? this.data,
      isRead: isRead ?? this.isRead,
    );
  }

  Color get priorityColor {
    switch (priority) {
      case NotificationPriority.low:
        return Colors.blue;
      case NotificationPriority.normal:
        return Colors.green;
      case NotificationPriority.high:
        return Colors.orange;
      case NotificationPriority.critical:
        return Colors.red;
    }
  }

  IconData get typeIcon {
    switch (type) {
      case NotificationType.maintenance:
        return Icons.build;
      case NotificationType.fuelLow:
        return Icons.local_gas_station;
      case NotificationType.securityAlert:
        return Icons.security;
      case NotificationType.attendanceReminder:
        return Icons.people;
      case NotificationType.systemUpdate:
        return Icons.system_update;
      case NotificationType.propertyStatus:
        return Icons.business;
      case NotificationType.thresholdAlert:
        return Icons.warning;
      case NotificationType.alertResolution:
        return Icons.check_circle;
    }
  }
}

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  final List<AppNotification> _notifications = [];
  final List<Function(AppNotification)> _listeners = [];

  List<AppNotification> get notifications => List.unmodifiable(_notifications);
  int get unreadCount => _notifications.where((n) => !n.isRead).length;

  Future<void> initialize() async {
    // Request permissions
    await _requestPermissions();

    // Initialize local notifications
    const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const initializationSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _flutterLocalNotificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    // Create notification channels
    await _createNotificationChannels();
  }

  Future<void> _requestPermissions() async {
    await Permission.notification.request();
  }

  Future<void> _createNotificationChannels() async {
    const maintenanceChannel = AndroidNotificationChannel(
      'maintenance',
      'Maintenance Alerts',
      description: 'Notifications for maintenance issues and updates',
      importance: Importance.high,
    );

    const fuelChannel = AndroidNotificationChannel(
      'fuel',
      'Fuel Alerts',
      description: 'Notifications for low fuel levels',
      importance: Importance.high,
    );

    const securityChannel = AndroidNotificationChannel(
      'security',
      'Security Alerts',
      description: 'Security incidents and alerts',
      importance: Importance.max,
    );

    const attendanceChannel = AndroidNotificationChannel(
      'attendance',
      'Attendance Reminders',
      description: 'Attendance tracking reminders',
      importance: Importance.defaultImportance,
    );

    await _flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(maintenanceChannel);

    await _flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(fuelChannel);

    await _flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(securityChannel);

    await _flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(attendanceChannel);
  }

  void _onNotificationTapped(NotificationResponse response) {
    // Handle notification tap
    final notificationId = response.id.toString();
    final notification = _notifications.firstWhere(
      (n) => n.id == notificationId,
      orElse: () => _notifications.first,
    );

    // Mark as read
    markAsRead(notification.id);

    // Navigate based on notification type
    _handleNotificationNavigation(notification);
  }

  void _handleNotificationNavigation(AppNotification notification) {
    // This would typically use a navigation service
    // For now, we'll just log the action
    // TODO: Implement proper navigation handling
  }

  Future<void> showNotification(AppNotification notification) async {
    // Add to internal list
    _notifications.insert(0, notification);

    // Notify listeners
    for (final listener in _listeners) {
      listener(notification);
    }

    // Show local notification
    await _showLocalNotification(notification);
  }

  Future<void> _showLocalNotification(AppNotification notification) async {
    final androidDetails = AndroidNotificationDetails(
      _getChannelId(notification.type),
      _getChannelName(notification.type),
      channelDescription: _getChannelDescription(notification.type),
      importance: _getImportance(notification.priority),
      priority: _getPriority(notification.priority),
      icon: '@mipmap/ic_launcher',
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    final notificationDetails = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _flutterLocalNotificationsPlugin.show(
      int.parse(notification.id.hashCode.toString().substring(0, 8)),
      notification.title,
      notification.body,
      notificationDetails,
      payload: notification.id,
    );
  }

  String _getChannelId(NotificationType type) {
    switch (type) {
      case NotificationType.maintenance:
        return 'maintenance';
      case NotificationType.fuelLow:
        return 'fuel';
      case NotificationType.securityAlert:
        return 'security';
      case NotificationType.attendanceReminder:
        return 'attendance';
      default:
        return 'general';
    }
  }

  String _getChannelName(NotificationType type) {
    switch (type) {
      case NotificationType.maintenance:
        return 'Maintenance Alerts';
      case NotificationType.fuelLow:
        return 'Fuel Alerts';
      case NotificationType.securityAlert:
        return 'Security Alerts';
      case NotificationType.attendanceReminder:
        return 'Attendance Reminders';
      default:
        return 'General Notifications';
    }
  }

  String _getChannelDescription(NotificationType type) {
    switch (type) {
      case NotificationType.maintenance:
        return 'Notifications for maintenance issues and updates';
      case NotificationType.fuelLow:
        return 'Notifications for low fuel levels';
      case NotificationType.securityAlert:
        return 'Security incidents and alerts';
      case NotificationType.attendanceReminder:
        return 'Attendance tracking reminders';
      default:
        return 'General application notifications';
    }
  }

  Importance _getImportance(NotificationPriority priority) {
    switch (priority) {
      case NotificationPriority.low:
        return Importance.low;
      case NotificationPriority.normal:
        return Importance.defaultImportance;
      case NotificationPriority.high:
        return Importance.high;
      case NotificationPriority.critical:
        return Importance.max;
    }
  }

  Priority _getPriority(NotificationPriority priority) {
    switch (priority) {
      case NotificationPriority.low:
        return Priority.low;
      case NotificationPriority.normal:
        return Priority.defaultPriority;
      case NotificationPriority.high:
        return Priority.high;
      case NotificationPriority.critical:
        return Priority.max;
    }
  }

  void addListener(Function(AppNotification) listener) {
    _listeners.add(listener);
  }

  void removeListener(Function(AppNotification) listener) {
    _listeners.remove(listener);
  }

  void markAsRead(String notificationId) {
    final index = _notifications.indexWhere((n) => n.id == notificationId);
    if (index != -1) {
      _notifications[index] = _notifications[index].copyWith(isRead: true);
    }
  }

  void markAllAsRead() {
    for (int i = 0; i < _notifications.length; i++) {
      _notifications[i] = _notifications[i].copyWith(isRead: true);
    }
  }

  void clearNotifications() {
    _notifications.clear();
  }

  // Predefined notification creators
  static AppNotification createMaintenanceAlert({
    required String propertyName,
    required String issueTitle,
    required String priority,
    Map<String, dynamic>? data,
  }) {
    return AppNotification(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      title: 'Maintenance Alert - $propertyName',
      body: '$issueTitle (Priority: ${priority.toUpperCase()})',
      type: NotificationType.maintenance,
      priority: priority == 'critical'
          ? NotificationPriority.critical
          : priority == 'high'
              ? NotificationPriority.high
              : NotificationPriority.normal,
      timestamp: DateTime.now(),
      data: data,
    );
  }

  static AppNotification createFuelAlert({
    required String propertyName,
    required double fuelLevel,
    required double backupHours,
    Map<String, dynamic>? data,
  }) {
    return AppNotification(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      title: 'Low Fuel Alert - $propertyName',
      body: 'Fuel level: ${fuelLevel.toStringAsFixed(1)}L (${backupHours.toStringAsFixed(1)} hours backup)',
      type: NotificationType.fuelLow,
      priority: backupHours < 6
          ? NotificationPriority.critical
          : NotificationPriority.high,
      timestamp: DateTime.now(),
      data: data,
    );
  }

  static AppNotification createSecurityAlert({
    required String propertyName,
    required String incident,
    Map<String, dynamic>? data,
  }) {
    return AppNotification(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      title: 'Security Alert - $propertyName',
      body: incident,
      type: NotificationType.securityAlert,
      priority: NotificationPriority.critical,
      timestamp: DateTime.now(),
      data: data,
    );
  }

  static AppNotification createAttendanceReminder({
    required String message,
    Map<String, dynamic>? data,
  }) {
    return AppNotification(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      title: 'Attendance Reminder',
      body: message,
      type: NotificationType.attendanceReminder,
      priority: NotificationPriority.normal,
      timestamp: DateTime.now(),
      data: data,
    );
  }

  // Status-based notification generation
  static List<AppNotification> generateStatusNotifications(PropertyStatus propertyStatus) {
    final notifications = <AppNotification>[];

    for (final entry in propertyStatus.functionalAreas.entries) {
      final areaName = entry.key;
      final areaStatus = entry.value;

      if (areaStatus.status == StatusLevel.red) {
        if (areaName == 'electricity') {
          final fuelMetric = areaStatus.metrics.firstWhere(
            (m) => m.name == 'fuel_level',
            orElse: () => StatusMetric.create('fuel_level', 0, 'L', StatusLevel.red),
          );
          final backupMetric = areaStatus.metrics.firstWhere(
            (m) => m.name == 'backup_hours',
            orElse: () => StatusMetric.create('backup_hours', 0, 'hrs', StatusLevel.red),
          );

          notifications.add(createFuelAlert(
            propertyName: propertyStatus.name,
            fuelLevel: fuelMetric.value.toDouble(),
            backupHours: backupMetric.value.toDouble(),
            data: {'propertyId': propertyStatus.id, 'area': areaName},
          ));
        } else if (areaName == 'security') {
          notifications.add(createSecurityAlert(
            propertyName: propertyStatus.name,
            incident: 'Security system issues detected',
            data: {'propertyId': propertyStatus.id, 'area': areaName},
          ));
        }
      }
    }

    return notifications;
  }
}
