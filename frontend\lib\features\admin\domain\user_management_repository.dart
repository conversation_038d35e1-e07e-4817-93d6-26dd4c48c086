import '../../../shared/models/user.dart';
import '../data/user_management_api_service.dart';

abstract class UserManagementRepository {
  Future<List<User>> getUsers({
    int? page,
    int? limit,
    String? role,
    String? status,
    String? search,
  });

  Future<User> getUserById(String id);

  Future<User> createUser(CreateUserRequest request);

  Future<User> updateUser(String id, UpdateUserRequest request);

  Future<void> deleteUser(String id);

  Future<User> activateUser(String id);

  Future<User> deactivateUser(String id);

  Future<User> approveUser(String id);

  Future<User> rejectUser(String id);

  Future<User> assignRoles(String id, AssignRolesRequest request);

  Future<User> removeRole(String id, String roleId);

  Future<List<User>> getPendingUsers();

  Future<List<User>> bulkApproveUsers(BulkApproveRequest request);

  // Additional methods needed by providers
  Future<List<User>> getAllUsers();
  Future<User> updateUserRole(String userId, String roleId);
}
