import 'package:dio/dio.dart';
import '../../../core/network/api_client.dart';
import '../../../core/network/api_result.dart';
import 'models/custom_screen.dart';

class ScreenManagementApiService {
  final ApiClient _apiClient;

  ScreenManagementApiService(this._apiClient);

  // Get all custom screens
  Future<ApiResult<List<CustomScreen>>> getScreens() async {
    try {
      final response = await _apiClient.get('/admin/screens');
      
      if (response.data['success'] == true) {
        final List<dynamic> screensJson = response.data['data'] ?? [];
        final screens = screensJson.map((json) => CustomScreen.fromJson(json)).toList();
        return ApiResult.success(screens);
      } else {
        return ApiResult.error(response.data['error'] ?? 'Failed to fetch screens');
      }
    } on DioException catch (e) {
      return ApiResult.error(e.message ?? 'Network error occurred');
    } catch (e) {
      return ApiResult.error('An unexpected error occurred: $e');
    }
  }

  // Get screen by ID
  Future<ApiResult<CustomScreen>> getScreen(String id) async {
    try {
      final response = await _apiClient.get('/admin/screens/$id');
      
      if (response.data['success'] == true) {
        final screen = CustomScreen.fromJson(response.data['data']);
        return ApiResult.success(screen);
      } else {
        return ApiResult.error(response.data['error'] ?? 'Failed to fetch screen');
      }
    } on DioException catch (e) {
      return ApiResult.error(e.message ?? 'Network error occurred');
    } catch (e) {
      return ApiResult.error('An unexpected error occurred: $e');
    }
  }

  // Create new screen
  Future<ApiResult<CustomScreen>> createScreen(Map<String, dynamic> screenData) async {
    try {
      final response = await _apiClient.post('/admin/screens', data: screenData);
      
      if (response.data['success'] == true) {
        final screen = CustomScreen.fromJson(response.data['data']);
        return ApiResult.success(screen);
      } else {
        return ApiResult.error(response.data['error'] ?? 'Failed to create screen');
      }
    } on DioException catch (e) {
      return ApiResult.error(e.message ?? 'Network error occurred');
    } catch (e) {
      return ApiResult.error('An unexpected error occurred: $e');
    }
  }

  // Update screen
  Future<ApiResult<CustomScreen>> updateScreen(String id, Map<String, dynamic> screenData) async {
    try {
      final response = await _apiClient.put('/admin/screens/$id', data: screenData);
      
      if (response.data['success'] == true) {
        final screen = CustomScreen.fromJson(response.data['data']);
        return ApiResult.success(screen);
      } else {
        return ApiResult.error(response.data['error'] ?? 'Failed to update screen');
      }
    } on DioException catch (e) {
      return ApiResult.error(e.message ?? 'Network error occurred');
    } catch (e) {
      return ApiResult.error('An unexpected error occurred: $e');
    }
  }

  // Delete screen
  Future<ApiResult<bool>> deleteScreen(String id) async {
    try {
      final response = await _apiClient.delete('/admin/screens/$id');
      
      if (response.data['success'] == true) {
        return ApiResult.success(true);
      } else {
        return ApiResult.error(response.data['error'] ?? 'Failed to delete screen');
      }
    } on DioException catch (e) {
      return ApiResult.error(e.message ?? 'Network error occurred');
    } catch (e) {
      return ApiResult.error('An unexpected error occurred: $e');
    }
  }

  // Get all custom widgets
  Future<ApiResult<List<CustomWidget>>> getWidgets() async {
    try {
      final response = await _apiClient.get('/admin/widgets');
      
      if (response.data['success'] == true) {
        final List<dynamic> widgetsJson = response.data['data'] ?? [];
        final widgets = widgetsJson.map((json) => CustomWidget.fromJson(json)).toList();
        return ApiResult.success(widgets);
      } else {
        return ApiResult.error(response.data['error'] ?? 'Failed to fetch widgets');
      }
    } on DioException catch (e) {
      return ApiResult.error(e.message ?? 'Network error occurred');
    } catch (e) {
      return ApiResult.error('An unexpected error occurred: $e');
    }
  }

  // Create new widget
  Future<ApiResult<CustomWidget>> createWidget(Map<String, dynamic> widgetData) async {
    try {
      final response = await _apiClient.post('/admin/widgets', data: widgetData);
      
      if (response.data['success'] == true) {
        final widget = CustomWidget.fromJson(response.data['data']);
        return ApiResult.success(widget);
      } else {
        return ApiResult.error(response.data['error'] ?? 'Failed to create widget');
      }
    } on DioException catch (e) {
      return ApiResult.error(e.message ?? 'Network error occurred');
    } catch (e) {
      return ApiResult.error('An unexpected error occurred: $e');
    }
  }

  // Update widget
  Future<ApiResult<CustomWidget>> updateWidget(String id, Map<String, dynamic> widgetData) async {
    try {
      final response = await _apiClient.put('/admin/widgets/$id', data: widgetData);
      
      if (response.data['success'] == true) {
        final widget = CustomWidget.fromJson(response.data['data']);
        return ApiResult.success(widget);
      } else {
        return ApiResult.error(response.data['error'] ?? 'Failed to update widget');
      }
    } on DioException catch (e) {
      return ApiResult.error(e.message ?? 'Network error occurred');
    } catch (e) {
      return ApiResult.error('An unexpected error occurred: $e');
    }
  }

  // Delete widget
  Future<ApiResult<bool>> deleteWidget(String id) async {
    try {
      final response = await _apiClient.delete('/admin/widgets/$id');
      
      if (response.data['success'] == true) {
        return ApiResult.success(true);
      } else {
        return ApiResult.error(response.data['error'] ?? 'Failed to delete widget');
      }
    } on DioException catch (e) {
      return ApiResult.error(e.message ?? 'Network error occurred');
    } catch (e) {
      return ApiResult.error('An unexpected error occurred: $e');
    }
  }

  // Get available widget types
  Future<ApiResult<List<Map<String, dynamic>>>> getWidgetTypes() async {
    try {
      final response = await _apiClient.get('/admin/widget-types');
      
      if (response.data['success'] == true) {
        final List<dynamic> typesJson = response.data['data'] ?? [];
        final types = typesJson.cast<Map<String, dynamic>>();
        return ApiResult.success(types);
      } else {
        return ApiResult.error(response.data['error'] ?? 'Failed to fetch widget types');
      }
    } on DioException catch (e) {
      return ApiResult.error(e.message ?? 'Network error occurred');
    } catch (e) {
      return ApiResult.error('An unexpected error occurred: $e');
    }
  }

  // Validate screen route
  Future<ApiResult<bool>> validateRoute(String route) async {
    try {
      final response = await _apiClient.post('/admin/screens/validate-route', data: {'route': route});
      
      if (response.data['success'] == true) {
        return ApiResult.success(response.data['data']['isValid'] ?? false);
      } else {
        return ApiResult.error(response.data['error'] ?? 'Failed to validate route');
      }
    } on DioException catch (e) {
      return ApiResult.error(e.message ?? 'Network error occurred');
    } catch (e) {
      return ApiResult.error('An unexpected error occurred: $e');
    }
  }
}
