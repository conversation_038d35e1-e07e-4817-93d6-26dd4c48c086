// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'dashboard_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DashboardStatus _$DashboardStatusFromJson(Map<String, dynamic> json) =>
    DashboardStatus(
      success: json['success'] as bool,
      data: DashboardData.fromJson(json['data'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$DashboardStatusToJson(DashboardStatus instance) =>
    <String, dynamic>{
      'success': instance.success,
      'data': instance.data,
    };

DashboardData _$DashboardDataFromJson(Map<String, dynamic> json) =>
    DashboardData(
      properties:
          PropertyStats.fromJson(json['properties'] as Map<String, dynamic>),
      maintenanceIssues: MaintenanceStats.fromJson(
          json['maintenance_issues'] as Map<String, dynamic>),
      recentAlerts: (json['recent_alerts'] as List<dynamic>)
          .map((e) => RecentAlert.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$DashboardDataToJson(DashboardData instance) =>
    <String, dynamic>{
      'properties': instance.properties,
      'maintenance_issues': instance.maintenanceIssues,
      'recent_alerts': instance.recentAlerts,
    };

PropertyStats _$PropertyStatsFromJson(Map<String, dynamic> json) =>
    PropertyStats(
      total: (json['total'] as num).toInt(),
      operational: (json['operational'] as num).toInt(),
      warning: (json['warning'] as num).toInt(),
      critical: (json['critical'] as num).toInt(),
    );

Map<String, dynamic> _$PropertyStatsToJson(PropertyStats instance) =>
    <String, dynamic>{
      'total': instance.total,
      'operational': instance.operational,
      'warning': instance.warning,
      'critical': instance.critical,
    };

MaintenanceStats _$MaintenanceStatsFromJson(Map<String, dynamic> json) =>
    MaintenanceStats(
      total: (json['total'] as num).toInt(),
      open: (json['open'] as num).toInt(),
      inProgress: (json['in_progress'] as num).toInt(),
      critical: (json['critical'] as num).toInt(),
    );

Map<String, dynamic> _$MaintenanceStatsToJson(MaintenanceStats instance) =>
    <String, dynamic>{
      'total': instance.total,
      'open': instance.open,
      'in_progress': instance.inProgress,
      'critical': instance.critical,
    };

RecentAlert _$RecentAlertFromJson(Map<String, dynamic> json) => RecentAlert(
      id: json['id'] as String,
      type: json['type'] as String,
      severity: json['severity'] as String,
      message: json['message'] as String,
      propertyName: json['property_name'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
    );

Map<String, dynamic> _$RecentAlertToJson(RecentAlert instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': instance.type,
      'severity': instance.severity,
      'message': instance.message,
      'property_name': instance.propertyName,
      'timestamp': instance.timestamp.toIso8601String(),
    };
