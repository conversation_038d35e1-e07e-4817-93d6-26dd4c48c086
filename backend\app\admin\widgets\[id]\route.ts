import { NextRequest } from 'next/server';
import { requireAuth } from '@/lib/auth';
import { createApiResponse, getRequestBody, handleError, corsHeaders } from '@/lib/utils';
import { prisma } from '@/lib/prisma';
import { validateRequest } from '@/lib/validation';
import Joi from 'joi';

const updateWidgetSchema = Joi.object({
  name: Joi.string(),
  type: Joi.string(),
  screen_name: Joi.string(),
  config: Joi.object(),
  position: Joi.object({
    x: Joi.number(),
    y: Joi.number(),
    width: Joi.number(),
    height: Joi.number(),
  }),
  is_visible: Joi.boolean(),
  is_enabled: Joi.boolean(),
  required_permissions: Joi.array().items(Joi.string()),
  allowed_roles: Joi.array().items(Joi.string()),
});

async function getWidgetHandler(
  request: NextRequest,
  context: { params: { id: string } },
  currentUser: any
) {
  try {
    // Check if user has admin permissions
    const hasAdminRole = currentUser.roles?.includes('admin');
    if (!hasAdminRole) {
      return Response.json(
        createApiResponse(null, 'Insufficient permissions', 'FORBIDDEN'),
        { status: 403 }
      );
    }

    const { id } = context.params;

    const widget = await prisma.widgetPermission.findUnique({
      where: { id },
    });

    if (!widget) {
      return Response.json(
        createApiResponse(null, 'Widget not found', 'NOT_FOUND'),
        { status: 404 }
      );
    }

    // Transform to match frontend expectations
    const transformedWidget = {
      id: widget.id,
      name: widget.widgetName,
      type: widget.widgetName.includes('chart') ? 'chart' :
            widget.widgetName.includes('stat') ? 'statCard' :
            widget.widgetName.includes('table') ? 'table' : 'custom',
      screen_name: widget.screenName,
      config: {},
      position: { x: 0, y: 0, width: 1, height: 1 },
      is_visible: widget.isVisible,
      is_enabled: widget.isEnabled,
      required_permissions: widget.requiredPermissions || [],
      allowed_roles: widget.allowedRoles || [],
      created_at: widget.createdAt,
      updated_at: widget.updatedAt,
    };

    return Response.json(
      createApiResponse(transformedWidget),
      {
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to fetch widget');
  }
}

async function updateWidgetHandler(
  request: NextRequest,
  context: { params: { id: string } },
  currentUser: any
) {
  try {
    // Check if user has admin permissions
    const hasAdminRole = currentUser.roles?.includes('admin');
    if (!hasAdminRole) {
      return Response.json(
        createApiResponse(null, 'Insufficient permissions', 'FORBIDDEN'),
        { status: 403 }
      );
    }

    const { id } = context.params;
    const body = await getRequestBody(request);

    // Validate request body
    const validation = validateRequest(updateWidgetSchema, body);
    if (!validation.isValid) {
      return Response.json(
        createApiResponse(null, 'Validation failed', 'VALIDATION_ERROR'),
        { status: 400 }
      );
    }

    const updateData: any = {};

    if (validation.data.name) updateData.widgetName = validation.data.name;
    if (validation.data.screen_name) updateData.screenName = validation.data.screen_name;
    if (validation.data.is_visible !== undefined) updateData.isVisible = validation.data.is_visible;
    if (validation.data.is_enabled !== undefined) updateData.isEnabled = validation.data.is_enabled;
    if (validation.data.required_permissions) updateData.requiredPermissions = validation.data.required_permissions;
    if (validation.data.allowed_roles) updateData.allowedRoles = validation.data.allowed_roles;

    const widget = await prisma.widgetPermission.update({
      where: { id },
      data: updateData,
    });

    // Transform response to match frontend expectations
    const transformedWidget = {
      id: widget.id,
      name: widget.widgetName,
      type: validation.data.type || 'custom',
      screen_name: widget.screenName,
      config: validation.data.config || {},
      position: validation.data.position || { x: 0, y: 0, width: 1, height: 1 },
      is_visible: widget.isVisible,
      is_enabled: widget.isEnabled,
      required_permissions: widget.requiredPermissions || [],
      allowed_roles: widget.allowedRoles || [],
      created_at: widget.createdAt,
      updated_at: widget.updatedAt,
    };

    return Response.json(
      createApiResponse(transformedWidget),
      {
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to update widget');
  }
}

async function deleteWidgetHandler(
  request: NextRequest,
  context: { params: { id: string } },
  currentUser: any
) {
  try {
    // Check if user has admin permissions
    const hasAdminRole = currentUser.roles?.includes('admin');
    if (!hasAdminRole) {
      return Response.json(
        createApiResponse(null, 'Insufficient permissions', 'FORBIDDEN'),
        { status: 403 }
      );
    }

    const { id } = context.params;

    await prisma.widgetPermission.delete({
      where: { id },
    });

    return Response.json(
      createApiResponse({ message: 'Widget deleted successfully' }),
      {
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to delete widget');
  }
}

// GET /admin/widgets/[id] - Get specific widget
export const GET = requireAuth(getWidgetHandler);

// PUT /admin/widgets/[id] - Update widget
export const PUT = requireAuth(updateWidgetHandler);

// DELETE /admin/widgets/[id] - Delete widget
export const DELETE = requireAuth(deleteWidgetHandler);

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: corsHeaders(),
  });
}
