import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../../../shared/models/attendance.dart';

class AttendanceStatsWidget extends StatelessWidget {
  final List<AttendanceRecord> attendanceRecords;
  final DateTime? startDate;
  final DateTime? endDate;

  const AttendanceStatsWidget({
    super.key,
    required this.attendanceRecords,
    this.startDate,
    this.endDate,
  });

  @override
  Widget build(BuildContext context) {
    final stats = _calculateStats();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Attendance Statistics',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (startDate != null && endDate != null)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.blue.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      '${_formatDate(startDate!)} - ${_formatDate(endDate!)}',
                      style: const TextStyle(
                        fontSize: 12,
                        color: Colors.blue,
                      ),
                    ),
                  ),
              ],
            ),

            const SizedBox(height: 20),

            // Summary Cards
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'Total Records',
                    stats['total'].toString(),
                    Icons.people,
                    Colors.blue,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    'Present',
                    stats['present'].toString(),
                    Icons.check_circle,
                    Colors.green,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    'Absent',
                    stats['absent'].toString(),
                    Icons.cancel,
                    Colors.red,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    'Late',
                    stats['late'].toString(),
                    Icons.access_time,
                    Colors.orange,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 20),

            // Charts Row
            Row(
              children: [
                // Pie Chart
                Expanded(
                  flex: 1,
                  child: SizedBox(
                    height: 200,
                    child: Column(
                      children: [
                        Text(
                          'Attendance Distribution',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Expanded(
                          child: PieChart(
                            PieChartData(
                              sections: _buildPieChartSections(stats),
                              centerSpaceRadius: 40,
                              sectionsSpace: 2,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(width: 20),

                // Attendance Rate
                Expanded(
                  flex: 1,
                  child: _buildAttendanceRate(context, stats),
                ),
              ],
            ),

            const SizedBox(height: 20),

            // Daily Trend (if we have multiple days)
            if (_hasDailyData()) _buildDailyTrend(context),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildAttendanceRate(BuildContext context, Map<String, int> stats) {
    final total = stats['total']!;
    final present = stats['present']!;
    final rate = total > 0 ? (present / total * 100) : 0.0;

    return Container(
      height: 200,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            'Attendance Rate',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          SizedBox(
            width: 100,
            height: 100,
            child: CircularProgressIndicator(
              value: rate / 100,
              strokeWidth: 8,
              backgroundColor: Colors.grey[300],
              valueColor: AlwaysStoppedAnimation<Color>(
                rate >= 90 ? Colors.green :
                rate >= 75 ? Colors.orange : Colors.red,
              ),
            ),
          ),
          const SizedBox(height: 16),
          Text(
            '${rate.toStringAsFixed(1)}%',
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDailyTrend(BuildContext context) {
    final dailyStats = _calculateDailyStats();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Daily Attendance Trend',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        SizedBox(
          height: 200,
          child: LineChart(
            LineChartData(
              gridData: const FlGridData(show: true),
              titlesData: FlTitlesData(
                leftTitles: const AxisTitles(
                  sideTitles: SideTitles(showTitles: true),
                ),
                bottomTitles: AxisTitles(
                  sideTitles: SideTitles(
                    showTitles: true,
                    getTitlesWidget: (value, meta) {
                      final date = DateTime.fromMillisecondsSinceEpoch(value.toInt());
                      return Text(
                        '${date.day}/${date.month}',
                        style: const TextStyle(fontSize: 10),
                      );
                    },
                  ),
                ),
                topTitles: const AxisTitles(
                  sideTitles: SideTitles(showTitles: false),
                ),
                rightTitles: const AxisTitles(
                  sideTitles: SideTitles(showTitles: false),
                ),
              ),
              borderData: FlBorderData(show: true),
              lineBarsData: [
                LineChartBarData(
                  spots: dailyStats.entries.map((entry) {
                    return FlSpot(
                      entry.key.millisecondsSinceEpoch.toDouble(),
                      entry.value.toDouble(),
                    );
                  }).toList(),
                  isCurved: true,
                  color: Colors.blue,
                  barWidth: 3,
                  dotData: const FlDotData(show: true),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  List<PieChartSectionData> _buildPieChartSections(Map<String, int> stats) {
    final total = stats['total']!;
    if (total == 0) return [];

    return [
      PieChartSectionData(
        color: Colors.green,
        value: stats['present']!.toDouble(),
        title: '${(stats['present']! / total * 100).toStringAsFixed(1)}%',
        radius: 50,
        titleStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
      PieChartSectionData(
        color: Colors.red,
        value: stats['absent']!.toDouble(),
        title: '${(stats['absent']! / total * 100).toStringAsFixed(1)}%',
        radius: 50,
        titleStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
      PieChartSectionData(
        color: Colors.orange,
        value: stats['late']!.toDouble(),
        title: '${(stats['late']! / total * 100).toStringAsFixed(1)}%',
        radius: 50,
        titleStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
      if (stats['halfDay']! > 0)
        PieChartSectionData(
          color: Colors.blue,
          value: stats['halfDay']!.toDouble(),
          title: '${(stats['halfDay']! / total * 100).toStringAsFixed(1)}%',
          radius: 50,
          titleStyle: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
    ];
  }

  Map<String, int> _calculateStats() {
    final stats = {
      'total': attendanceRecords.length,
      'present': 0,
      'absent': 0,
      'late': 0,
      'halfDay': 0,
    };

    for (final record in attendanceRecords) {
      switch (record.status.toLowerCase()) {
        case 'present':
          stats['present'] = stats['present']! + 1;
          break;
        case 'absent':
          stats['absent'] = stats['absent']! + 1;
          break;
        case 'late':
          stats['late'] = stats['late']! + 1;
          break;
        case 'half day':
          stats['halfDay'] = stats['halfDay']! + 1;
          break;
      }
    }

    return stats;
  }

  Map<DateTime, int> _calculateDailyStats() {
    final dailyStats = <DateTime, int>{};

    for (final record in attendanceRecords) {
      final date = DateTime(record.date.year, record.date.month, record.date.day);
      if (record.status.toLowerCase() == 'present') {
        dailyStats[date] = (dailyStats[date] ?? 0) + 1;
      }
    }

    return dailyStats;
  }

  bool _hasDailyData() {
    final uniqueDates = attendanceRecords.map((r) =>
      DateTime(r.date.year, r.date.month, r.date.day)
    ).toSet();
    return uniqueDates.length > 1;
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
