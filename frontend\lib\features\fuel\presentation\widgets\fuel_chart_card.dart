import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../../../shared/models/generator_fuel.dart';

enum FuelChartType {
  consumption,
  level,
  efficiency,
}

class FuelChartCard extends StatelessWidget {
  final List<GeneratorFuelLog> fuelLogs;
  final FuelChartType chartType;
  final String title;

  const FuelChartCard({
    super.key,
    required this.fuelLogs,
    required this.chartType,
    required this.title,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: _buildChart(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildChart() {
    if (fuelLogs.isEmpty) {
      return const Center(
        child: Text('No data available'),
      );
    }

    switch (chartType) {
      case FuelChartType.consumption:
        return _buildConsumptionChart();
      case FuelChartType.level:
        return _buildLevelChart();
      case FuelChartType.efficiency:
        return _buildEfficiencyChart();
    }
  }

  Widget _buildConsumptionChart() {
    final spots = fuelLogs.asMap().entries.map((entry) {
      final index = entry.key.toDouble();
      final log = entry.value;
      return FlSpot(index, log.consumptionRate ?? 0);
    }).toList();

    return LineChart(
      LineChartData(
        gridData: const FlGridData(show: true),
        titlesData: FlTitlesData(
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 40,
              getTitlesWidget: (value, meta) {
                return Text('${value.toInt()}L');
              },
            ),
          ),
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 30,
              getTitlesWidget: (value, meta) {
                final index = value.toInt();
                if (index >= 0 && index < fuelLogs.length) {
                  final date = fuelLogs[index].recordedAt;
                  return Text('${date.day}/${date.month}');
                }
                return const Text('');
              },
            ),
          ),
          topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
        ),
        borderData: FlBorderData(show: true),
        lineBarsData: [
          LineChartBarData(
            spots: spots,
            isCurved: true,
            color: Colors.blue,
            barWidth: 3,
            dotData: const FlDotData(show: true),
          ),
        ],
      ),
    );
  }

  Widget _buildLevelChart() {
    final spots = fuelLogs.asMap().entries.map((entry) {
      final index = entry.key.toDouble();
      final log = entry.value;
      return FlSpot(index, log.fuelLevelLiters);
    }).toList();

    return LineChart(
      LineChartData(
        gridData: const FlGridData(show: true),
        titlesData: FlTitlesData(
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 40,
              getTitlesWidget: (value, meta) {
                return Text('${value.toInt()}L');
              },
            ),
          ),
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 30,
              getTitlesWidget: (value, meta) {
                final index = value.toInt();
                if (index >= 0 && index < fuelLogs.length) {
                  final date = fuelLogs[index].recordedAt;
                  return Text('${date.day}/${date.month}');
                }
                return const Text('');
              },
            ),
          ),
          topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
        ),
        borderData: FlBorderData(show: true),
        lineBarsData: [
          LineChartBarData(
            spots: spots,
            isCurved: true,
            color: Colors.green,
            barWidth: 3,
            dotData: const FlDotData(show: true),
          ),
        ],
      ),
    );
  }

  Widget _buildEfficiencyChart() {
    final spots = fuelLogs.asMap().entries.map((entry) {
      final index = entry.key.toDouble();
      final log = entry.value;
      // Use efficiency percentage if available, otherwise calculate
      final efficiency = log.efficiencyPercentage ??
          (log.runtimeHours != null && log.runtimeHours! > 0 && log.consumptionRate != null
              ? (log.consumptionRate! / log.runtimeHours!) * 100
              : 0.0);
      return FlSpot(index, efficiency);
    }).toList();

    return LineChart(
      LineChartData(
        gridData: const FlGridData(show: true),
        titlesData: FlTitlesData(
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 40,
              getTitlesWidget: (value, meta) {
                return Text('${value.toStringAsFixed(1)}L/h');
              },
            ),
          ),
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 30,
              getTitlesWidget: (value, meta) {
                final index = value.toInt();
                if (index >= 0 && index < fuelLogs.length) {
                  final date = fuelLogs[index].recordedAt;
                  return Text('${date.day}/${date.month}');
                }
                return const Text('');
              },
            ),
          ),
          topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
        ),
        borderData: FlBorderData(show: true),
        lineBarsData: [
          LineChartBarData(
            spots: spots,
            isCurved: true,
            color: Colors.orange,
            barWidth: 3,
            dotData: const FlDotData(show: true),
          ),
        ],
      ),
    );
  }
}
