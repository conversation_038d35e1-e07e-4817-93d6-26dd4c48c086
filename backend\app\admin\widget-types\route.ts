import { NextRequest } from 'next/server';
import { requireAuth } from '@/lib/auth';
import { createApiResponse, handleError, corsHeaders } from '@/lib/utils';

async function getWidgetTypesHandler(request: NextRequest, context: any, currentUser: any) {
  try {
    // Check if user has admin permissions
    const hasAdminRole = currentUser.roles?.includes('admin');
    if (!hasAdminRole) {
      return Response.json(
        createApiResponse(null, 'Insufficient permissions', 'FORBIDDEN'),
        { status: 403 }
      );
    }

    // Define available widget types that match the frontend expectations
    const widgetTypes = [
      {
        id: 'statCard',
        name: 'Stat Card',
        description: 'Display key metrics and statistics',
        icon: 'assessment',
        category: 'metrics',
        config_schema: {
          title: { type: 'string', required: true },
          value: { type: 'number', required: true },
          unit: { type: 'string', required: false },
          trend: { type: 'string', enum: ['up', 'down', 'neutral'], required: false },
          color: { type: 'string', required: false },
        },
      },
      {
        id: 'chart',
        name: 'Chart',
        description: 'Display data in various chart formats',
        icon: 'bar_chart',
        category: 'visualization',
        config_schema: {
          chart_type: { type: 'string', enum: ['line', 'bar', 'pie', 'doughnut'], required: true },
          title: { type: 'string', required: true },
          data_source: { type: 'string', required: true },
          x_axis: { type: 'string', required: false },
          y_axis: { type: 'string', required: false },
        },
      },
      {
        id: 'table',
        name: 'Table',
        description: 'Display data in tabular format',
        icon: 'table_chart',
        category: 'data',
        config_schema: {
          title: { type: 'string', required: true },
          data_source: { type: 'string', required: true },
          columns: { type: 'array', required: true },
          pagination: { type: 'boolean', required: false },
          search: { type: 'boolean', required: false },
        },
      },
      {
        id: 'form',
        name: 'Form',
        description: 'Interactive form for data input',
        icon: 'dynamic_form',
        category: 'input',
        config_schema: {
          title: { type: 'string', required: true },
          fields: { type: 'array', required: true },
          submit_endpoint: { type: 'string', required: true },
          validation: { type: 'object', required: false },
        },
      },
      {
        id: 'button',
        name: 'Button',
        description: 'Action button for user interactions',
        icon: 'smart_button',
        category: 'action',
        config_schema: {
          label: { type: 'string', required: true },
          action_type: { type: 'string', enum: ['api_call', 'navigation', 'modal'], required: true },
          action_config: { type: 'object', required: true },
          style: { type: 'string', enum: ['primary', 'secondary', 'danger'], required: false },
        },
      },
      {
        id: 'text',
        name: 'Text',
        description: 'Display formatted text content',
        icon: 'text_fields',
        category: 'content',
        config_schema: {
          content: { type: 'string', required: true },
          format: { type: 'string', enum: ['plain', 'markdown', 'html'], required: false },
          style: { type: 'object', required: false },
        },
      },
      {
        id: 'image',
        name: 'Image',
        description: 'Display images and media content',
        icon: 'image',
        category: 'media',
        config_schema: {
          src: { type: 'string', required: true },
          alt: { type: 'string', required: false },
          caption: { type: 'string', required: false },
          fit: { type: 'string', enum: ['cover', 'contain', 'fill'], required: false },
        },
      },
      {
        id: 'list',
        name: 'List',
        description: 'Display items in list format',
        icon: 'list',
        category: 'data',
        config_schema: {
          title: { type: 'string', required: true },
          data_source: { type: 'string', required: true },
          item_template: { type: 'object', required: true },
          pagination: { type: 'boolean', required: false },
        },
      },
      {
        id: 'grid',
        name: 'Grid',
        description: 'Display items in grid layout',
        icon: 'grid_view',
        category: 'layout',
        config_schema: {
          title: { type: 'string', required: true },
          data_source: { type: 'string', required: true },
          columns: { type: 'number', required: false },
          item_template: { type: 'object', required: true },
        },
      },
      {
        id: 'custom',
        name: 'Custom',
        description: 'Custom widget with flexible configuration',
        icon: 'extension',
        category: 'advanced',
        config_schema: {
          component: { type: 'string', required: true },
          props: { type: 'object', required: false },
          style: { type: 'object', required: false },
        },
      },
    ];

    return Response.json(
      createApiResponse(widgetTypes),
      {
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to fetch widget types');
  }
}

// GET /admin/widget-types - Get all available widget types
export const GET = requireAuth(getWidgetTypesHandler);

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: corsHeaders(),
  });
}
