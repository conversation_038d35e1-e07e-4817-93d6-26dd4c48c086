const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkDatabase() {
  try {
    console.log('🔍 Checking existing database data...\n');

    // Get all permissions
    const permissions = await prisma.permission.findMany();
    console.log('📋 Existing Permissions:');
    permissions.forEach(p => console.log(`  - ${p.name}: ${p.description}`));
    console.log();

    // Get all roles
    const roles = await prisma.role.findMany();
    console.log('👥 Existing Roles:');
    roles.forEach(r => console.log(`  - ${r.name}: ${r.description}`));
    console.log();

    // Get role permissions
    const rolePermissions = await prisma.rolePermission.findMany({
      include: {
        role: true,
        permission: true
      }
    });

    console.log('🔗 Role-Permission Mappings:');
    const rolePermMap = {};
    rolePermissions.forEach(rp => {
      if (!rolePermMap[rp.role.name]) rolePermMap[rp.role.name] = [];
      rolePermMap[rp.role.name].push(rp.permission.name);
    });

    Object.entries(rolePermMap).forEach(([role, perms]) => {
      console.log(`  ${role}: ${perms.join(', ')}`);
    });

  } catch (error) {
    console.error('❌ Error checking database:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkDatabase();
