import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';
import { createApiResponse, getRequestBody, handleError, corsHeaders } from '@/lib/utils';
import { ThresholdMonitor, MonitoringData } from '@/lib/monitoring/threshold-monitor';
import { validateRequest } from '@/lib/validation';
import Joi from 'joi';

const monitorMetricSchema = Joi.object({
  property_id: Joi.string().uuid().required(),
  service_type: Joi.string().required(),
  metric_name: Joi.string().required(),
  value: Joi.number().required(),
  unit: Joi.string().optional(),
  timestamp: Joi.date().optional(),
});

const monitorMultipleSchema = Joi.object({
  metrics: Joi.array().items(monitorMetricSchema).required(),
});

const thresholdMonitor = new ThresholdMonitor();

async function monitorMetricHandler(request: NextRequest, context: any, currentUser: any) {
  try {
    const body = await getRequestBody(request);
    
    // Validate request body
    const validation = validateRequest(monitorMetricSchema, body);
    if (!validation.isValid) {
      return Response.json(
        createApiResponse(null, 'Validation failed', 'VALIDATION_ERROR'),
        { status: 400 }
      );
    }

    const { property_id, service_type, metric_name, value, unit, timestamp } = validation.data;

    const monitoringData: MonitoringData = {
      propertyId: property_id,
      serviceType: service_type,
      metricName: metric_name,
      value,
      unit,
      timestamp: timestamp ? new Date(timestamp) : new Date(),
    };

    // Monitor the metric
    const alerts = await thresholdMonitor.monitorMetric(monitoringData);

    return Response.json(
      createApiResponse({
        alerts: alerts.map(alert => ({
          id: alert.id,
          property_id: alert.propertyId,
          property_name: alert.propertyName,
          service_type: alert.serviceType,
          metric_name: alert.metricName,
          current_value: alert.currentValue,
          threshold_type: alert.thresholdType,
          threshold_value: alert.thresholdValue,
          message: alert.message,
          severity: alert.severity,
          timestamp: alert.timestamp,
          is_resolved: alert.isResolved,
        })),
        monitored_at: monitoringData.timestamp,
      }),
      {
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to monitor metric');
  }
}

async function monitorMultipleHandler(request: NextRequest, context: any, currentUser: any) {
  try {
    const body = await getRequestBody(request);
    
    // Validate request body
    const validation = validateRequest(monitorMultipleSchema, body);
    if (!validation.isValid) {
      return Response.json(
        createApiResponse(null, 'Validation failed', 'VALIDATION_ERROR'),
        { status: 400 }
      );
    }

    const { metrics } = validation.data;

    const monitoringData: MonitoringData[] = metrics.map((metric: any) => ({
      propertyId: metric.property_id,
      serviceType: metric.service_type,
      metricName: metric.metric_name,
      value: metric.value,
      unit: metric.unit,
      timestamp: metric.timestamp ? new Date(metric.timestamp) : new Date(),
    }));

    // Monitor all metrics
    const allAlerts = await thresholdMonitor.monitorMetrics(monitoringData);

    return Response.json(
      createApiResponse({
        alerts: allAlerts.map(alert => ({
          id: alert.id,
          property_id: alert.propertyId,
          property_name: alert.propertyName,
          service_type: alert.serviceType,
          metric_name: alert.metricName,
          current_value: alert.currentValue,
          threshold_type: alert.thresholdType,
          threshold_value: alert.thresholdValue,
          message: alert.message,
          severity: alert.severity,
          timestamp: alert.timestamp,
          is_resolved: alert.isResolved,
        })),
        metrics_count: metrics.length,
        alerts_generated: allAlerts.length,
        monitored_at: new Date(),
      }),
      {
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to monitor metrics');
  }
}

async function getActiveAlertsHandler(request: NextRequest, context: any, currentUser: any) {
  try {
    const url = new URL(request.url);
    const propertyId = url.searchParams.get('property_id');

    const alerts = await thresholdMonitor.getActiveAlerts(propertyId || undefined);

    return Response.json(
      createApiResponse({
        alerts: alerts.map(alert => ({
          id: alert.id,
          property_id: alert.propertyId,
          property_name: alert.propertyName,
          service_type: alert.serviceType,
          metric_name: alert.metricName,
          current_value: alert.currentValue,
          threshold_type: alert.thresholdType,
          threshold_value: alert.thresholdValue,
          message: alert.message,
          severity: alert.severity,
          timestamp: alert.timestamp,
          is_resolved: alert.isResolved,
        })),
        count: alerts.length,
      }),
      {
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to get active alerts');
  }
}

// POST /api/monitoring - Monitor a single metric
export const POST = requireAuth(monitorMetricHandler);

// GET /api/monitoring - Get active alerts
export const GET = requireAuth(getActiveAlertsHandler);

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: corsHeaders(),
  });
}
