import '../../../shared/models/attendance.dart';
import '../data/attendance_api_service.dart';

abstract class AttendanceRepository {
  // Site Attendance
  Future<List<AttendanceRecord>> getSiteAttendance({
    String? propertyId,
    String? date,
    String? startDate,
    String? endDate,
    String? userId,
    int? page,
    int? limit,
  });

  Future<List<AttendanceRecord>> submitSiteAttendance(SubmitSiteAttendanceRequest request);

  Future<AttendanceRecord> getSiteAttendanceById(String id);

  Future<AttendanceRecord> updateSiteAttendance(String id, UpdateSiteAttendanceRequest request);

  Future<void> deleteSiteAttendance(String id);

  // Office Attendance
  Future<List<AttendanceRecord>> getOfficeAttendance({
    String? officeId,
    String? date,
    String? startDate,
    String? endDate,
    String? userId,
    int? page,
    int? limit,
  });

  Future<List<AttendanceRecord>> submitOfficeAttendance(SubmitOfficeAttendanceRequest request);

  Future<AttendanceRecord> getOfficeAttendanceById(String id);

  Future<AttendanceRecord> updateOfficeAttendance(String id, UpdateOfficeAttendanceRequest request);

  Future<void> deleteOfficeAttendance(String id);

  // Reports
  Future<AttendanceSummary> getAttendanceSummary({
    String? startDate,
    String? endDate,
    String? propertyId,
    String? officeId,
  });

  Future<UserAttendanceReport> getUserAttendanceReport(
    String userId, {
    String? startDate,
    String? endDate,
  });

  // Additional methods expected by providers
  Future<List<AttendanceRecord>> getAllAttendanceRecords();

  Future<List<AttendanceRecord>> getAttendanceByDateRange(DateTime startDate, DateTime endDate);

  Future<AttendanceRecord> createAttendanceRecord(AttendanceRecord record);

  Future<AttendanceRecord> updateAttendanceRecord(AttendanceRecord record);

  Future<void> deleteAttendanceRecord(String recordId);

  Future<void> markPresent(String workerId, DateTime date, {
    DateTime? checkInTime,
    String? notes,
    String attendanceType = 'site',
  });

  Future<void> markAbsent(String workerId, DateTime date, {String? reason});

  Future<void> checkOut(String recordId, DateTime checkOutTime);
}
