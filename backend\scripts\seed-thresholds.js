const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

const defaultThresholds = [
  // Fuel monitoring thresholds
  {
    serviceType: 'fuel',
    metricName: 'fuel_level',
    unit: 'liters',
    warningThreshold: 100.0,
    criticalThreshold: 50.0,
    isActive: true,
  },
  {
    serviceType: 'fuel',
    metricName: 'fuel_percentage',
    unit: '%',
    warningThreshold: 25.0,
    criticalThreshold: 10.0,
    isActive: true,
  },
  {
    serviceType: 'fuel',
    metricName: 'backup_hours',
    unit: 'hours',
    warningThreshold: 8.0,
    criticalThreshold: 4.0,
    isActive: true,
  },

  // Attendance monitoring thresholds
  {
    serviceType: 'attendance',
    metricName: 'attendance_percentage',
    unit: '%',
    warningThreshold: 80.0,
    criticalThreshold: 60.0,
    isActive: true,
  },
  {
    serviceType: 'attendance',
    metricName: 'daily_attendance',
    unit: 'count',
    warningThreshold: 5.0,
    criticalThreshold: 3.0,
    isActive: true,
  },

  // Maintenance monitoring thresholds
  {
    serviceType: 'maintenance',
    metricName: 'open_issues',
    unit: 'count',
    warningThreshold: 10.0,
    criticalThreshold: 20.0,
    isActive: true,
  },
  {
    serviceType: 'maintenance',
    metricName: 'overdue_issues',
    unit: 'count',
    warningThreshold: 3.0,
    criticalThreshold: 5.0,
    isActive: true,
  },
  {
    serviceType: 'maintenance',
    metricName: 'response_time',
    unit: 'hours',
    warningThreshold: 24.0,
    criticalThreshold: 48.0,
    isActive: true,
  },

  // Security monitoring thresholds
  {
    serviceType: 'security',
    metricName: 'security_incidents',
    unit: 'count',
    warningThreshold: 1.0,
    criticalThreshold: 3.0,
    isActive: true,
  },
  {
    serviceType: 'security',
    metricName: 'access_violations',
    unit: 'count',
    warningThreshold: 2.0,
    criticalThreshold: 5.0,
    isActive: true,
  },

  // Electrical system monitoring thresholds
  {
    serviceType: 'electricity',
    metricName: 'power_outage_duration',
    unit: 'hours',
    warningThreshold: 2.0,
    criticalThreshold: 6.0,
    isActive: true,
  },
  {
    serviceType: 'electricity',
    metricName: 'generator_runtime',
    unit: 'hours',
    warningThreshold: 8.0,
    criticalThreshold: 12.0,
    isActive: true,
  },
  {
    serviceType: 'electricity',
    metricName: 'battery_level',
    unit: '%',
    warningThreshold: 30.0,
    criticalThreshold: 15.0,
    isActive: true,
  },

  // System performance thresholds
  {
    serviceType: 'system',
    metricName: 'efficiency_percentage',
    unit: '%',
    warningThreshold: 80.0,
    criticalThreshold: 60.0,
    isActive: true,
  },
  {
    serviceType: 'system',
    metricName: 'downtime_hours',
    unit: 'hours',
    warningThreshold: 1.0,
    criticalThreshold: 4.0,
    isActive: true,
  },
  {
    serviceType: 'system',
    metricName: 'failure_rate',
    unit: '%',
    warningThreshold: 5.0,
    criticalThreshold: 10.0,
    isActive: true,
  },
];

async function seedThresholds() {
  try {
    console.log('Seeding default threshold configurations...');

    for (const threshold of defaultThresholds) {
      await prisma.thresholdConfig.upsert({
        where: {
          serviceType_metricName: {
            serviceType: threshold.serviceType,
            metricName: threshold.metricName,
          },
        },
        update: {
          unit: threshold.unit,
          warningThreshold: threshold.warningThreshold,
          criticalThreshold: threshold.criticalThreshold,
          isActive: threshold.isActive,
        },
        create: threshold,
      });
      console.log(`✓ Threshold: ${threshold.serviceType}.${threshold.metricName}`);
    }

    console.log('✅ Threshold configurations seeded successfully!');
    console.log(`📊 Created ${defaultThresholds.length} threshold configurations`);

    // Display summary by service type
    const summary = defaultThresholds.reduce((acc, threshold) => {
      acc[threshold.serviceType] = (acc[threshold.serviceType] || 0) + 1;
      return acc;
    }, {});

    console.log('\n📈 Threshold Summary by Service Type:');
    Object.entries(summary).forEach(([serviceType, count]) => {
      console.log(`  ${serviceType}: ${count} thresholds`);
    });

  } catch (error) {
    console.error('❌ Error seeding thresholds:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seed function
if (require.main === module) {
  seedThresholds()
    .then(() => {
      console.log('🎉 Threshold seeding completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Threshold seeding failed:', error);
      process.exit(1);
    });
}

module.exports = { seedThresholds };
