// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'fuel_api_service.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CreateFuelLogRequest _$CreateFuelLogRequestFromJson(
        Map<String, dynamic> json) =>
    CreateFuelLogRequest(
      fuelLevelLiters: (json['fuel_level_liters'] as num).toDouble(),
      consumptionRate: (json['consumption_rate'] as num?)?.toDouble(),
      runtimeHours: (json['runtime_hours'] as num?)?.toDouble(),
      efficiencyPercentage: (json['efficiency_percentage'] as num?)?.toDouble(),
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$CreateFuelLogRequestToJson(
        CreateFuelLogRequest instance) =>
    <String, dynamic>{
      'fuel_level_liters': instance.fuelLevelLiters,
      'consumption_rate': instance.consumptionRate,
      'runtime_hours': instance.runtimeHours,
      'efficiency_percentage': instance.efficiencyPercentage,
      'notes': instance.notes,
    };

UpdateFuelLogRequest _$UpdateFuelLogRequestFromJson(
        Map<String, dynamic> json) =>
    UpdateFuelLogRequest(
      fuelLevelLiters: (json['fuel_level_liters'] as num?)?.toDouble(),
      consumptionRate: (json['consumption_rate'] as num?)?.toDouble(),
      runtimeHours: (json['runtime_hours'] as num?)?.toDouble(),
      efficiencyPercentage: (json['efficiency_percentage'] as num?)?.toDouble(),
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$UpdateFuelLogRequestToJson(
        UpdateFuelLogRequest instance) =>
    <String, dynamic>{
      'fuel_level_liters': instance.fuelLevelLiters,
      'consumption_rate': instance.consumptionRate,
      'runtime_hours': instance.runtimeHours,
      'efficiency_percentage': instance.efficiencyPercentage,
      'notes': instance.notes,
    };

// **************************************************************************
// RetrofitGenerator
// **************************************************************************

// ignore_for_file: unnecessary_brace_in_string_interps,no_leading_underscores_for_local_identifiers,unused_element,unnecessary_string_interpolations

class _FuelApiService implements FuelApiService {
  _FuelApiService(
    this._dio, {
    this.baseUrl,
    this.errorLogger,
  });

  final Dio _dio;

  String? baseUrl;

  final ParseErrorLogger? errorLogger;

  @override
  Future<ApiResponse<List<GeneratorFuelLog>>> getFuelLogs(
      String propertyId) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options =
        _setStreamType<ApiResponse<List<GeneratorFuelLog>>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/generator-fuel/${propertyId}',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(
                baseUrl: _combineBaseUrls(
              _dio.options.baseUrl,
              baseUrl,
            )));
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<List<GeneratorFuelLog>> _value;
    try {
      _value = ApiResponse<List<GeneratorFuelLog>>.fromJson(
        _result.data!,
        (json) => json is List<dynamic>
            ? json
                .map<GeneratorFuelLog>(
                    (i) => GeneratorFuelLog.fromJson(i as Map<String, dynamic>))
                .toList()
            : List.empty(),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<HttpResponse<dynamic>> createFuelLog(
    String propertyId,
    CreateFuelLogRequest request,
  ) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(request.toJson());
    final _options = _setStreamType<HttpResponse<dynamic>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/api/generator-fuel/${propertyId}',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch(_options);
    final _value = _result.data;
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<ApiResponse<GeneratorFuelLog>> getFuelLogById(String id) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<ApiResponse<GeneratorFuelLog>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/api/generator-fuel/logs/${id}',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<GeneratorFuelLog> _value;
    try {
      _value = ApiResponse<GeneratorFuelLog>.fromJson(
        _result.data!,
        (json) => GeneratorFuelLog.fromJson(json as Map<String, dynamic>),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<GeneratorFuelLog>> updateFuelLog(
    String id,
    UpdateFuelLogRequest request,
  ) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(request.toJson());
    final _options = _setStreamType<ApiResponse<GeneratorFuelLog>>(Options(
      method: 'PUT',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/api/generator-fuel/logs/${id}',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<GeneratorFuelLog> _value;
    try {
      _value = ApiResponse<GeneratorFuelLog>.fromJson(
        _result.data!,
        (json) => GeneratorFuelLog.fromJson(json as Map<String, dynamic>),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<VoidResponse>> deleteFuelLog(String id) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<ApiResponse<VoidResponse>>(Options(
      method: 'DELETE',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/api/generator-fuel/logs/${id}',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<VoidResponse> _value;
    try {
      _value = ApiResponse<VoidResponse>.fromJson(
        _result.data!,
        (json) => VoidResponse.fromJson(json as Map<String, dynamic>),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  RequestOptions _setStreamType<T>(RequestOptions requestOptions) {
    if (T != dynamic &&
        !(requestOptions.responseType == ResponseType.bytes ||
            requestOptions.responseType == ResponseType.stream)) {
      if (T == String) {
        requestOptions.responseType = ResponseType.plain;
      } else {
        requestOptions.responseType = ResponseType.json;
      }
    }
    return requestOptions;
  }

  String _combineBaseUrls(
    String dioBaseUrl,
    String? baseUrl,
  ) {
    if (baseUrl == null || baseUrl.trim().isEmpty) {
      return dioBaseUrl;
    }

    final url = Uri.parse(baseUrl);

    if (url.isAbsolute) {
      return url.toString();
    }

    return Uri.parse(dioBaseUrl).resolveUri(url).toString();
  }
}
