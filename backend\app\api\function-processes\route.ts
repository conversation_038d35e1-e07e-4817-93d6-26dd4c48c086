import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth, requireRole } from '@/lib/auth';
import { createApiResponse, getQueryParams, getRequestBody, handleError, corsHeaders } from '@/lib/utils';
import { validateRequest } from '@/lib/validation';
import Jo<PERSON> from 'joi';

const createFunctionProcessSchema = Joi.object({
  name: Joi.string().min(2).required(),
  description: Joi.string().optional(),
  category: Joi.string().required(),
  input_parameters: Joi.object().optional(),
  output_parameters: Joi.object().optional(),
  execution_frequency: Joi.string().valid('manual', 'hourly', 'daily', 'weekly', 'monthly').default('manual'),
  status: Joi.string().valid('ACTIVE', 'INACTIVE', 'MAINTENANCE').default('ACTIVE'),
});

async function getFunctionProcessesHandler(request: NextRequest, context: any, currentUser: any) {
  try {
    const params = getQueryParams(request);
    const { category, status, execution_frequency } = params;

    // Build where clause
    const where: any = {};
    
    if (category) {
      where.category = category;
    }
    
    if (status) {
      where.status = status.toUpperCase();
    }
    
    if (execution_frequency) {
      where.executionFrequency = execution_frequency;
    }

    // Get function processes with execution logs count
    const functionProcesses = await prisma.functionProcess.findMany({
      where,
      include: {
        _count: {
          select: {
            functionProcessLogs: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    // Transform data to match API response format
    const transformedProcesses = functionProcesses.map(process => ({
      id: process.id,
      name: process.name,
      description: process.description,
      category: process.category,
      input_parameters: process.inputParameters,
      output_parameters: process.outputParameters,
      execution_frequency: process.executionFrequency,
      status: process.status,
      execution_count: process._count.functionProcessLogs,
      created_at: process.createdAt,
      updated_at: process.updatedAt,
    }));

    return Response.json(
      createApiResponse(transformedProcesses),
      { 
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to fetch function processes');
  }
}

async function createFunctionProcessHandler(request: NextRequest, context: any, currentUser: any) {
  try {
    const body = await getRequestBody(request);
    
    // Validate request body
    const validation = validateRequest(createFunctionProcessSchema, body);
    if (!validation.isValid) {
      return Response.json(
        createApiResponse(null, 'Validation failed', 'VALIDATION_ERROR'),
        { status: 400 }
      );
    }

    const { 
      name, 
      description, 
      category, 
      input_parameters, 
      output_parameters, 
      execution_frequency, 
      status 
    } = validation.data;

    // Check if function process with same name already exists
    const existingProcess = await prisma.functionProcess.findFirst({
      where: { name },
    });

    if (existingProcess) {
      return Response.json(
        createApiResponse(null, 'Function process with this name already exists', 'DUPLICATE_NAME'),
        { status: 409 }
      );
    }

    // Create function process
    const functionProcess = await prisma.functionProcess.create({
      data: {
        name,
        description,
        category,
        inputParameters: input_parameters || {},
        outputParameters: output_parameters || {},
        executionFrequency: execution_frequency,
        status,
      },
    });

    return Response.json(
      createApiResponse({
        message: 'Function process created successfully',
        function_process: {
          id: functionProcess.id,
          name: functionProcess.name,
          description: functionProcess.description,
          category: functionProcess.category,
          input_parameters: functionProcess.inputParameters,
          output_parameters: functionProcess.outputParameters,
          execution_frequency: functionProcess.executionFrequency,
          status: functionProcess.status,
          created_at: functionProcess.createdAt,
          updated_at: functionProcess.updatedAt,
        },
      }),
      { 
        status: 201,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to create function process');
  }
}

export const GET = requireAuth(getFunctionProcessesHandler);
export const POST = requireRole(['admin'])(createFunctionProcessHandler);

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: corsHeaders(),
  });
}
