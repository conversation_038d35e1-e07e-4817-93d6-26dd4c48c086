import 'package:json_annotation/json_annotation.dart';

part 'user_permission.g.dart';

@JsonSerializable()
class UserPermission {
  final String id;
  final String name;
  final String resource;
  final String action;
  @JsonKey(name: 'from_role')
  final String fromRole;

  const UserPermission({
    required this.id,
    required this.name,
    required this.resource,
    required this.action,
    required this.fromRole,
  });

  factory UserPermission.fromJson(Map<String, dynamic> json) => _$UserPermissionFromJson(json);
  Map<String, dynamic> toJson() => _$UserPermissionToJson(this);

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserPermission &&
        other.id == id &&
        other.name == name &&
        other.resource == resource &&
        other.action == action &&
        other.fromRole == fromRole;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        name.hashCode ^
        resource.hashCode ^
        action.hashCode ^
        fromRole.hashCode;
  }

  @override
  String toString() {
    return 'UserPermission(id: $id, name: $name, resource: $resource, action: $action, fromRole: $fromRole)';
  }
}
