import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../shared/models/attendance.dart';
import '../providers/attendance_providers.dart';

class CheckInOutWidget extends ConsumerStatefulWidget {
  final String? propertyId;
  final VoidCallback? onCheckInOut;

  const CheckInOutWidget({
    super.key,
    this.propertyId,
    this.onCheckInOut,
  });

  @override
  ConsumerState<CheckInOutWidget> createState() => _CheckInOutWidgetState();
}

class _CheckInOutWidgetState extends ConsumerState<CheckInOutWidget> {
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    final attendanceAsync = ref.watch(attendanceRecordsProvider);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Check In/Out',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            attendanceAsync.when(
              data: (records) => _buildCheckInOutButtons(records),
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => Text('Error: $error'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCheckInOutButtons(List<AttendanceRecord> records) {
    final today = DateTime.now();
    final todayRecords = records.where((record) =>
        record.date.year == today.year &&
        record.date.month == today.month &&
        record.date.day == today.day).toList();

    final isCheckedIn = todayRecords.any((record) =>
        record.checkInTime != null && record.checkOutTime == null);

    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: _isLoading ? null : () => _handleCheckIn(),
            icon: const Icon(Icons.login),
            label: const Text('Check In'),
            style: ElevatedButton.styleFrom(
              backgroundColor: isCheckedIn ? Colors.grey : Colors.green,
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ElevatedButton.icon(
            onPressed: _isLoading || !isCheckedIn ? null : () => _handleCheckOut(),
            icon: const Icon(Icons.logout),
            label: const Text('Check Out'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _handleCheckIn() async {
    setState(() => _isLoading = true);
    try {
      // Create a new attendance record for check-in
      final record = AttendanceRecord.createSiteAttendance(
        propertyId: widget.propertyId ?? '',
        userId: 'current_user', // This should come from auth provider
        workerName: 'Current User', // This should come from auth provider
        date: DateTime.now(),
        checkInTime: DateTime.now().toString(),
        status: 'present',
      );

      await ref.read(attendanceRecordsProvider.notifier).createAttendanceRecord(record);
      widget.onCheckInOut?.call();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Check-in failed: $e')),
        );
      }
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }

  Future<void> _handleCheckOut() async {
    setState(() => _isLoading = true);
    try {
      // Find today's record and update with check-out time
      final attendanceAsync = ref.read(attendanceRecordsProvider);
      attendanceAsync.whenData((records) async {
        final today = DateTime.now();
        final todayRecord = records.firstWhere(
          (record) =>
              record.date.year == today.year &&
              record.date.month == today.month &&
              record.date.day == today.day &&
              record.checkOutTime == null,
          orElse: () => throw Exception('No check-in record found for today'),
        );

        final updatedRecord = todayRecord.copyWith(
          checkOutTime: DateTime.now().toString(),
        );

        await ref.read(attendanceRecordsProvider.notifier).updateAttendanceRecord(updatedRecord);
      });

      widget.onCheckInOut?.call();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Check-out failed: $e')),
        );
      }
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }
}
