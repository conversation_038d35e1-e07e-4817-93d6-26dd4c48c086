import 'package:flutter/material.dart';
import '../../../../shared/models/user.dart';

class UserCard extends StatelessWidget {
  final User user;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final VoidCallback? onActivate;
  final VoidCallback? onDeactivate;
  final VoidCallback? onApprove;
  final VoidCallback? onReject;
  final VoidCallback? onManageRoles;

  const UserCard({
    super.key,
    required this.user,
    this.onEdit,
    this.onDelete,
    this.onActivate,
    this.onDeactivate,
    this.onApprove,
    this.onReject,
    this.onManageRoles,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // User Header
            Row(
              children: [
                // Avatar
                CircleAvatar(
                  radius: 24,
                  backgroundColor: _getUserStatusColor().withValues(alpha: 0.1),
                  child: Text(
                    _getInitials(),
                    style: TextStyle(
                      color: _getUserStatusColor(),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        user.fullName,
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        user.email,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                      if (user.phone != null) ...[
                        const SizedBox(height: 2),
                        Text(
                          user.phone!,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                // Status Badge
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _getUserStatusColor().withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: _getUserStatusColor()),
                  ),
                  child: Text(
                    _getUserStatusText(),
                    style: TextStyle(
                      color: _getUserStatusColor(),
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // User Details
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem(
                    'Primary Role',
                    user.primaryRole ?? 'No role assigned',
                    Icons.person_outline,
                  ),
                ),
                Expanded(
                  child: _buildInfoItem(
                    'Created',
                    _formatDate(user.createdAt),
                    Icons.calendar_today,
                  ),
                ),
              ],
            ),
            
            if (user.username != null || user.mobileNumber != null) ...[
              const SizedBox(height: 12),
              Row(
                children: [
                  if (user.username != null)
                    Expanded(
                      child: _buildInfoItem(
                        'Username',
                        user.username!,
                        Icons.account_circle,
                      ),
                    ),
                  if (user.mobileNumber != null)
                    Expanded(
                      child: _buildInfoItem(
                        'Mobile',
                        user.mobileNumber!,
                        Icons.phone,
                      ),
                    ),
                ],
              ),
            ],
            
            const SizedBox(height: 16),
            
            // Action Buttons
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                if (user.approvalStatus == 'pending' && onApprove != null) ...[
                  _buildActionButton(
                    'Approve',
                    Icons.check,
                    Colors.green,
                    onApprove!,
                  ),
                ],
                if (user.approvalStatus == 'pending' && onReject != null) ...[
                  _buildActionButton(
                    'Reject',
                    Icons.close,
                    Colors.red,
                    onReject!,
                  ),
                ],
                if (user.isActive && onDeactivate != null) ...[
                  _buildActionButton(
                    'Deactivate',
                    Icons.block,
                    Colors.orange,
                    onDeactivate!,
                  ),
                ],
                if (!user.isActive && onActivate != null) ...[
                  _buildActionButton(
                    'Activate',
                    Icons.check_circle,
                    Colors.green,
                    onActivate!,
                  ),
                ],
                if (onManageRoles != null) ...[
                  _buildActionButton(
                    'Roles',
                    Icons.security,
                    Colors.blue,
                    onManageRoles!,
                  ),
                ],
                if (onEdit != null) ...[
                  _buildActionButton(
                    'Edit',
                    Icons.edit,
                    Colors.blue,
                    onEdit!,
                  ),
                ],
                if (onDelete != null) ...[
                  _buildActionButton(
                    'Delete',
                    Icons.delete,
                    Colors.red,
                    onDelete!,
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoItem(String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(icon, size: 16, color: Colors.grey[600]),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: const TextStyle(
                  fontSize: 12,
                  color: Colors.grey,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                value,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildActionButton(String label, IconData icon, Color color, VoidCallback onPressed) {
    return OutlinedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 16),
      label: Text(label),
      style: OutlinedButton.styleFrom(
        foregroundColor: color,
        side: BorderSide(color: color),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
    );
  }

  String _getInitials() {
    final names = user.fullName.split(' ');
    if (names.length >= 2) {
      return '${names[0][0]}${names[1][0]}'.toUpperCase();
    } else if (names.isNotEmpty) {
      return names[0][0].toUpperCase();
    }
    return 'U';
  }

  Color _getUserStatusColor() {
    if (user.approvalStatus == 'pending') return Colors.orange;
    if (!user.isActive) return Colors.red;
    return Colors.green;
  }

  String _getUserStatusText() {
    if (user.approvalStatus == 'pending') return 'Pending';
    if (!user.isActive) return 'Inactive';
    return 'Active';
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
