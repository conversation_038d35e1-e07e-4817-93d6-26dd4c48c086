// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CreateFuelLogRequest _$CreateFuelLogRequestFromJson(
        Map<String, dynamic> json) =>
    CreateFuelLogRequest(
      fuelLevelLiters: (json['fuel_level_liters'] as num).toDouble(),
      consumptionRate: (json['consumption_rate'] as num?)?.toDouble(),
      runtimeHours: (json['runtime_hours'] as num?)?.toDouble(),
      efficiencyPercentage: (json['efficiency_percentage'] as num?)?.toDouble(),
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$CreateFuelLogRequestToJson(
        CreateFuelLogRequest instance) =>
    <String, dynamic>{
      'fuel_level_liters': instance.fuelLevelLiters,
      'consumption_rate': instance.consumptionRate,
      'runtime_hours': instance.runtimeHours,
      'efficiency_percentage': instance.efficiencyPercentage,
      'notes': instance.notes,
    };

UpdateFuelLogRequest _$UpdateFuelLogRequestFromJson(
        Map<String, dynamic> json) =>
    UpdateFuelLogRequest(
      fuelLevelLiters: (json['fuel_level_liters'] as num?)?.toDouble(),
      consumptionRate: (json['consumption_rate'] as num?)?.toDouble(),
      runtimeHours: (json['runtime_hours'] as num?)?.toDouble(),
      efficiencyPercentage: (json['efficiency_percentage'] as num?)?.toDouble(),
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$UpdateFuelLogRequestToJson(
        UpdateFuelLogRequest instance) =>
    <String, dynamic>{
      'fuel_level_liters': instance.fuelLevelLiters,
      'consumption_rate': instance.consumptionRate,
      'runtime_hours': instance.runtimeHours,
      'efficiency_percentage': instance.efficiencyPercentage,
      'notes': instance.notes,
    };
