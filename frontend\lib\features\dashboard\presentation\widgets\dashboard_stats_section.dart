import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../shared/widgets/stat_card.dart';
import '../../data/dashboard_providers.dart';

class DashboardStatsSection extends ConsumerWidget {
  const DashboardStatsSection({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final dashboardStatsAsync = ref.watch(dashboardStatsProvider);

    return dashboardStatsAsync.when(
      data: (stats) => _buildStatsGrid(context, stats),
      loading: () => _buildLoadingGrid(),
      error: (error, stack) => _buildErrorWidget(error),
    );
  }

  Widget _buildStatsGrid(BuildContext context, DashboardStats stats) {
    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      childAspectRatio: 1.5,
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      children: [
        StatCard(
          title: 'Total Properties',
          value: stats.totalProperties.toString(),
          icon: Icons.business,
          color: Colors.blue,
        ),
        StatCard(
          title: 'Active Issues',
          value: stats.activeIssues.toString(),
          icon: Icons.warning,
          color: Colors.orange,
        ),
        StatCard(
          title: 'Attendance Rate',
          value: '${stats.attendanceRate.toStringAsFixed(1)}%',
          icon: Icons.people,
          color: Colors.green,
        ),
        StatCard(
          title: 'Fuel Efficiency',
          value: '${stats.fuelEfficiency.toStringAsFixed(1)}L/h',
          icon: Icons.local_gas_station,
          color: Colors.purple,
        ),
      ],
    );
  }

  Widget _buildLoadingGrid() {
    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      childAspectRatio: 1.5,
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      children: List.generate(4, (index) => _buildLoadingCard()),
    );
  }

  Widget _buildLoadingCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: 100,
              height: 16,
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            const SizedBox(height: 8),
            Container(
              width: 60,
              height: 24,
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            const Spacer(),
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(20),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorWidget(Object error) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            const Icon(
              Icons.error_outline,
              size: 48,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'Error loading dashboard stats',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.red.shade700,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              error.toString(),
              style: const TextStyle(fontSize: 12),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

// Dashboard stats model
class DashboardStats {
  final int totalProperties;
  final int activeIssues;
  final double attendanceRate;
  final double fuelEfficiency;
  final double propertiesTrend;
  final double issuesTrend;
  final double attendanceTrend;
  final double fuelTrend;

  DashboardStats({
    required this.totalProperties,
    required this.activeIssues,
    required this.attendanceRate,
    required this.fuelEfficiency,
    required this.propertiesTrend,
    required this.issuesTrend,
    required this.attendanceTrend,
    required this.fuelTrend,
  });

  factory DashboardStats.fromJson(Map<String, dynamic> json) {
    return DashboardStats(
      totalProperties: json['totalProperties'] ?? 0,
      activeIssues: json['activeIssues'] ?? 0,
      attendanceRate: (json['attendanceRate'] ?? 0.0).toDouble(),
      fuelEfficiency: (json['fuelEfficiency'] ?? 0.0).toDouble(),
      propertiesTrend: (json['propertiesTrend'] ?? 0.0).toDouble(),
      issuesTrend: (json['issuesTrend'] ?? 0.0).toDouble(),
      attendanceTrend: (json['attendanceTrend'] ?? 0.0).toDouble(),
      fuelTrend: (json['fuelTrend'] ?? 0.0).toDouble(),
    );
  }
}
