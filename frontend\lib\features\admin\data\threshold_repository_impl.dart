import 'package:dio/dio.dart';
import '../domain/threshold_repository.dart';
import 'threshold_api_service.dart';

class ThresholdRepositoryImpl implements ThresholdRepository {
  final ThresholdApiService _apiService;

  ThresholdRepositoryImpl(this._apiService);

  @override
  Future<List<ThresholdConfig>> getThresholds({
    String? functionalArea,
    String? propertyType,
    String? metricName,
  }) async {
    try {
      final response = await _apiService.getThresholds(
        functionalArea: functionalArea,
        propertyType: propertyType,
        metricName: metricName,
      );

      if (response.success && response.data != null) {
        return response.data!;
      } else {
        throw Exception(response.error ?? 'Failed to fetch thresholds');
      }
    } on DioException catch (e) {
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      throw Exception('Failed to fetch thresholds: $e');
    }
  }

  @override
  Future<ThresholdConfig> getThresholdById(String id) async {
    try {
      final response = await _apiService.getThresholdById(id);

      if (response.success && response.data != null) {
        return response.data!;
      } else {
        throw Exception(response.error ?? 'Failed to fetch threshold');
      }
    } on DioException catch (e) {
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      throw Exception('Failed to fetch threshold: $e');
    }
  }

  @override
  Future<ThresholdConfig> createThreshold(CreateThresholdRequest request) async {
    try {
      final response = await _apiService.createThreshold(request);

      if (response.success && response.data != null) {
        return response.data!;
      } else {
        throw Exception(response.error ?? 'Failed to create threshold');
      }
    } on DioException catch (e) {
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      throw Exception('Failed to create threshold: $e');
    }
  }

  @override
  Future<ThresholdConfig> updateThreshold(String id, UpdateThresholdRequest request) async {
    try {
      final response = await _apiService.updateThreshold(id, request);

      if (response.success && response.data != null) {
        return response.data!;
      } else {
        throw Exception(response.error ?? 'Failed to update threshold');
      }
    } on DioException catch (e) {
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      throw Exception('Failed to update threshold: $e');
    }
  }

  @override
  Future<void> deleteThreshold(String id) async {
    try {
      final response = await _apiService.deleteThreshold(id);

      if (!response.success) {
        throw Exception(response.error ?? 'Failed to delete threshold');
      }
    } on DioException catch (e) {
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      throw Exception('Failed to delete threshold: $e');
    }
  }

  @override
  Future<List<ThresholdConfig>> bulkCreateThresholds(BulkCreateThresholdsRequest request) async {
    try {
      final response = await _apiService.bulkCreateThresholds(request);

      if (response.success && response.data != null) {
        return response.data!;
      } else {
        throw Exception(response.error ?? 'Failed to create thresholds');
      }
    } on DioException catch (e) {
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      throw Exception('Failed to create thresholds: $e');
    }
  }

  @override
  Future<List<ThresholdConfig>> bulkUpdateThresholds(BulkUpdateThresholdsRequest request) async {
    try {
      final response = await _apiService.bulkUpdateThresholds(request);

      if (response.success && response.data != null) {
        return response.data!;
      } else {
        throw Exception(response.error ?? 'Failed to update thresholds');
      }
    } on DioException catch (e) {
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      throw Exception('Failed to update thresholds: $e');
    }
  }

  @override
  Future<void> updateThresholds(List<ThresholdConfig> thresholds) async {
    try {
      // Update thresholds one by one
      for (final threshold in thresholds) {
        final request = UpdateThresholdRequest(
          serviceType: threshold.serviceType,
          metricName: threshold.metricName,
          warningThreshold: threshold.warningThreshold,
          criticalThreshold: threshold.criticalThreshold,
          unit: threshold.unit,
          description: threshold.description,
          isActive: threshold.isActive,
        );
        await updateThreshold(threshold.id, request);
      }
    } catch (e) {
      throw Exception('Failed to update thresholds: $e');
    }
  }

  @override
  Future<void> resetThresholds() async {
    try {
      // Get all thresholds and reset them to default values
      final thresholds = await getThresholds();
      for (final threshold in thresholds) {
        await deleteThreshold(threshold.id);
      }
    } catch (e) {
      throw Exception('Failed to reset thresholds: $e');
    }
  }
}
