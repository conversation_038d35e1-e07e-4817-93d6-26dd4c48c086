import { NextRequest, NextResponse } from 'next/server';
import { requireAuth } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { createApiResponse, getQueryParams, handleError, corsHeaders } from '@/lib/utils';

async function getAttendanceHandler(
  request: NextRequest,
  currentUser: any
) {
  try {
    const params = getQueryParams(request);
    const { 
      property_id, 
      date, 
      start_date, 
      end_date, 
      user_id, 
      page = '1', 
      limit = '50' 
    } = params;
    
    // Build where clause
    const where: any = {};
    
    if (property_id) {
      where.propertyId = property_id;
    }
    
    if (user_id) {
      where.userId = user_id;
    }
    
    if (date) {
      const targetDate = new Date(date);
      where.date = {
        gte: new Date(targetDate.getFullYear(), targetDate.getMonth(), targetDate.getDate()),
        lt: new Date(targetDate.getFullYear(), targetDate.getMonth(), targetDate.getDate() + 1),
      };
    } else if (start_date && end_date) {
      where.date = {
        gte: new Date(start_date),
        lte: new Date(end_date),
      };
    }
    
    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    
    // Get attendance records
    const attendance = await prisma.propertyAttendance.findMany({
      where,
      include: {
        property: {
          select: {
            id: true,
            name: true,
            type: true,
          },
        },
        user: {
          select: {
            id: true,
            fullName: true,
            email: true,
          },
        },
        recorder: {
          select: {
            id: true,
            fullName: true,
          },
        },
      },
      orderBy: [
        { date: 'desc' },
        { checkInTime: 'desc' },
      ],
      skip: (pageNum - 1) * limitNum,
      take: limitNum,
    });
    
    // Get total count for pagination
    const totalCount = await prisma.propertyAttendance.count({ where });
    
    // Transform to match Flutter model expectations
    const transformedAttendance = attendance.map(record => ({
      id: record.id,
      property_id: record.propertyId,
      office_id: null, // Properties don't have office_id in the new schema
      user_id: record.userId,
      worker_name: record.user.fullName,
      worker_role: record.user.email, // You might want to add role field to user model
      date: record.date.toISOString().split('T')[0],
      check_in_time: record.checkInTime,
      check_out_time: record.checkOutTime,
      hours_worked: record.hoursWorked ? parseFloat(record.hoursWorked.toString()) : null,
      notes: record.notes,
      status: record.status || 'present', // Default to present if no status
      recorded_by: record.recordedBy,
      created_at: record.createdAt.toISOString(),
      // Additional property info
      property: {
        id: record.property.id,
        name: record.property.name,
        type: record.property.type,
      },
    }));
    
    return Response.json(
      createApiResponse({
        attendance: transformedAttendance,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total: totalCount,
          totalPages: Math.ceil(totalCount / limitNum),
        },
      }),
      { 
        status: 200,
        headers: corsHeaders(),
      }
    );
    
  } catch (error) {
    return handleError(error, 'Failed to fetch attendance records');
  }
}

export const GET = requireAuth(getAttendanceHandler);

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: corsHeaders(),
  });
}
