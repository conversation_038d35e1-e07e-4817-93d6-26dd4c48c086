import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';
import { validateRequest } from '@/lib/validation';
import { createApiResponse, getRequestBody, handleError, corsHeaders } from '@/lib/utils';
import Joi from 'joi';

const updateFuelLogSchema = Joi.object({
  fuel_level_liters: Joi.number().positive().optional(),
  consumption_rate: Joi.number().positive().optional(),
  runtime_hours: Joi.number().positive().optional(),
  efficiency_percentage: Joi.number().min(0).max(100).optional(),
  last_maintenance: Joi.date().optional(),
  next_maintenance: Joi.date().optional(),
  notes: Joi.string().optional(),
});

async function getFuelLogHandler(
  request: NextRequest, 
  context: { params: { logId: string } }, 
  currentUser: any
) {
  try {
    const { logId: id } = context.params;

    const fuelLog = await prisma.generatorFuelLog.findUnique({
      where: { id },
      include: {
        property: {
          select: {
            id: true,
            name: true,
          },
        },
        recorder: {
          select: {
            id: true,
            fullName: true,
            email: true,
          },
        },
      },
    });

    if (!fuelLog) {
      return Response.json(
        createApiResponse(null, 'Fuel log not found', 'NOT_FOUND'),
        { status: 404 }
      );
    }

    const transformedLog = {
      id: fuelLog.id,
      property_id: fuelLog.propertyId,
      property_name: fuelLog.property.name,
      fuel_level_liters: parseFloat(fuelLog.fuelLevelLiters.toString()),
      consumption_rate: fuelLog.consumptionRate ? parseFloat(fuelLog.consumptionRate.toString()) : null,
      runtime_hours: fuelLog.runtimeHours ? parseFloat(fuelLog.runtimeHours.toString()) : null,
      efficiency_percentage: fuelLog.efficiencyPercentage ? parseFloat(fuelLog.efficiencyPercentage.toString()) : null,
      last_maintenance: fuelLog.lastMaintenance,
      next_maintenance: fuelLog.nextMaintenance,
      notes: fuelLog.notes,
      recorded_by: fuelLog.recordedBy,
      recorded_at: fuelLog.recordedAt,
      recorder: fuelLog.recorder ? {
        id: fuelLog.recorder.id,
        full_name: fuelLog.recorder.fullName,
        email: fuelLog.recorder.email,
      } : null,
    };

    return Response.json(
      createApiResponse(transformedLog),
      { 
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to fetch fuel log');
  }
}

async function updateFuelLogHandler(
  request: NextRequest, 
  context: { params: { logId: string } }, 
  currentUser: any
) {
  try {
    const { logId: id } = context.params;
    const body = await getRequestBody(request);
    
    // Validate request body
    const validation = validateRequest(updateFuelLogSchema, body);
    if (!validation.isValid) {
      return Response.json(
        createApiResponse(null, 'Validation failed', 'VALIDATION_ERROR'),
        { status: 400 }
      );
    }

    // Verify fuel log exists
    const existingLog = await prisma.generatorFuelLog.findUnique({
      where: { id },
    });

    if (!existingLog) {
      return Response.json(
        createApiResponse(null, 'Fuel log not found', 'NOT_FOUND'),
        { status: 404 }
      );
    }

    const updateData: any = {};
    const { 
      fuel_level_liters, 
      consumption_rate, 
      runtime_hours, 
      efficiency_percentage, 
      last_maintenance,
      next_maintenance,
      notes 
    } = validation.data;

    if (fuel_level_liters !== undefined) updateData.fuelLevelLiters = fuel_level_liters;
    if (consumption_rate !== undefined) updateData.consumptionRate = consumption_rate;
    if (runtime_hours !== undefined) updateData.runtimeHours = runtime_hours;
    if (efficiency_percentage !== undefined) updateData.efficiencyPercentage = efficiency_percentage;
    if (last_maintenance !== undefined) updateData.lastMaintenance = last_maintenance ? new Date(last_maintenance) : null;
    if (next_maintenance !== undefined) updateData.nextMaintenance = next_maintenance ? new Date(next_maintenance) : null;
    if (notes !== undefined) updateData.notes = notes;

    // Update fuel log
    const updatedLog = await prisma.generatorFuelLog.update({
      where: { id },
      data: updateData,
    });

    return Response.json(
      createApiResponse({
        message: 'Fuel log updated successfully',
        log: {
          id: updatedLog.id,
          property_id: updatedLog.propertyId,
          fuel_level_liters: parseFloat(updatedLog.fuelLevelLiters.toString()),
          consumption_rate: updatedLog.consumptionRate ? parseFloat(updatedLog.consumptionRate.toString()) : null,
          runtime_hours: updatedLog.runtimeHours ? parseFloat(updatedLog.runtimeHours.toString()) : null,
          efficiency_percentage: updatedLog.efficiencyPercentage ? parseFloat(updatedLog.efficiencyPercentage.toString()) : null,
          last_maintenance: updatedLog.lastMaintenance,
          next_maintenance: updatedLog.nextMaintenance,
          notes: updatedLog.notes,
          recorded_at: updatedLog.recordedAt,
        },
      }),
      { 
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to update fuel log');
  }
}

async function deleteFuelLogHandler(
  request: NextRequest, 
  context: { params: { logId: string } }, 
  currentUser: any
) {
  try {
    const { logId: id } = context.params;

    // Verify fuel log exists
    const existingLog = await prisma.generatorFuelLog.findUnique({
      where: { id },
    });

    if (!existingLog) {
      return Response.json(
        createApiResponse(null, 'Fuel log not found', 'NOT_FOUND'),
        { status: 404 }
      );
    }

    // Delete fuel log
    await prisma.generatorFuelLog.delete({
      where: { id },
    });

    return Response.json(
      createApiResponse({
        message: 'Fuel log deleted successfully',
      }),
      { 
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to delete fuel log');
  }
}

export const GET = requireAuth(getFuelLogHandler);
export const PUT = requireAuth(updateFuelLogHandler);
export const DELETE = requireAuth(deleteFuelLogHandler);

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: corsHeaders(),
  });
}
