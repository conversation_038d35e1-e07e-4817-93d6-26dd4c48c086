import { prisma } from '../prisma';
import { ThresholdAlert } from './threshold-monitor';

export interface NotificationPayload {
  id: string;
  type: 'threshold_alert' | 'alert_resolution' | 'system_update' | 'maintenance_reminder';
  title: string;
  message: string;
  data?: Record<string, any>;
  priority: 'low' | 'normal' | 'high' | 'critical';
  targetUsers?: string[];
  targetRoles?: string[];
  propertyId?: string;
  timestamp: Date;
}

export interface SSEConnection {
  userId: string;
  response: Response;
  controller: ReadableStreamDefaultController;
}

export class NotificationService {
  private sseConnections: Map<string, SSEConnection> = new Map();
  private notificationQueue: NotificationPayload[] = [];

  /**
   * Send threshold alert notification
   */
  async sendThresholdAlert(alert: ThresholdAlert): Promise<void> {
    const notification: NotificationPayload = {
      id: `alert-${alert.id}`,
      type: 'threshold_alert',
      title: `${alert.thresholdType.toUpperCase()}: ${alert.metricName}`,
      message: alert.message,
      data: {
        alertId: alert.id,
        propertyId: alert.propertyId,
        propertyName: alert.propertyName,
        serviceType: alert.serviceType,
        metricName: alert.metricName,
        currentValue: alert.currentValue,
        thresholdValue: alert.thresholdValue,
        thresholdType: alert.thresholdType,
      },
      priority: this.mapSeverityToPriority(alert.severity),
      propertyId: alert.propertyId,
      timestamp: alert.timestamp,
    };

    // Determine target users based on alert type and property
    notification.targetUsers = await this.getTargetUsers(alert);

    // Send notification
    await this.sendNotification(notification);
  }

  /**
   * Send alert resolution notification
   */
  async sendAlertResolution(alert: ThresholdAlert): Promise<void> {
    const notification: NotificationPayload = {
      id: `resolution-${alert.id}`,
      type: 'alert_resolution',
      title: `RESOLVED: ${alert.metricName}`,
      message: `${alert.metricName} at ${alert.propertyName} has returned to normal levels.`,
      data: {
        alertId: alert.id,
        propertyId: alert.propertyId,
        propertyName: alert.propertyName,
        serviceType: alert.serviceType,
        metricName: alert.metricName,
      },
      priority: 'normal',
      propertyId: alert.propertyId,
      timestamp: new Date(),
    };

    // Send to same users who received the original alert
    notification.targetUsers = await this.getTargetUsers(alert);

    await this.sendNotification(notification);
  }

  /**
   * Send general notification
   */
  async sendNotification(notification: NotificationPayload): Promise<void> {
    try {
      // Store notification in database
      await this.storeNotification(notification);

      // Send via SSE to connected users
      await this.sendSSENotification(notification);

      // Add to queue for offline users
      this.notificationQueue.push(notification);

      // Clean old notifications from queue
      this.cleanNotificationQueue();
    } catch (error) {
      console.error('Error sending notification:', error);
    }
  }

  /**
   * Store notification in database
   */
  private async storeNotification(notification: NotificationPayload): Promise<void> {
    try {
      await prisma.notification.create({
        data: {
          id: notification.id,
          type: notification.type,
          title: notification.title,
          message: notification.message,
          data: notification.data || {},
          priority: notification.priority.toUpperCase(),
          propertyId: notification.propertyId,
          targetUsers: notification.targetUsers || [],
          targetRoles: notification.targetRoles || [],
          isRead: false,
          createdAt: notification.timestamp,
        },
      });
    } catch (error) {
      console.error('Error storing notification:', error);
    }
  }

  /**
   * Send notification via Server-Sent Events
   */
  private async sendSSENotification(notification: NotificationPayload): Promise<void> {
    const targetUsers = notification.targetUsers || [];
    
    // If no specific users, send to all connected users
    const usersToNotify = targetUsers.length > 0 ? targetUsers : Array.from(this.sseConnections.keys());

    for (const userId of usersToNotify) {
      const connection = this.sseConnections.get(userId);
      if (connection) {
        try {
          const sseData = `data: ${JSON.stringify(notification)}\n\n`;
          connection.controller.enqueue(new TextEncoder().encode(sseData));
        } catch (error) {
          console.error(`Error sending SSE to user ${userId}:`, error);
          // Remove broken connection
          this.sseConnections.delete(userId);
        }
      }
    }
  }

  /**
   * Add SSE connection for a user
   */
  addSSEConnection(userId: string, controller: ReadableStreamDefaultController): void {
    // Remove existing connection if any
    this.removeSSEConnection(userId);

    // Add new connection
    this.sseConnections.set(userId, {
      userId,
      response: new Response(),
      controller,
    });

    console.log(`SSE connection added for user: ${userId}`);

    // Send queued notifications for this user
    this.sendQueuedNotifications(userId);
  }

  /**
   * Remove SSE connection for a user
   */
  removeSSEConnection(userId: string): void {
    const connection = this.sseConnections.get(userId);
    if (connection) {
      try {
        connection.controller.close();
      } catch (error) {
        // Connection might already be closed
      }
      this.sseConnections.delete(userId);
      console.log(`SSE connection removed for user: ${userId}`);
    }
  }

  /**
   * Send queued notifications to a newly connected user
   */
  private async sendQueuedNotifications(userId: string): Promise<void> {
    const userNotifications = this.notificationQueue.filter(
      notification => 
        !notification.targetUsers || 
        notification.targetUsers.length === 0 || 
        notification.targetUsers.includes(userId)
    );

    for (const notification of userNotifications) {
      const connection = this.sseConnections.get(userId);
      if (connection) {
        try {
          const sseData = `data: ${JSON.stringify(notification)}\n\n`;
          connection.controller.enqueue(new TextEncoder().encode(sseData));
        } catch (error) {
          console.error(`Error sending queued notification to user ${userId}:`, error);
          break;
        }
      }
    }
  }

  /**
   * Get target users for an alert
   */
  private async getTargetUsers(alert: ThresholdAlert): Promise<string[]> {
    try {
      // Get users who should receive alerts for this property
      const targetRoles = ['admin', 'manager'];
      
      // Add specific roles based on alert type
      if (alert.serviceType === 'maintenance') {
        targetRoles.push('maintenance');
      } else if (alert.serviceType === 'security') {
        targetRoles.push('security');
      }

      // Get users with these roles
      const users = await prisma.user.findMany({
        where: {
          isActive: true,
          userRoles: {
            some: {
              role: {
                name: { in: targetRoles },
              },
            },
          },
        },
        select: { id: true },
      });

      return users.map(user => user.id);
    } catch (error) {
      console.error('Error getting target users:', error);
      return [];
    }
  }

  /**
   * Map alert severity to notification priority
   */
  private mapSeverityToPriority(severity: string): 'low' | 'normal' | 'high' | 'critical' {
    switch (severity) {
      case 'critical':
        return 'critical';
      case 'high':
        return 'high';
      case 'medium':
        return 'normal';
      case 'low':
      default:
        return 'low';
    }
  }

  /**
   * Clean old notifications from queue
   */
  private cleanNotificationQueue(): void {
    const maxAge = 24 * 60 * 60 * 1000; // 24 hours
    const now = new Date().getTime();
    
    this.notificationQueue = this.notificationQueue.filter(
      notification => now - notification.timestamp.getTime() < maxAge
    );
  }

  /**
   * Get unread notifications for a user
   */
  async getUnreadNotifications(userId: string): Promise<NotificationPayload[]> {
    try {
      const notifications = await prisma.notification.findMany({
        where: {
          OR: [
            { targetUsers: { has: userId } },
            { targetUsers: { isEmpty: true } },
          ],
          isRead: false,
        },
        orderBy: { createdAt: 'desc' },
        take: 50,
      });

      return notifications.map(notification => ({
        id: notification.id,
        type: notification.type as any,
        title: notification.title,
        message: notification.message,
        data: notification.data as Record<string, any>,
        priority: notification.priority.toLowerCase() as any,
        targetUsers: notification.targetUsers,
        targetRoles: notification.targetRoles,
        propertyId: notification.propertyId,
        timestamp: notification.createdAt,
      }));
    } catch (error) {
      console.error('Error getting unread notifications:', error);
      return [];
    }
  }

  /**
   * Mark notification as read
   */
  async markAsRead(notificationId: string, userId: string): Promise<void> {
    try {
      await prisma.notification.update({
        where: { id: notificationId },
        data: { isRead: true },
      });
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  }

  /**
   * Get connected users count
   */
  getConnectedUsersCount(): number {
    return this.sseConnections.size;
  }

  /**
   * Send system update notification
   */
  async sendSystemUpdate(title: string, message: string, targetRoles?: string[]): Promise<void> {
    const notification: NotificationPayload = {
      id: `system-${Date.now()}`,
      type: 'system_update',
      title,
      message,
      priority: 'normal',
      targetRoles: targetRoles || ['admin', 'manager'],
      timestamp: new Date(),
    };

    await this.sendNotification(notification);
  }
}
