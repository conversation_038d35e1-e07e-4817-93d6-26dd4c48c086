/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/next";
exports.ids = ["vendor-chunks/next"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/output/log.js":
/*!****************************************************!*\
  !*** ./node_modules/next/dist/build/output/log.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    bootstrap: function() {\n        return bootstrap;\n    },\n    error: function() {\n        return error;\n    },\n    event: function() {\n        return event;\n    },\n    info: function() {\n        return info;\n    },\n    prefixes: function() {\n        return prefixes;\n    },\n    ready: function() {\n        return ready;\n    },\n    trace: function() {\n        return trace;\n    },\n    wait: function() {\n        return wait;\n    },\n    warn: function() {\n        return warn;\n    },\n    warnOnce: function() {\n        return warnOnce;\n    }\n});\nconst _picocolors = __webpack_require__(/*! ../../lib/picocolors */ \"(rsc)/./node_modules/next/dist/lib/picocolors.js\");\nconst prefixes = {\n    wait: (0, _picocolors.white)((0, _picocolors.bold)(\"○\")),\n    error: (0, _picocolors.red)((0, _picocolors.bold)(\"⨯\")),\n    warn: (0, _picocolors.yellow)((0, _picocolors.bold)(\"⚠\")),\n    ready: \"▲\",\n    info: (0, _picocolors.white)((0, _picocolors.bold)(\" \")),\n    event: (0, _picocolors.green)((0, _picocolors.bold)(\"✓\")),\n    trace: (0, _picocolors.magenta)((0, _picocolors.bold)(\"\\xbb\"))\n};\nconst LOGGING_METHOD = {\n    log: \"log\",\n    warn: \"warn\",\n    error: \"error\"\n};\nfunction prefixedLog(prefixType, ...message) {\n    if ((message[0] === \"\" || message[0] === undefined) && message.length === 1) {\n        message.shift();\n    }\n    const consoleMethod = prefixType in LOGGING_METHOD ? LOGGING_METHOD[prefixType] : \"log\";\n    const prefix = prefixes[prefixType];\n    // If there's no message, don't print the prefix but a new line\n    if (message.length === 0) {\n        console[consoleMethod](\"\");\n    } else {\n        console[consoleMethod](\" \" + prefix, ...message);\n    }\n}\nfunction bootstrap(...message) {\n    console.log(\" \", ...message);\n}\nfunction wait(...message) {\n    prefixedLog(\"wait\", ...message);\n}\nfunction error(...message) {\n    prefixedLog(\"error\", ...message);\n}\nfunction warn(...message) {\n    prefixedLog(\"warn\", ...message);\n}\nfunction ready(...message) {\n    prefixedLog(\"ready\", ...message);\n}\nfunction info(...message) {\n    prefixedLog(\"info\", ...message);\n}\nfunction event(...message) {\n    prefixedLog(\"event\", ...message);\n}\nfunction trace(...message) {\n    prefixedLog(\"trace\", ...message);\n}\nconst warnOnceMessages = new Set();\nfunction warnOnce(...message) {\n    if (!warnOnceMessages.has(message[0])) {\n        warnOnceMessages.add(message.join(\" \"));\n        warn(...message);\n    }\n}\n\n//# sourceMappingURL=log.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/output/log.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/client/components/hooks-server-context.js":
/*!**************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/hooks-server-context.js ***!
  \**************************************************************************/
/***/ ((module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    DynamicServerError: function() {\n        return DynamicServerError;\n    },\n    isDynamicServerError: function() {\n        return isDynamicServerError;\n    }\n});\nconst DYNAMIC_ERROR_CODE = \"DYNAMIC_SERVER_USAGE\";\nclass DynamicServerError extends Error {\n    constructor(description){\n        super(\"Dynamic server usage: \" + description);\n        this.description = description;\n        this.digest = DYNAMIC_ERROR_CODE;\n    }\n}\nfunction isDynamicServerError(err) {\n    if (typeof err !== \"object\" || err === null || !(\"digest\" in err) || typeof err.digest !== \"string\") {\n        return false;\n    }\n    return err.digest === DYNAMIC_ERROR_CODE;\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=hooks-server-context.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2hvb2tzLXNlcnZlci1jb250ZXh0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztJQUVhQSxvQkFBa0I7ZUFBbEJBOztJQVFHQyxzQkFBb0I7ZUFBcEJBOzs7QUFWaEIsTUFBTUMscUJBQXFCO0FBRXBCLE1BQU1GLDJCQUEyQkc7SUFHdENDLFlBQVlDLFdBQW1DLENBQUU7UUFDL0MsS0FBSyxDQUFDLDJCQUF5QkE7YUFETEEsV0FBQUEsR0FBQUE7YUFGNUJDLE1BQUFBLEdBQW9DSjtJQUlwQztBQUNGO0FBRU8sU0FBU0QscUJBQXFCTSxHQUFZO0lBQy9DLElBQ0UsT0FBT0EsUUFBUSxZQUNmQSxRQUFRLFFBQ1IsQ0FBRSxhQUFZQSxHQUFBQSxLQUNkLE9BQU9BLElBQUlELE1BQU0sS0FBSyxVQUN0QjtRQUNBLE9BQU87SUFDVDtJQUVBLE9BQU9DLElBQUlELE1BQU0sS0FBS0o7QUFDeEIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zcnNyLXByb3BlcnR5LW1hbmFnZW1lbnQtYmFja2VuZC8uLi8uLi8uLi9zcmMvY2xpZW50L2NvbXBvbmVudHMvaG9va3Mtc2VydmVyLWNvbnRleHQudHM/ODIzOCJdLCJuYW1lcyI6WyJEeW5hbWljU2VydmVyRXJyb3IiLCJpc0R5bmFtaWNTZXJ2ZXJFcnJvciIsIkRZTkFNSUNfRVJST1JfQ09ERSIsIkVycm9yIiwiY29uc3RydWN0b3IiLCJkZXNjcmlwdGlvbiIsImRpZ2VzdCIsImVyciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/client/components/hooks-server-context.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/client/components/static-generation-bailout.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/static-generation-bailout.js ***!
  \*******************************************************************************/
/***/ ((module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    StaticGenBailoutError: function() {\n        return StaticGenBailoutError;\n    },\n    isStaticGenBailoutError: function() {\n        return isStaticGenBailoutError;\n    }\n});\nconst NEXT_STATIC_GEN_BAILOUT = \"NEXT_STATIC_GEN_BAILOUT\";\nclass StaticGenBailoutError extends Error {\n    constructor(...args){\n        super(...args);\n        this.code = NEXT_STATIC_GEN_BAILOUT;\n    }\n}\nfunction isStaticGenBailoutError(error) {\n    if (typeof error !== \"object\" || error === null || !(\"code\" in error)) {\n        return false;\n    }\n    return error.code === NEXT_STATIC_GEN_BAILOUT;\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=static-generation-bailout.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3N0YXRpYy1nZW5lcmF0aW9uLWJhaWxvdXQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0lBRWFBLHVCQUFxQjtlQUFyQkE7O0lBSUdDLHlCQUF1QjtlQUF2QkE7OztBQU5oQixNQUFNQywwQkFBMEI7QUFFekIsTUFBTUYsOEJBQThCRzs7O2FBQ3pCQyxJQUFBQSxHQUFPRjs7QUFDekI7QUFFTyxTQUFTRCx3QkFDZEksS0FBYztJQUVkLElBQUksT0FBT0EsVUFBVSxZQUFZQSxVQUFVLFFBQVEsQ0FBRSxXQUFVQSxLQUFBQSxHQUFRO1FBQ3JFLE9BQU87SUFDVDtJQUVBLE9BQU9BLE1BQU1ELElBQUksS0FBS0Y7QUFDeEIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zcnNyLXByb3BlcnR5LW1hbmFnZW1lbnQtYmFja2VuZC8uLi8uLi8uLi9zcmMvY2xpZW50L2NvbXBvbmVudHMvc3RhdGljLWdlbmVyYXRpb24tYmFpbG91dC50cz80YzMxIl0sIm5hbWVzIjpbIlN0YXRpY0dlbkJhaWxvdXRFcnJvciIsImlzU3RhdGljR2VuQmFpbG91dEVycm9yIiwiTkVYVF9TVEFUSUNfR0VOX0JBSUxPVVQiLCJFcnJvciIsImNvZGUiLCJlcnJvciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/client/components/static-generation-bailout.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/compiled/@opentelemetry/api/index.js":
/*!*********************************************************************!*\
  !*** ./node_modules/next/dist/compiled/@opentelemetry/api/index.js ***!
  \*********************************************************************/
/***/ ((module) => {

eval("(()=>{\"use strict\";var e={491:(e,t,r)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.ContextAPI=void 0;const n=r(223);const a=r(172);const o=r(930);const i=\"context\";const c=new n.NoopContextManager;class ContextAPI{constructor(){}static getInstance(){if(!this._instance){this._instance=new ContextAPI}return this._instance}setGlobalContextManager(e){return(0,a.registerGlobal)(i,e,o.DiagAPI.instance())}active(){return this._getContextManager().active()}with(e,t,r,...n){return this._getContextManager().with(e,t,r,...n)}bind(e,t){return this._getContextManager().bind(e,t)}_getContextManager(){return(0,a.getGlobal)(i)||c}disable(){this._getContextManager().disable();(0,a.unregisterGlobal)(i,o.DiagAPI.instance())}}t.ContextAPI=ContextAPI},930:(e,t,r)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.DiagAPI=void 0;const n=r(56);const a=r(912);const o=r(957);const i=r(172);const c=\"diag\";class DiagAPI{constructor(){function _logProxy(e){return function(...t){const r=(0,i.getGlobal)(\"diag\");if(!r)return;return r[e](...t)}}const e=this;const setLogger=(t,r={logLevel:o.DiagLogLevel.INFO})=>{var n,c,s;if(t===e){const t=new Error(\"Cannot use diag as the logger for itself. Please use a DiagLogger implementation like ConsoleDiagLogger or a custom implementation\");e.error((n=t.stack)!==null&&n!==void 0?n:t.message);return false}if(typeof r===\"number\"){r={logLevel:r}}const u=(0,i.getGlobal)(\"diag\");const l=(0,a.createLogLevelDiagLogger)((c=r.logLevel)!==null&&c!==void 0?c:o.DiagLogLevel.INFO,t);if(u&&!r.suppressOverrideMessage){const e=(s=(new Error).stack)!==null&&s!==void 0?s:\"<failed to generate stacktrace>\";u.warn(`Current logger will be overwritten from ${e}`);l.warn(`Current logger will overwrite one already registered from ${e}`)}return(0,i.registerGlobal)(\"diag\",l,e,true)};e.setLogger=setLogger;e.disable=()=>{(0,i.unregisterGlobal)(c,e)};e.createComponentLogger=e=>new n.DiagComponentLogger(e);e.verbose=_logProxy(\"verbose\");e.debug=_logProxy(\"debug\");e.info=_logProxy(\"info\");e.warn=_logProxy(\"warn\");e.error=_logProxy(\"error\")}static instance(){if(!this._instance){this._instance=new DiagAPI}return this._instance}}t.DiagAPI=DiagAPI},653:(e,t,r)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.MetricsAPI=void 0;const n=r(660);const a=r(172);const o=r(930);const i=\"metrics\";class MetricsAPI{constructor(){}static getInstance(){if(!this._instance){this._instance=new MetricsAPI}return this._instance}setGlobalMeterProvider(e){return(0,a.registerGlobal)(i,e,o.DiagAPI.instance())}getMeterProvider(){return(0,a.getGlobal)(i)||n.NOOP_METER_PROVIDER}getMeter(e,t,r){return this.getMeterProvider().getMeter(e,t,r)}disable(){(0,a.unregisterGlobal)(i,o.DiagAPI.instance())}}t.MetricsAPI=MetricsAPI},181:(e,t,r)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.PropagationAPI=void 0;const n=r(172);const a=r(874);const o=r(194);const i=r(277);const c=r(369);const s=r(930);const u=\"propagation\";const l=new a.NoopTextMapPropagator;class PropagationAPI{constructor(){this.createBaggage=c.createBaggage;this.getBaggage=i.getBaggage;this.getActiveBaggage=i.getActiveBaggage;this.setBaggage=i.setBaggage;this.deleteBaggage=i.deleteBaggage}static getInstance(){if(!this._instance){this._instance=new PropagationAPI}return this._instance}setGlobalPropagator(e){return(0,n.registerGlobal)(u,e,s.DiagAPI.instance())}inject(e,t,r=o.defaultTextMapSetter){return this._getGlobalPropagator().inject(e,t,r)}extract(e,t,r=o.defaultTextMapGetter){return this._getGlobalPropagator().extract(e,t,r)}fields(){return this._getGlobalPropagator().fields()}disable(){(0,n.unregisterGlobal)(u,s.DiagAPI.instance())}_getGlobalPropagator(){return(0,n.getGlobal)(u)||l}}t.PropagationAPI=PropagationAPI},997:(e,t,r)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.TraceAPI=void 0;const n=r(172);const a=r(846);const o=r(139);const i=r(607);const c=r(930);const s=\"trace\";class TraceAPI{constructor(){this._proxyTracerProvider=new a.ProxyTracerProvider;this.wrapSpanContext=o.wrapSpanContext;this.isSpanContextValid=o.isSpanContextValid;this.deleteSpan=i.deleteSpan;this.getSpan=i.getSpan;this.getActiveSpan=i.getActiveSpan;this.getSpanContext=i.getSpanContext;this.setSpan=i.setSpan;this.setSpanContext=i.setSpanContext}static getInstance(){if(!this._instance){this._instance=new TraceAPI}return this._instance}setGlobalTracerProvider(e){const t=(0,n.registerGlobal)(s,this._proxyTracerProvider,c.DiagAPI.instance());if(t){this._proxyTracerProvider.setDelegate(e)}return t}getTracerProvider(){return(0,n.getGlobal)(s)||this._proxyTracerProvider}getTracer(e,t){return this.getTracerProvider().getTracer(e,t)}disable(){(0,n.unregisterGlobal)(s,c.DiagAPI.instance());this._proxyTracerProvider=new a.ProxyTracerProvider}}t.TraceAPI=TraceAPI},277:(e,t,r)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.deleteBaggage=t.setBaggage=t.getActiveBaggage=t.getBaggage=void 0;const n=r(491);const a=r(780);const o=(0,a.createContextKey)(\"OpenTelemetry Baggage Key\");function getBaggage(e){return e.getValue(o)||undefined}t.getBaggage=getBaggage;function getActiveBaggage(){return getBaggage(n.ContextAPI.getInstance().active())}t.getActiveBaggage=getActiveBaggage;function setBaggage(e,t){return e.setValue(o,t)}t.setBaggage=setBaggage;function deleteBaggage(e){return e.deleteValue(o)}t.deleteBaggage=deleteBaggage},993:(e,t)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.BaggageImpl=void 0;class BaggageImpl{constructor(e){this._entries=e?new Map(e):new Map}getEntry(e){const t=this._entries.get(e);if(!t){return undefined}return Object.assign({},t)}getAllEntries(){return Array.from(this._entries.entries()).map((([e,t])=>[e,t]))}setEntry(e,t){const r=new BaggageImpl(this._entries);r._entries.set(e,t);return r}removeEntry(e){const t=new BaggageImpl(this._entries);t._entries.delete(e);return t}removeEntries(...e){const t=new BaggageImpl(this._entries);for(const r of e){t._entries.delete(r)}return t}clear(){return new BaggageImpl}}t.BaggageImpl=BaggageImpl},830:(e,t)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.baggageEntryMetadataSymbol=void 0;t.baggageEntryMetadataSymbol=Symbol(\"BaggageEntryMetadata\")},369:(e,t,r)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.baggageEntryMetadataFromString=t.createBaggage=void 0;const n=r(930);const a=r(993);const o=r(830);const i=n.DiagAPI.instance();function createBaggage(e={}){return new a.BaggageImpl(new Map(Object.entries(e)))}t.createBaggage=createBaggage;function baggageEntryMetadataFromString(e){if(typeof e!==\"string\"){i.error(`Cannot create baggage metadata from unknown type: ${typeof e}`);e=\"\"}return{__TYPE__:o.baggageEntryMetadataSymbol,toString(){return e}}}t.baggageEntryMetadataFromString=baggageEntryMetadataFromString},67:(e,t,r)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.context=void 0;const n=r(491);t.context=n.ContextAPI.getInstance()},223:(e,t,r)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.NoopContextManager=void 0;const n=r(780);class NoopContextManager{active(){return n.ROOT_CONTEXT}with(e,t,r,...n){return t.call(r,...n)}bind(e,t){return t}enable(){return this}disable(){return this}}t.NoopContextManager=NoopContextManager},780:(e,t)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.ROOT_CONTEXT=t.createContextKey=void 0;function createContextKey(e){return Symbol.for(e)}t.createContextKey=createContextKey;class BaseContext{constructor(e){const t=this;t._currentContext=e?new Map(e):new Map;t.getValue=e=>t._currentContext.get(e);t.setValue=(e,r)=>{const n=new BaseContext(t._currentContext);n._currentContext.set(e,r);return n};t.deleteValue=e=>{const r=new BaseContext(t._currentContext);r._currentContext.delete(e);return r}}}t.ROOT_CONTEXT=new BaseContext},506:(e,t,r)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.diag=void 0;const n=r(930);t.diag=n.DiagAPI.instance()},56:(e,t,r)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.DiagComponentLogger=void 0;const n=r(172);class DiagComponentLogger{constructor(e){this._namespace=e.namespace||\"DiagComponentLogger\"}debug(...e){return logProxy(\"debug\",this._namespace,e)}error(...e){return logProxy(\"error\",this._namespace,e)}info(...e){return logProxy(\"info\",this._namespace,e)}warn(...e){return logProxy(\"warn\",this._namespace,e)}verbose(...e){return logProxy(\"verbose\",this._namespace,e)}}t.DiagComponentLogger=DiagComponentLogger;function logProxy(e,t,r){const a=(0,n.getGlobal)(\"diag\");if(!a){return}r.unshift(t);return a[e](...r)}},972:(e,t)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.DiagConsoleLogger=void 0;const r=[{n:\"error\",c:\"error\"},{n:\"warn\",c:\"warn\"},{n:\"info\",c:\"info\"},{n:\"debug\",c:\"debug\"},{n:\"verbose\",c:\"trace\"}];class DiagConsoleLogger{constructor(){function _consoleFunc(e){return function(...t){if(console){let r=console[e];if(typeof r!==\"function\"){r=console.log}if(typeof r===\"function\"){return r.apply(console,t)}}}}for(let e=0;e<r.length;e++){this[r[e].n]=_consoleFunc(r[e].c)}}}t.DiagConsoleLogger=DiagConsoleLogger},912:(e,t,r)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.createLogLevelDiagLogger=void 0;const n=r(957);function createLogLevelDiagLogger(e,t){if(e<n.DiagLogLevel.NONE){e=n.DiagLogLevel.NONE}else if(e>n.DiagLogLevel.ALL){e=n.DiagLogLevel.ALL}t=t||{};function _filterFunc(r,n){const a=t[r];if(typeof a===\"function\"&&e>=n){return a.bind(t)}return function(){}}return{error:_filterFunc(\"error\",n.DiagLogLevel.ERROR),warn:_filterFunc(\"warn\",n.DiagLogLevel.WARN),info:_filterFunc(\"info\",n.DiagLogLevel.INFO),debug:_filterFunc(\"debug\",n.DiagLogLevel.DEBUG),verbose:_filterFunc(\"verbose\",n.DiagLogLevel.VERBOSE)}}t.createLogLevelDiagLogger=createLogLevelDiagLogger},957:(e,t)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.DiagLogLevel=void 0;var r;(function(e){e[e[\"NONE\"]=0]=\"NONE\";e[e[\"ERROR\"]=30]=\"ERROR\";e[e[\"WARN\"]=50]=\"WARN\";e[e[\"INFO\"]=60]=\"INFO\";e[e[\"DEBUG\"]=70]=\"DEBUG\";e[e[\"VERBOSE\"]=80]=\"VERBOSE\";e[e[\"ALL\"]=9999]=\"ALL\"})(r=t.DiagLogLevel||(t.DiagLogLevel={}))},172:(e,t,r)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.unregisterGlobal=t.getGlobal=t.registerGlobal=void 0;const n=r(200);const a=r(521);const o=r(130);const i=a.VERSION.split(\".\")[0];const c=Symbol.for(`opentelemetry.js.api.${i}`);const s=n._globalThis;function registerGlobal(e,t,r,n=false){var o;const i=s[c]=(o=s[c])!==null&&o!==void 0?o:{version:a.VERSION};if(!n&&i[e]){const t=new Error(`@opentelemetry/api: Attempted duplicate registration of API: ${e}`);r.error(t.stack||t.message);return false}if(i.version!==a.VERSION){const t=new Error(`@opentelemetry/api: Registration of version v${i.version} for ${e} does not match previously registered API v${a.VERSION}`);r.error(t.stack||t.message);return false}i[e]=t;r.debug(`@opentelemetry/api: Registered a global for ${e} v${a.VERSION}.`);return true}t.registerGlobal=registerGlobal;function getGlobal(e){var t,r;const n=(t=s[c])===null||t===void 0?void 0:t.version;if(!n||!(0,o.isCompatible)(n)){return}return(r=s[c])===null||r===void 0?void 0:r[e]}t.getGlobal=getGlobal;function unregisterGlobal(e,t){t.debug(`@opentelemetry/api: Unregistering a global for ${e} v${a.VERSION}.`);const r=s[c];if(r){delete r[e]}}t.unregisterGlobal=unregisterGlobal},130:(e,t,r)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.isCompatible=t._makeCompatibilityCheck=void 0;const n=r(521);const a=/^(\\d+)\\.(\\d+)\\.(\\d+)(-(.+))?$/;function _makeCompatibilityCheck(e){const t=new Set([e]);const r=new Set;const n=e.match(a);if(!n){return()=>false}const o={major:+n[1],minor:+n[2],patch:+n[3],prerelease:n[4]};if(o.prerelease!=null){return function isExactmatch(t){return t===e}}function _reject(e){r.add(e);return false}function _accept(e){t.add(e);return true}return function isCompatible(e){if(t.has(e)){return true}if(r.has(e)){return false}const n=e.match(a);if(!n){return _reject(e)}const i={major:+n[1],minor:+n[2],patch:+n[3],prerelease:n[4]};if(i.prerelease!=null){return _reject(e)}if(o.major!==i.major){return _reject(e)}if(o.major===0){if(o.minor===i.minor&&o.patch<=i.patch){return _accept(e)}return _reject(e)}if(o.minor<=i.minor){return _accept(e)}return _reject(e)}}t._makeCompatibilityCheck=_makeCompatibilityCheck;t.isCompatible=_makeCompatibilityCheck(n.VERSION)},886:(e,t,r)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.metrics=void 0;const n=r(653);t.metrics=n.MetricsAPI.getInstance()},901:(e,t)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.ValueType=void 0;var r;(function(e){e[e[\"INT\"]=0]=\"INT\";e[e[\"DOUBLE\"]=1]=\"DOUBLE\"})(r=t.ValueType||(t.ValueType={}))},102:(e,t)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.createNoopMeter=t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=t.NOOP_OBSERVABLE_GAUGE_METRIC=t.NOOP_OBSERVABLE_COUNTER_METRIC=t.NOOP_UP_DOWN_COUNTER_METRIC=t.NOOP_HISTOGRAM_METRIC=t.NOOP_COUNTER_METRIC=t.NOOP_METER=t.NoopObservableUpDownCounterMetric=t.NoopObservableGaugeMetric=t.NoopObservableCounterMetric=t.NoopObservableMetric=t.NoopHistogramMetric=t.NoopUpDownCounterMetric=t.NoopCounterMetric=t.NoopMetric=t.NoopMeter=void 0;class NoopMeter{constructor(){}createHistogram(e,r){return t.NOOP_HISTOGRAM_METRIC}createCounter(e,r){return t.NOOP_COUNTER_METRIC}createUpDownCounter(e,r){return t.NOOP_UP_DOWN_COUNTER_METRIC}createObservableGauge(e,r){return t.NOOP_OBSERVABLE_GAUGE_METRIC}createObservableCounter(e,r){return t.NOOP_OBSERVABLE_COUNTER_METRIC}createObservableUpDownCounter(e,r){return t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC}addBatchObservableCallback(e,t){}removeBatchObservableCallback(e){}}t.NoopMeter=NoopMeter;class NoopMetric{}t.NoopMetric=NoopMetric;class NoopCounterMetric extends NoopMetric{add(e,t){}}t.NoopCounterMetric=NoopCounterMetric;class NoopUpDownCounterMetric extends NoopMetric{add(e,t){}}t.NoopUpDownCounterMetric=NoopUpDownCounterMetric;class NoopHistogramMetric extends NoopMetric{record(e,t){}}t.NoopHistogramMetric=NoopHistogramMetric;class NoopObservableMetric{addCallback(e){}removeCallback(e){}}t.NoopObservableMetric=NoopObservableMetric;class NoopObservableCounterMetric extends NoopObservableMetric{}t.NoopObservableCounterMetric=NoopObservableCounterMetric;class NoopObservableGaugeMetric extends NoopObservableMetric{}t.NoopObservableGaugeMetric=NoopObservableGaugeMetric;class NoopObservableUpDownCounterMetric extends NoopObservableMetric{}t.NoopObservableUpDownCounterMetric=NoopObservableUpDownCounterMetric;t.NOOP_METER=new NoopMeter;t.NOOP_COUNTER_METRIC=new NoopCounterMetric;t.NOOP_HISTOGRAM_METRIC=new NoopHistogramMetric;t.NOOP_UP_DOWN_COUNTER_METRIC=new NoopUpDownCounterMetric;t.NOOP_OBSERVABLE_COUNTER_METRIC=new NoopObservableCounterMetric;t.NOOP_OBSERVABLE_GAUGE_METRIC=new NoopObservableGaugeMetric;t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=new NoopObservableUpDownCounterMetric;function createNoopMeter(){return t.NOOP_METER}t.createNoopMeter=createNoopMeter},660:(e,t,r)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.NOOP_METER_PROVIDER=t.NoopMeterProvider=void 0;const n=r(102);class NoopMeterProvider{getMeter(e,t,r){return n.NOOP_METER}}t.NoopMeterProvider=NoopMeterProvider;t.NOOP_METER_PROVIDER=new NoopMeterProvider},200:function(e,t,r){var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){if(n===undefined)n=r;Object.defineProperty(e,n,{enumerable:true,get:function(){return t[r]}})}:function(e,t,r,n){if(n===undefined)n=r;e[n]=t[r]});var a=this&&this.__exportStar||function(e,t){for(var r in e)if(r!==\"default\"&&!Object.prototype.hasOwnProperty.call(t,r))n(t,e,r)};Object.defineProperty(t,\"__esModule\",{value:true});a(r(46),t)},651:(e,t)=>{Object.defineProperty(t,\"__esModule\",{value:true});t._globalThis=void 0;t._globalThis=typeof globalThis===\"object\"?globalThis:global},46:function(e,t,r){var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){if(n===undefined)n=r;Object.defineProperty(e,n,{enumerable:true,get:function(){return t[r]}})}:function(e,t,r,n){if(n===undefined)n=r;e[n]=t[r]});var a=this&&this.__exportStar||function(e,t){for(var r in e)if(r!==\"default\"&&!Object.prototype.hasOwnProperty.call(t,r))n(t,e,r)};Object.defineProperty(t,\"__esModule\",{value:true});a(r(651),t)},939:(e,t,r)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.propagation=void 0;const n=r(181);t.propagation=n.PropagationAPI.getInstance()},874:(e,t)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.NoopTextMapPropagator=void 0;class NoopTextMapPropagator{inject(e,t){}extract(e,t){return e}fields(){return[]}}t.NoopTextMapPropagator=NoopTextMapPropagator},194:(e,t)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.defaultTextMapSetter=t.defaultTextMapGetter=void 0;t.defaultTextMapGetter={get(e,t){if(e==null){return undefined}return e[t]},keys(e){if(e==null){return[]}return Object.keys(e)}};t.defaultTextMapSetter={set(e,t,r){if(e==null){return}e[t]=r}}},845:(e,t,r)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.trace=void 0;const n=r(997);t.trace=n.TraceAPI.getInstance()},403:(e,t,r)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.NonRecordingSpan=void 0;const n=r(476);class NonRecordingSpan{constructor(e=n.INVALID_SPAN_CONTEXT){this._spanContext=e}spanContext(){return this._spanContext}setAttribute(e,t){return this}setAttributes(e){return this}addEvent(e,t){return this}setStatus(e){return this}updateName(e){return this}end(e){}isRecording(){return false}recordException(e,t){}}t.NonRecordingSpan=NonRecordingSpan},614:(e,t,r)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.NoopTracer=void 0;const n=r(491);const a=r(607);const o=r(403);const i=r(139);const c=n.ContextAPI.getInstance();class NoopTracer{startSpan(e,t,r=c.active()){const n=Boolean(t===null||t===void 0?void 0:t.root);if(n){return new o.NonRecordingSpan}const s=r&&(0,a.getSpanContext)(r);if(isSpanContext(s)&&(0,i.isSpanContextValid)(s)){return new o.NonRecordingSpan(s)}else{return new o.NonRecordingSpan}}startActiveSpan(e,t,r,n){let o;let i;let s;if(arguments.length<2){return}else if(arguments.length===2){s=t}else if(arguments.length===3){o=t;s=r}else{o=t;i=r;s=n}const u=i!==null&&i!==void 0?i:c.active();const l=this.startSpan(e,o,u);const g=(0,a.setSpan)(u,l);return c.with(g,s,undefined,l)}}t.NoopTracer=NoopTracer;function isSpanContext(e){return typeof e===\"object\"&&typeof e[\"spanId\"]===\"string\"&&typeof e[\"traceId\"]===\"string\"&&typeof e[\"traceFlags\"]===\"number\"}},124:(e,t,r)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.NoopTracerProvider=void 0;const n=r(614);class NoopTracerProvider{getTracer(e,t,r){return new n.NoopTracer}}t.NoopTracerProvider=NoopTracerProvider},125:(e,t,r)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.ProxyTracer=void 0;const n=r(614);const a=new n.NoopTracer;class ProxyTracer{constructor(e,t,r,n){this._provider=e;this.name=t;this.version=r;this.options=n}startSpan(e,t,r){return this._getTracer().startSpan(e,t,r)}startActiveSpan(e,t,r,n){const a=this._getTracer();return Reflect.apply(a.startActiveSpan,a,arguments)}_getTracer(){if(this._delegate){return this._delegate}const e=this._provider.getDelegateTracer(this.name,this.version,this.options);if(!e){return a}this._delegate=e;return this._delegate}}t.ProxyTracer=ProxyTracer},846:(e,t,r)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.ProxyTracerProvider=void 0;const n=r(125);const a=r(124);const o=new a.NoopTracerProvider;class ProxyTracerProvider{getTracer(e,t,r){var a;return(a=this.getDelegateTracer(e,t,r))!==null&&a!==void 0?a:new n.ProxyTracer(this,e,t,r)}getDelegate(){var e;return(e=this._delegate)!==null&&e!==void 0?e:o}setDelegate(e){this._delegate=e}getDelegateTracer(e,t,r){var n;return(n=this._delegate)===null||n===void 0?void 0:n.getTracer(e,t,r)}}t.ProxyTracerProvider=ProxyTracerProvider},996:(e,t)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.SamplingDecision=void 0;var r;(function(e){e[e[\"NOT_RECORD\"]=0]=\"NOT_RECORD\";e[e[\"RECORD\"]=1]=\"RECORD\";e[e[\"RECORD_AND_SAMPLED\"]=2]=\"RECORD_AND_SAMPLED\"})(r=t.SamplingDecision||(t.SamplingDecision={}))},607:(e,t,r)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.getSpanContext=t.setSpanContext=t.deleteSpan=t.setSpan=t.getActiveSpan=t.getSpan=void 0;const n=r(780);const a=r(403);const o=r(491);const i=(0,n.createContextKey)(\"OpenTelemetry Context Key SPAN\");function getSpan(e){return e.getValue(i)||undefined}t.getSpan=getSpan;function getActiveSpan(){return getSpan(o.ContextAPI.getInstance().active())}t.getActiveSpan=getActiveSpan;function setSpan(e,t){return e.setValue(i,t)}t.setSpan=setSpan;function deleteSpan(e){return e.deleteValue(i)}t.deleteSpan=deleteSpan;function setSpanContext(e,t){return setSpan(e,new a.NonRecordingSpan(t))}t.setSpanContext=setSpanContext;function getSpanContext(e){var t;return(t=getSpan(e))===null||t===void 0?void 0:t.spanContext()}t.getSpanContext=getSpanContext},325:(e,t,r)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.TraceStateImpl=void 0;const n=r(564);const a=32;const o=512;const i=\",\";const c=\"=\";class TraceStateImpl{constructor(e){this._internalState=new Map;if(e)this._parse(e)}set(e,t){const r=this._clone();if(r._internalState.has(e)){r._internalState.delete(e)}r._internalState.set(e,t);return r}unset(e){const t=this._clone();t._internalState.delete(e);return t}get(e){return this._internalState.get(e)}serialize(){return this._keys().reduce(((e,t)=>{e.push(t+c+this.get(t));return e}),[]).join(i)}_parse(e){if(e.length>o)return;this._internalState=e.split(i).reverse().reduce(((e,t)=>{const r=t.trim();const a=r.indexOf(c);if(a!==-1){const o=r.slice(0,a);const i=r.slice(a+1,t.length);if((0,n.validateKey)(o)&&(0,n.validateValue)(i)){e.set(o,i)}else{}}return e}),new Map);if(this._internalState.size>a){this._internalState=new Map(Array.from(this._internalState.entries()).reverse().slice(0,a))}}_keys(){return Array.from(this._internalState.keys()).reverse()}_clone(){const e=new TraceStateImpl;e._internalState=new Map(this._internalState);return e}}t.TraceStateImpl=TraceStateImpl},564:(e,t)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.validateValue=t.validateKey=void 0;const r=\"[_0-9a-z-*/]\";const n=`[a-z]${r}{0,255}`;const a=`[a-z0-9]${r}{0,240}@[a-z]${r}{0,13}`;const o=new RegExp(`^(?:${n}|${a})$`);const i=/^[ -~]{0,255}[!-~]$/;const c=/,|=/;function validateKey(e){return o.test(e)}t.validateKey=validateKey;function validateValue(e){return i.test(e)&&!c.test(e)}t.validateValue=validateValue},98:(e,t,r)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.createTraceState=void 0;const n=r(325);function createTraceState(e){return new n.TraceStateImpl(e)}t.createTraceState=createTraceState},476:(e,t,r)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.INVALID_SPAN_CONTEXT=t.INVALID_TRACEID=t.INVALID_SPANID=void 0;const n=r(475);t.INVALID_SPANID=\"0000000000000000\";t.INVALID_TRACEID=\"00000000000000000000000000000000\";t.INVALID_SPAN_CONTEXT={traceId:t.INVALID_TRACEID,spanId:t.INVALID_SPANID,traceFlags:n.TraceFlags.NONE}},357:(e,t)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.SpanKind=void 0;var r;(function(e){e[e[\"INTERNAL\"]=0]=\"INTERNAL\";e[e[\"SERVER\"]=1]=\"SERVER\";e[e[\"CLIENT\"]=2]=\"CLIENT\";e[e[\"PRODUCER\"]=3]=\"PRODUCER\";e[e[\"CONSUMER\"]=4]=\"CONSUMER\"})(r=t.SpanKind||(t.SpanKind={}))},139:(e,t,r)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.wrapSpanContext=t.isSpanContextValid=t.isValidSpanId=t.isValidTraceId=void 0;const n=r(476);const a=r(403);const o=/^([0-9a-f]{32})$/i;const i=/^[0-9a-f]{16}$/i;function isValidTraceId(e){return o.test(e)&&e!==n.INVALID_TRACEID}t.isValidTraceId=isValidTraceId;function isValidSpanId(e){return i.test(e)&&e!==n.INVALID_SPANID}t.isValidSpanId=isValidSpanId;function isSpanContextValid(e){return isValidTraceId(e.traceId)&&isValidSpanId(e.spanId)}t.isSpanContextValid=isSpanContextValid;function wrapSpanContext(e){return new a.NonRecordingSpan(e)}t.wrapSpanContext=wrapSpanContext},847:(e,t)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.SpanStatusCode=void 0;var r;(function(e){e[e[\"UNSET\"]=0]=\"UNSET\";e[e[\"OK\"]=1]=\"OK\";e[e[\"ERROR\"]=2]=\"ERROR\"})(r=t.SpanStatusCode||(t.SpanStatusCode={}))},475:(e,t)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.TraceFlags=void 0;var r;(function(e){e[e[\"NONE\"]=0]=\"NONE\";e[e[\"SAMPLED\"]=1]=\"SAMPLED\"})(r=t.TraceFlags||(t.TraceFlags={}))},521:(e,t)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.VERSION=void 0;t.VERSION=\"1.6.0\"}};var t={};function __nccwpck_require__(r){var n=t[r];if(n!==undefined){return n.exports}var a=t[r]={exports:{}};var o=true;try{e[r].call(a.exports,a,a.exports,__nccwpck_require__);o=false}finally{if(o)delete t[r]}return a.exports}if(typeof __nccwpck_require__!==\"undefined\")__nccwpck_require__.ab=__dirname+\"/\";var r={};(()=>{var e=r;Object.defineProperty(e,\"__esModule\",{value:true});e.trace=e.propagation=e.metrics=e.diag=e.context=e.INVALID_SPAN_CONTEXT=e.INVALID_TRACEID=e.INVALID_SPANID=e.isValidSpanId=e.isValidTraceId=e.isSpanContextValid=e.createTraceState=e.TraceFlags=e.SpanStatusCode=e.SpanKind=e.SamplingDecision=e.ProxyTracerProvider=e.ProxyTracer=e.defaultTextMapSetter=e.defaultTextMapGetter=e.ValueType=e.createNoopMeter=e.DiagLogLevel=e.DiagConsoleLogger=e.ROOT_CONTEXT=e.createContextKey=e.baggageEntryMetadataFromString=void 0;var t=__nccwpck_require__(369);Object.defineProperty(e,\"baggageEntryMetadataFromString\",{enumerable:true,get:function(){return t.baggageEntryMetadataFromString}});var n=__nccwpck_require__(780);Object.defineProperty(e,\"createContextKey\",{enumerable:true,get:function(){return n.createContextKey}});Object.defineProperty(e,\"ROOT_CONTEXT\",{enumerable:true,get:function(){return n.ROOT_CONTEXT}});var a=__nccwpck_require__(972);Object.defineProperty(e,\"DiagConsoleLogger\",{enumerable:true,get:function(){return a.DiagConsoleLogger}});var o=__nccwpck_require__(957);Object.defineProperty(e,\"DiagLogLevel\",{enumerable:true,get:function(){return o.DiagLogLevel}});var i=__nccwpck_require__(102);Object.defineProperty(e,\"createNoopMeter\",{enumerable:true,get:function(){return i.createNoopMeter}});var c=__nccwpck_require__(901);Object.defineProperty(e,\"ValueType\",{enumerable:true,get:function(){return c.ValueType}});var s=__nccwpck_require__(194);Object.defineProperty(e,\"defaultTextMapGetter\",{enumerable:true,get:function(){return s.defaultTextMapGetter}});Object.defineProperty(e,\"defaultTextMapSetter\",{enumerable:true,get:function(){return s.defaultTextMapSetter}});var u=__nccwpck_require__(125);Object.defineProperty(e,\"ProxyTracer\",{enumerable:true,get:function(){return u.ProxyTracer}});var l=__nccwpck_require__(846);Object.defineProperty(e,\"ProxyTracerProvider\",{enumerable:true,get:function(){return l.ProxyTracerProvider}});var g=__nccwpck_require__(996);Object.defineProperty(e,\"SamplingDecision\",{enumerable:true,get:function(){return g.SamplingDecision}});var p=__nccwpck_require__(357);Object.defineProperty(e,\"SpanKind\",{enumerable:true,get:function(){return p.SpanKind}});var d=__nccwpck_require__(847);Object.defineProperty(e,\"SpanStatusCode\",{enumerable:true,get:function(){return d.SpanStatusCode}});var _=__nccwpck_require__(475);Object.defineProperty(e,\"TraceFlags\",{enumerable:true,get:function(){return _.TraceFlags}});var f=__nccwpck_require__(98);Object.defineProperty(e,\"createTraceState\",{enumerable:true,get:function(){return f.createTraceState}});var b=__nccwpck_require__(139);Object.defineProperty(e,\"isSpanContextValid\",{enumerable:true,get:function(){return b.isSpanContextValid}});Object.defineProperty(e,\"isValidTraceId\",{enumerable:true,get:function(){return b.isValidTraceId}});Object.defineProperty(e,\"isValidSpanId\",{enumerable:true,get:function(){return b.isValidSpanId}});var v=__nccwpck_require__(476);Object.defineProperty(e,\"INVALID_SPANID\",{enumerable:true,get:function(){return v.INVALID_SPANID}});Object.defineProperty(e,\"INVALID_TRACEID\",{enumerable:true,get:function(){return v.INVALID_TRACEID}});Object.defineProperty(e,\"INVALID_SPAN_CONTEXT\",{enumerable:true,get:function(){return v.INVALID_SPAN_CONTEXT}});const O=__nccwpck_require__(67);Object.defineProperty(e,\"context\",{enumerable:true,get:function(){return O.context}});const P=__nccwpck_require__(506);Object.defineProperty(e,\"diag\",{enumerable:true,get:function(){return P.diag}});const N=__nccwpck_require__(886);Object.defineProperty(e,\"metrics\",{enumerable:true,get:function(){return N.metrics}});const S=__nccwpck_require__(939);Object.defineProperty(e,\"propagation\",{enumerable:true,get:function(){return S.propagation}});const C=__nccwpck_require__(845);Object.defineProperty(e,\"trace\",{enumerable:true,get:function(){return C.trace}});e[\"default\"]={context:O.context,diag:P.diag,metrics:N.metrics,propagation:S.propagation,trace:C.trace}})();module.exports=r})();//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/compiled/@opentelemetry/api/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/lib/constants.js":
/*!*************************************************!*\
  !*** ./node_modules/next/dist/lib/constants.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    ACTION_SUFFIX: function() {\n        return ACTION_SUFFIX;\n    },\n    APP_DIR_ALIAS: function() {\n        return APP_DIR_ALIAS;\n    },\n    CACHE_ONE_YEAR: function() {\n        return CACHE_ONE_YEAR;\n    },\n    DOT_NEXT_ALIAS: function() {\n        return DOT_NEXT_ALIAS;\n    },\n    ESLINT_DEFAULT_DIRS: function() {\n        return ESLINT_DEFAULT_DIRS;\n    },\n    GSP_NO_RETURNED_VALUE: function() {\n        return GSP_NO_RETURNED_VALUE;\n    },\n    GSSP_COMPONENT_MEMBER_ERROR: function() {\n        return GSSP_COMPONENT_MEMBER_ERROR;\n    },\n    GSSP_NO_RETURNED_VALUE: function() {\n        return GSSP_NO_RETURNED_VALUE;\n    },\n    INSTRUMENTATION_HOOK_FILENAME: function() {\n        return INSTRUMENTATION_HOOK_FILENAME;\n    },\n    MIDDLEWARE_FILENAME: function() {\n        return MIDDLEWARE_FILENAME;\n    },\n    MIDDLEWARE_LOCATION_REGEXP: function() {\n        return MIDDLEWARE_LOCATION_REGEXP;\n    },\n    NEXT_BODY_SUFFIX: function() {\n        return NEXT_BODY_SUFFIX;\n    },\n    NEXT_CACHE_IMPLICIT_TAG_ID: function() {\n        return NEXT_CACHE_IMPLICIT_TAG_ID;\n    },\n    NEXT_CACHE_REVALIDATED_TAGS_HEADER: function() {\n        return NEXT_CACHE_REVALIDATED_TAGS_HEADER;\n    },\n    NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER: function() {\n        return NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER;\n    },\n    NEXT_CACHE_SOFT_TAGS_HEADER: function() {\n        return NEXT_CACHE_SOFT_TAGS_HEADER;\n    },\n    NEXT_CACHE_SOFT_TAG_MAX_LENGTH: function() {\n        return NEXT_CACHE_SOFT_TAG_MAX_LENGTH;\n    },\n    NEXT_CACHE_TAGS_HEADER: function() {\n        return NEXT_CACHE_TAGS_HEADER;\n    },\n    NEXT_CACHE_TAG_MAX_ITEMS: function() {\n        return NEXT_CACHE_TAG_MAX_ITEMS;\n    },\n    NEXT_CACHE_TAG_MAX_LENGTH: function() {\n        return NEXT_CACHE_TAG_MAX_LENGTH;\n    },\n    NEXT_DATA_SUFFIX: function() {\n        return NEXT_DATA_SUFFIX;\n    },\n    NEXT_INTERCEPTION_MARKER_PREFIX: function() {\n        return NEXT_INTERCEPTION_MARKER_PREFIX;\n    },\n    NEXT_META_SUFFIX: function() {\n        return NEXT_META_SUFFIX;\n    },\n    NEXT_QUERY_PARAM_PREFIX: function() {\n        return NEXT_QUERY_PARAM_PREFIX;\n    },\n    NON_STANDARD_NODE_ENV: function() {\n        return NON_STANDARD_NODE_ENV;\n    },\n    PAGES_DIR_ALIAS: function() {\n        return PAGES_DIR_ALIAS;\n    },\n    PRERENDER_REVALIDATE_HEADER: function() {\n        return PRERENDER_REVALIDATE_HEADER;\n    },\n    PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER: function() {\n        return PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER;\n    },\n    PUBLIC_DIR_MIDDLEWARE_CONFLICT: function() {\n        return PUBLIC_DIR_MIDDLEWARE_CONFLICT;\n    },\n    ROOT_DIR_ALIAS: function() {\n        return ROOT_DIR_ALIAS;\n    },\n    RSC_ACTION_CLIENT_WRAPPER_ALIAS: function() {\n        return RSC_ACTION_CLIENT_WRAPPER_ALIAS;\n    },\n    RSC_ACTION_ENCRYPTION_ALIAS: function() {\n        return RSC_ACTION_ENCRYPTION_ALIAS;\n    },\n    RSC_ACTION_PROXY_ALIAS: function() {\n        return RSC_ACTION_PROXY_ALIAS;\n    },\n    RSC_ACTION_VALIDATE_ALIAS: function() {\n        return RSC_ACTION_VALIDATE_ALIAS;\n    },\n    RSC_MOD_REF_PROXY_ALIAS: function() {\n        return RSC_MOD_REF_PROXY_ALIAS;\n    },\n    RSC_PREFETCH_SUFFIX: function() {\n        return RSC_PREFETCH_SUFFIX;\n    },\n    RSC_SUFFIX: function() {\n        return RSC_SUFFIX;\n    },\n    SERVER_PROPS_EXPORT_ERROR: function() {\n        return SERVER_PROPS_EXPORT_ERROR;\n    },\n    SERVER_PROPS_GET_INIT_PROPS_CONFLICT: function() {\n        return SERVER_PROPS_GET_INIT_PROPS_CONFLICT;\n    },\n    SERVER_PROPS_SSG_CONFLICT: function() {\n        return SERVER_PROPS_SSG_CONFLICT;\n    },\n    SERVER_RUNTIME: function() {\n        return SERVER_RUNTIME;\n    },\n    SSG_FALLBACK_EXPORT_ERROR: function() {\n        return SSG_FALLBACK_EXPORT_ERROR;\n    },\n    SSG_GET_INITIAL_PROPS_CONFLICT: function() {\n        return SSG_GET_INITIAL_PROPS_CONFLICT;\n    },\n    STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR: function() {\n        return STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR;\n    },\n    UNSTABLE_REVALIDATE_RENAME_ERROR: function() {\n        return UNSTABLE_REVALIDATE_RENAME_ERROR;\n    },\n    WEBPACK_LAYERS: function() {\n        return WEBPACK_LAYERS;\n    },\n    WEBPACK_RESOURCE_QUERIES: function() {\n        return WEBPACK_RESOURCE_QUERIES;\n    }\n});\nconst NEXT_QUERY_PARAM_PREFIX = \"nxtP\";\nconst NEXT_INTERCEPTION_MARKER_PREFIX = \"nxtI\";\nconst PRERENDER_REVALIDATE_HEADER = \"x-prerender-revalidate\";\nconst PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER = \"x-prerender-revalidate-if-generated\";\nconst RSC_PREFETCH_SUFFIX = \".prefetch.rsc\";\nconst RSC_SUFFIX = \".rsc\";\nconst ACTION_SUFFIX = \".action\";\nconst NEXT_DATA_SUFFIX = \".json\";\nconst NEXT_META_SUFFIX = \".meta\";\nconst NEXT_BODY_SUFFIX = \".body\";\nconst NEXT_CACHE_TAGS_HEADER = \"x-next-cache-tags\";\nconst NEXT_CACHE_SOFT_TAGS_HEADER = \"x-next-cache-soft-tags\";\nconst NEXT_CACHE_REVALIDATED_TAGS_HEADER = \"x-next-revalidated-tags\";\nconst NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER = \"x-next-revalidate-tag-token\";\nconst NEXT_CACHE_TAG_MAX_ITEMS = 128;\nconst NEXT_CACHE_TAG_MAX_LENGTH = 256;\nconst NEXT_CACHE_SOFT_TAG_MAX_LENGTH = 1024;\nconst NEXT_CACHE_IMPLICIT_TAG_ID = \"_N_T_\";\nconst CACHE_ONE_YEAR = 31536000;\nconst MIDDLEWARE_FILENAME = \"middleware\";\nconst MIDDLEWARE_LOCATION_REGEXP = `(?:src/)?${MIDDLEWARE_FILENAME}`;\nconst INSTRUMENTATION_HOOK_FILENAME = \"instrumentation\";\nconst PAGES_DIR_ALIAS = \"private-next-pages\";\nconst DOT_NEXT_ALIAS = \"private-dot-next\";\nconst ROOT_DIR_ALIAS = \"private-next-root-dir\";\nconst APP_DIR_ALIAS = \"private-next-app-dir\";\nconst RSC_MOD_REF_PROXY_ALIAS = \"next/dist/build/webpack/loaders/next-flight-loader/module-proxy\";\nconst RSC_ACTION_VALIDATE_ALIAS = \"private-next-rsc-action-validate\";\nconst RSC_ACTION_PROXY_ALIAS = \"private-next-rsc-server-reference\";\nconst RSC_ACTION_ENCRYPTION_ALIAS = \"private-next-rsc-action-encryption\";\nconst RSC_ACTION_CLIENT_WRAPPER_ALIAS = \"private-next-rsc-action-client-wrapper\";\nconst PUBLIC_DIR_MIDDLEWARE_CONFLICT = `You can not have a '_next' folder inside of your public folder. This conflicts with the internal '/_next' route. https://nextjs.org/docs/messages/public-next-folder-conflict`;\nconst SSG_GET_INITIAL_PROPS_CONFLICT = `You can not use getInitialProps with getStaticProps. To use SSG, please remove your getInitialProps`;\nconst SERVER_PROPS_GET_INIT_PROPS_CONFLICT = `You can not use getInitialProps with getServerSideProps. Please remove getInitialProps.`;\nconst SERVER_PROPS_SSG_CONFLICT = `You can not use getStaticProps or getStaticPaths with getServerSideProps. To use SSG, please remove getServerSideProps`;\nconst STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR = `can not have getInitialProps/getServerSideProps, https://nextjs.org/docs/messages/404-get-initial-props`;\nconst SERVER_PROPS_EXPORT_ERROR = `pages with \\`getServerSideProps\\` can not be exported. See more info here: https://nextjs.org/docs/messages/gssp-export`;\nconst GSP_NO_RETURNED_VALUE = \"Your `getStaticProps` function did not return an object. Did you forget to add a `return`?\";\nconst GSSP_NO_RETURNED_VALUE = \"Your `getServerSideProps` function did not return an object. Did you forget to add a `return`?\";\nconst UNSTABLE_REVALIDATE_RENAME_ERROR = \"The `unstable_revalidate` property is available for general use.\\n\" + \"Please use `revalidate` instead.\";\nconst GSSP_COMPONENT_MEMBER_ERROR = `can not be attached to a page's component and must be exported from the page. See more info here: https://nextjs.org/docs/messages/gssp-component-member`;\nconst NON_STANDARD_NODE_ENV = `You are using a non-standard \"NODE_ENV\" value in your environment. This creates inconsistencies in the project and is strongly advised against. Read more: https://nextjs.org/docs/messages/non-standard-node-env`;\nconst SSG_FALLBACK_EXPORT_ERROR = `Pages with \\`fallback\\` enabled in \\`getStaticPaths\\` can not be exported. See more info here: https://nextjs.org/docs/messages/ssg-fallback-true-export`;\nconst ESLINT_DEFAULT_DIRS = [\n    \"app\",\n    \"pages\",\n    \"components\",\n    \"lib\",\n    \"src\"\n];\nconst SERVER_RUNTIME = {\n    edge: \"edge\",\n    experimentalEdge: \"experimental-edge\",\n    nodejs: \"nodejs\"\n};\n/**\n * The names of the webpack layers. These layers are the primitives for the\n * webpack chunks.\n */ const WEBPACK_LAYERS_NAMES = {\n    /**\n   * The layer for the shared code between the client and server bundles.\n   */ shared: \"shared\",\n    /**\n   * React Server Components layer (rsc).\n   */ reactServerComponents: \"rsc\",\n    /**\n   * Server Side Rendering layer for app (ssr).\n   */ serverSideRendering: \"ssr\",\n    /**\n   * The browser client bundle layer for actions.\n   */ actionBrowser: \"action-browser\",\n    /**\n   * The layer for the API routes.\n   */ api: \"api\",\n    /**\n   * The layer for the middleware code.\n   */ middleware: \"middleware\",\n    /**\n   * The layer for the instrumentation hooks.\n   */ instrument: \"instrument\",\n    /**\n   * The layer for assets on the edge.\n   */ edgeAsset: \"edge-asset\",\n    /**\n   * The browser client bundle layer for App directory.\n   */ appPagesBrowser: \"app-pages-browser\",\n    /**\n   * The server bundle layer for metadata routes.\n   */ appMetadataRoute: \"app-metadata-route\",\n    /**\n   * The layer for the server bundle for App Route handlers.\n   */ appRouteHandler: \"app-route-handler\"\n};\nconst WEBPACK_LAYERS = {\n    ...WEBPACK_LAYERS_NAMES,\n    GROUP: {\n        serverOnly: [\n            WEBPACK_LAYERS_NAMES.reactServerComponents,\n            WEBPACK_LAYERS_NAMES.actionBrowser,\n            WEBPACK_LAYERS_NAMES.appMetadataRoute,\n            WEBPACK_LAYERS_NAMES.appRouteHandler,\n            WEBPACK_LAYERS_NAMES.instrument\n        ],\n        clientOnly: [\n            WEBPACK_LAYERS_NAMES.serverSideRendering,\n            WEBPACK_LAYERS_NAMES.appPagesBrowser\n        ],\n        nonClientServerTarget: [\n            // middleware and pages api\n            WEBPACK_LAYERS_NAMES.middleware,\n            WEBPACK_LAYERS_NAMES.api\n        ],\n        app: [\n            WEBPACK_LAYERS_NAMES.reactServerComponents,\n            WEBPACK_LAYERS_NAMES.actionBrowser,\n            WEBPACK_LAYERS_NAMES.appMetadataRoute,\n            WEBPACK_LAYERS_NAMES.appRouteHandler,\n            WEBPACK_LAYERS_NAMES.serverSideRendering,\n            WEBPACK_LAYERS_NAMES.appPagesBrowser,\n            WEBPACK_LAYERS_NAMES.shared,\n            WEBPACK_LAYERS_NAMES.instrument\n        ]\n    }\n};\nconst WEBPACK_RESOURCE_QUERIES = {\n    edgeSSREntry: \"__next_edge_ssr_entry__\",\n    metadata: \"__next_metadata__\",\n    metadataRoute: \"__next_metadata_route__\",\n    metadataImageMeta: \"__next_metadata_image_meta__\"\n};\n\n//# sourceMappingURL=constants.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/lib/constants.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/lib/picocolors.js":
/*!**************************************************!*\
  !*** ./node_modules/next/dist/lib/picocolors.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("// ISC License\n// Copyright (c) 2021 Alexey Raspopov, Kostiantyn Denysov, Anton Verinov\n// Permission to use, copy, modify, and/or distribute this software for any\n// purpose with or without fee is hereby granted, provided that the above\n// copyright notice and this permission notice appear in all copies.\n// THE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES\n// WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF\n// MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR\n// ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES\n// WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN\n// ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF\n// OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.\n//\n// https://github.com/alexeyraspopov/picocolors/blob/b6261487e7b81aaab2440e397a356732cad9e342/picocolors.js#L1\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    bgBlack: function() {\n        return bgBlack;\n    },\n    bgBlue: function() {\n        return bgBlue;\n    },\n    bgCyan: function() {\n        return bgCyan;\n    },\n    bgGreen: function() {\n        return bgGreen;\n    },\n    bgMagenta: function() {\n        return bgMagenta;\n    },\n    bgRed: function() {\n        return bgRed;\n    },\n    bgWhite: function() {\n        return bgWhite;\n    },\n    bgYellow: function() {\n        return bgYellow;\n    },\n    black: function() {\n        return black;\n    },\n    blue: function() {\n        return blue;\n    },\n    bold: function() {\n        return bold;\n    },\n    cyan: function() {\n        return cyan;\n    },\n    dim: function() {\n        return dim;\n    },\n    gray: function() {\n        return gray;\n    },\n    green: function() {\n        return green;\n    },\n    hidden: function() {\n        return hidden;\n    },\n    inverse: function() {\n        return inverse;\n    },\n    italic: function() {\n        return italic;\n    },\n    magenta: function() {\n        return magenta;\n    },\n    purple: function() {\n        return purple;\n    },\n    red: function() {\n        return red;\n    },\n    reset: function() {\n        return reset;\n    },\n    strikethrough: function() {\n        return strikethrough;\n    },\n    underline: function() {\n        return underline;\n    },\n    white: function() {\n        return white;\n    },\n    yellow: function() {\n        return yellow;\n    }\n});\nvar _globalThis;\nconst { env, stdout } = ((_globalThis = globalThis) == null ? void 0 : _globalThis.process) ?? {};\nconst enabled = env && !env.NO_COLOR && (env.FORCE_COLOR || (stdout == null ? void 0 : stdout.isTTY) && !env.CI && env.TERM !== \"dumb\");\nconst replaceClose = (str, close, replace, index)=>{\n    const start = str.substring(0, index) + replace;\n    const end = str.substring(index + close.length);\n    const nextIndex = end.indexOf(close);\n    return ~nextIndex ? start + replaceClose(end, close, replace, nextIndex) : start + end;\n};\nconst formatter = (open, close, replace = open)=>{\n    if (!enabled) return String;\n    return (input)=>{\n        const string = \"\" + input;\n        const index = string.indexOf(close, open.length);\n        return ~index ? open + replaceClose(string, close, replace, index) + close : open + string + close;\n    };\n};\nconst reset = enabled ? (s)=>`\\x1b[0m${s}\\x1b[0m` : String;\nconst bold = formatter(\"\\x1b[1m\", \"\\x1b[22m\", \"\\x1b[22m\\x1b[1m\");\nconst dim = formatter(\"\\x1b[2m\", \"\\x1b[22m\", \"\\x1b[22m\\x1b[2m\");\nconst italic = formatter(\"\\x1b[3m\", \"\\x1b[23m\");\nconst underline = formatter(\"\\x1b[4m\", \"\\x1b[24m\");\nconst inverse = formatter(\"\\x1b[7m\", \"\\x1b[27m\");\nconst hidden = formatter(\"\\x1b[8m\", \"\\x1b[28m\");\nconst strikethrough = formatter(\"\\x1b[9m\", \"\\x1b[29m\");\nconst black = formatter(\"\\x1b[30m\", \"\\x1b[39m\");\nconst red = formatter(\"\\x1b[31m\", \"\\x1b[39m\");\nconst green = formatter(\"\\x1b[32m\", \"\\x1b[39m\");\nconst yellow = formatter(\"\\x1b[33m\", \"\\x1b[39m\");\nconst blue = formatter(\"\\x1b[34m\", \"\\x1b[39m\");\nconst magenta = formatter(\"\\x1b[35m\", \"\\x1b[39m\");\nconst purple = formatter(\"\\x1b[38;2;173;127;168m\", \"\\x1b[39m\");\nconst cyan = formatter(\"\\x1b[36m\", \"\\x1b[39m\");\nconst white = formatter(\"\\x1b[37m\", \"\\x1b[39m\");\nconst gray = formatter(\"\\x1b[90m\", \"\\x1b[39m\");\nconst bgBlack = formatter(\"\\x1b[40m\", \"\\x1b[49m\");\nconst bgRed = formatter(\"\\x1b[41m\", \"\\x1b[49m\");\nconst bgGreen = formatter(\"\\x1b[42m\", \"\\x1b[49m\");\nconst bgYellow = formatter(\"\\x1b[43m\", \"\\x1b[49m\");\nconst bgBlue = formatter(\"\\x1b[44m\", \"\\x1b[49m\");\nconst bgMagenta = formatter(\"\\x1b[45m\", \"\\x1b[49m\");\nconst bgCyan = formatter(\"\\x1b[46m\", \"\\x1b[49m\");\nconst bgWhite = formatter(\"\\x1b[47m\", \"\\x1b[49m\");\n\n//# sourceMappingURL=picocolors.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/lib/picocolors.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/lib/url.js":
/*!*******************************************!*\
  !*** ./node_modules/next/dist/lib/url.js ***!
  \*******************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    getPathname: function() {\n        return getPathname;\n    },\n    isFullStringUrl: function() {\n        return isFullStringUrl;\n    },\n    parseUrl: function() {\n        return parseUrl;\n    }\n});\nconst DUMMY_ORIGIN = \"http://n\";\nfunction getUrlWithoutHost(url) {\n    return new URL(url, DUMMY_ORIGIN);\n}\nfunction getPathname(url) {\n    return getUrlWithoutHost(url).pathname;\n}\nfunction isFullStringUrl(url) {\n    return /https?:\\/\\//.test(url);\n}\nfunction parseUrl(url) {\n    let parsed = undefined;\n    try {\n        parsed = new URL(url, DUMMY_ORIGIN);\n    } catch  {}\n    return parsed;\n}\n\n//# sourceMappingURL=url.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/lib/url.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/server/app-render/dynamic-rendering.js":
/*!***********************************************************************!*\
  !*** ./node_modules/next/dist/server/app-render/dynamic-rendering.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("/**\n * The functions provided by this module are used to communicate certain properties\n * about the currently running code so that Next.js can make decisions on how to handle\n * the current execution in different rendering modes such as pre-rendering, resuming, and SSR.\n *\n * Today Next.js treats all code as potentially static. Certain APIs may only make sense when dynamically rendering.\n * Traditionally this meant deopting the entire render to dynamic however with PPR we can now deopt parts\n * of a React tree as dynamic while still keeping other parts static. There are really two different kinds of\n * Dynamic indications.\n *\n * The first is simply an intention to be dynamic. unstable_noStore is an example of this where\n * the currently executing code simply declares that the current scope is dynamic but if you use it\n * inside unstable_cache it can still be cached. This type of indication can be removed if we ever\n * make the default dynamic to begin with because the only way you would ever be static is inside\n * a cache scope which this indication does not affect.\n *\n * The second is an indication that a dynamic data source was read. This is a stronger form of dynamic\n * because it means that it is inappropriate to cache this at all. using a dynamic data source inside\n * unstable_cache should error. If you want to use some dynamic data inside unstable_cache you should\n * read that data outside the cache and pass it in as an argument to the cached function.\n */ // Once postpone is in stable we should switch to importing the postpone export directly\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    Postpone: function() {\n        return Postpone;\n    },\n    createPostponedAbortSignal: function() {\n        return createPostponedAbortSignal;\n    },\n    createPrerenderState: function() {\n        return createPrerenderState;\n    },\n    formatDynamicAPIAccesses: function() {\n        return formatDynamicAPIAccesses;\n    },\n    markCurrentScopeAsDynamic: function() {\n        return markCurrentScopeAsDynamic;\n    },\n    trackDynamicDataAccessed: function() {\n        return trackDynamicDataAccessed;\n    },\n    trackDynamicFetch: function() {\n        return trackDynamicFetch;\n    },\n    usedDynamicAPIs: function() {\n        return usedDynamicAPIs;\n    }\n});\nconst _react = /*#__PURE__*/ _interop_require_default(__webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\"));\nconst _hooksservercontext = __webpack_require__(/*! ../../client/components/hooks-server-context */ \"(rsc)/./node_modules/next/dist/client/components/hooks-server-context.js\");\nconst _staticgenerationbailout = __webpack_require__(/*! ../../client/components/static-generation-bailout */ \"(rsc)/./node_modules/next/dist/client/components/static-generation-bailout.js\");\nconst _url = __webpack_require__(/*! ../../lib/url */ \"(rsc)/./node_modules/next/dist/lib/url.js\");\nfunction _interop_require_default(obj) {\n    return obj && obj.__esModule ? obj : {\n        default: obj\n    };\n}\nconst hasPostpone = typeof _react.default.unstable_postpone === \"function\";\nfunction createPrerenderState(isDebugSkeleton) {\n    return {\n        isDebugSkeleton,\n        dynamicAccesses: []\n    };\n}\nfunction markCurrentScopeAsDynamic(store, expression) {\n    const pathname = (0, _url.getPathname)(store.urlPathname);\n    if (store.isUnstableCacheCallback) {\n        // inside cache scopes marking a scope as dynamic has no effect because the outer cache scope\n        // creates a cache boundary. This is subtly different from reading a dynamic data source which is\n        // forbidden inside a cache scope.\n        return;\n    } else if (store.dynamicShouldError) {\n        throw new _staticgenerationbailout.StaticGenBailoutError(`Route ${pathname} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`);\n    } else if (// We are in a prerender (PPR enabled, during build)\n    store.prerenderState) {\n        // We track that we had a dynamic scope that postponed.\n        // This will be used by the renderer to decide whether\n        // the prerender requires a resume\n        postponeWithTracking(store.prerenderState, expression, pathname);\n    } else {\n        store.revalidate = 0;\n        if (store.isStaticGeneration) {\n            // We aren't prerendering but we are generating a static page. We need to bail out of static generation\n            const err = new _hooksservercontext.DynamicServerError(`Route ${pathname} couldn't be rendered statically because it used ${expression}. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`);\n            store.dynamicUsageDescription = expression;\n            store.dynamicUsageStack = err.stack;\n            throw err;\n        }\n    }\n}\nfunction trackDynamicDataAccessed(store, expression) {\n    const pathname = (0, _url.getPathname)(store.urlPathname);\n    if (store.isUnstableCacheCallback) {\n        throw new Error(`Route ${pathname} used \"${expression}\" inside a function cached with \"unstable_cache(...)\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"${expression}\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`);\n    } else if (store.dynamicShouldError) {\n        throw new _staticgenerationbailout.StaticGenBailoutError(`Route ${pathname} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`);\n    } else if (// We are in a prerender (PPR enabled, during build)\n    store.prerenderState) {\n        // We track that we had a dynamic scope that postponed.\n        // This will be used by the renderer to decide whether\n        // the prerender requires a resume\n        postponeWithTracking(store.prerenderState, expression, pathname);\n    } else {\n        store.revalidate = 0;\n        if (store.isStaticGeneration) {\n            // We aren't prerendering but we are generating a static page. We need to bail out of static generation\n            const err = new _hooksservercontext.DynamicServerError(`Route ${pathname} couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`);\n            store.dynamicUsageDescription = expression;\n            store.dynamicUsageStack = err.stack;\n            throw err;\n        }\n    }\n}\nfunction Postpone({ reason, prerenderState, pathname }) {\n    postponeWithTracking(prerenderState, reason, pathname);\n}\nfunction trackDynamicFetch(store, expression) {\n    if (store.prerenderState) {\n        postponeWithTracking(store.prerenderState, expression, store.urlPathname);\n    }\n}\nfunction postponeWithTracking(prerenderState, expression, pathname) {\n    assertPostpone();\n    const reason = `Route ${pathname} needs to bail out of prerendering at this point because it used ${expression}. ` + `React throws this special object to indicate where. It should not be caught by ` + `your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error`;\n    prerenderState.dynamicAccesses.push({\n        // When we aren't debugging, we don't need to create another error for the\n        // stack trace.\n        stack: prerenderState.isDebugSkeleton ? new Error().stack : undefined,\n        expression\n    });\n    _react.default.unstable_postpone(reason);\n}\nfunction usedDynamicAPIs(prerenderState) {\n    return prerenderState.dynamicAccesses.length > 0;\n}\nfunction formatDynamicAPIAccesses(prerenderState) {\n    return prerenderState.dynamicAccesses.filter((access)=>typeof access.stack === \"string\" && access.stack.length > 0).map(({ expression, stack })=>{\n        stack = stack.split(\"\\n\")// Remove the \"Error: \" prefix from the first line of the stack trace as\n        // well as the first 4 lines of the stack trace which is the distance\n        // from the user code and the `new Error().stack` call.\n        .slice(4).filter((line)=>{\n            // Exclude Next.js internals from the stack trace.\n            if (line.includes(\"node_modules/next/\")) {\n                return false;\n            }\n            // Exclude anonymous functions from the stack trace.\n            if (line.includes(\" (<anonymous>)\")) {\n                return false;\n            }\n            // Exclude Node.js internals from the stack trace.\n            if (line.includes(\" (node:\")) {\n                return false;\n            }\n            return true;\n        }).join(\"\\n\");\n        return `Dynamic API Usage Debug - ${expression}:\\n${stack}`;\n    });\n}\nfunction assertPostpone() {\n    if (!hasPostpone) {\n        throw new Error(`Invariant: React.unstable_postpone is not defined. This suggests the wrong version of React was loaded. This is a bug in Next.js`);\n    }\n}\nfunction createPostponedAbortSignal(reason) {\n    assertPostpone();\n    const controller = new AbortController();\n    // We get our hands on a postpone instance by calling postpone and catching the throw\n    try {\n        _react.default.unstable_postpone(reason);\n    } catch (x) {\n        controller.abort(x);\n    }\n    return controller.signal;\n}\n\n//# sourceMappingURL=dynamic-rendering.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/server/app-render/dynamic-rendering.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/server/future/route-kind.js":
/*!************************************************************!*\
  !*** ./node_modules/next/dist/server/future/route-kind.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"RouteKind\", ({\n    enumerable: true,\n    get: function() {\n        return RouteKind;\n    }\n}));\nvar RouteKind;\n(function(RouteKind) {\n    /**\n   * `PAGES` represents all the React pages that are under `pages/`.\n   */ RouteKind[\"PAGES\"] = \"PAGES\";\n    /**\n   * `PAGES_API` represents all the API routes under `pages/api/`.\n   */ RouteKind[\"PAGES_API\"] = \"PAGES_API\";\n    /**\n   * `APP_PAGE` represents all the React pages that are under `app/` with the\n   * filename of `page.{j,t}s{,x}`.\n   */ RouteKind[\"APP_PAGE\"] = \"APP_PAGE\";\n    /**\n   * `APP_ROUTE` represents all the API routes and metadata routes that are under `app/` with the\n   * filename of `route.{j,t}s{,x}`.\n   */ RouteKind[\"APP_ROUTE\"] = \"APP_ROUTE\";\n})(RouteKind || (RouteKind = {}));\n\n//# sourceMappingURL=route-kind.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUta2luZC5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QztBQUM3QztBQUNBLENBQUMsRUFBQztBQUNGLDZDQUE0QztBQUM1QztBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsRUFBQztBQUNGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLElBQUksRUFBRSxHQUFHO0FBQ2pDO0FBQ0E7QUFDQTtBQUNBLHlCQUF5QixJQUFJLEVBQUUsR0FBRztBQUNsQztBQUNBLENBQUMsOEJBQThCOztBQUUvQiIsInNvdXJjZXMiOlsid2VicGFjazovL3Nyc3ItcHJvcGVydHktbWFuYWdlbWVudC1iYWNrZW5kLy4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLWtpbmQuanM/MDM3MCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwge1xuICAgIHZhbHVlOiB0cnVlXG59KTtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIlJvdXRlS2luZFwiLCB7XG4gICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICBnZXQ6IGZ1bmN0aW9uKCkge1xuICAgICAgICByZXR1cm4gUm91dGVLaW5kO1xuICAgIH1cbn0pO1xudmFyIFJvdXRlS2luZDtcbihmdW5jdGlvbihSb3V0ZUtpbmQpIHtcbiAgICAvKipcbiAgICogYFBBR0VTYCByZXByZXNlbnRzIGFsbCB0aGUgUmVhY3QgcGFnZXMgdGhhdCBhcmUgdW5kZXIgYHBhZ2VzL2AuXG4gICAqLyBSb3V0ZUtpbmRbXCJQQUdFU1wiXSA9IFwiUEFHRVNcIjtcbiAgICAvKipcbiAgICogYFBBR0VTX0FQSWAgcmVwcmVzZW50cyBhbGwgdGhlIEFQSSByb3V0ZXMgdW5kZXIgYHBhZ2VzL2FwaS9gLlxuICAgKi8gUm91dGVLaW5kW1wiUEFHRVNfQVBJXCJdID0gXCJQQUdFU19BUElcIjtcbiAgICAvKipcbiAgICogYEFQUF9QQUdFYCByZXByZXNlbnRzIGFsbCB0aGUgUmVhY3QgcGFnZXMgdGhhdCBhcmUgdW5kZXIgYGFwcC9gIHdpdGggdGhlXG4gICAqIGZpbGVuYW1lIG9mIGBwYWdlLntqLHR9c3sseH1gLlxuICAgKi8gUm91dGVLaW5kW1wiQVBQX1BBR0VcIl0gPSBcIkFQUF9QQUdFXCI7XG4gICAgLyoqXG4gICAqIGBBUFBfUk9VVEVgIHJlcHJlc2VudHMgYWxsIHRoZSBBUEkgcm91dGVzIGFuZCBtZXRhZGF0YSByb3V0ZXMgdGhhdCBhcmUgdW5kZXIgYGFwcC9gIHdpdGggdGhlXG4gICAqIGZpbGVuYW1lIG9mIGByb3V0ZS57aix0fXN7LHh9YC5cbiAgICovIFJvdXRlS2luZFtcIkFQUF9ST1VURVwiXSA9IFwiQVBQX1JPVVRFXCI7XG59KShSb3V0ZUtpbmQgfHwgKFJvdXRlS2luZCA9IHt9KSk7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXJvdXRlLWtpbmQuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/server/future/route-kind.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js ***!
  \****************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\nif (false) {} else {\n    if (false) {} else {\n        if (true) {\n            module.exports = __webpack_require__(/*! next/dist/compiled/next-server/app-page.runtime.dev.js */ \"next/dist/compiled/next-server/app-page.runtime.dev.js\");\n        } else {}\n    }\n}\n\n//# sourceMappingURL=module.compiled.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js ***!
  \*******************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\nmodule.exports = __webpack_require__(/*! ../../module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js\").vendored[\"react-rsc\"].React;\n\n//# sourceMappingURL=react.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUtbW9kdWxlcy9hcHAtcGFnZS92ZW5kb3JlZC9yc2MvcmVhY3QuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYix1TEFBNkU7O0FBRTdFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3Jzci1wcm9wZXJ0eS1tYW5hZ2VtZW50LWJhY2tlbmQvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUtbW9kdWxlcy9hcHAtcGFnZS92ZW5kb3JlZC9yc2MvcmVhY3QuanM/NTlmOCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbm1vZHVsZS5leHBvcnRzID0gcmVxdWlyZShcIi4uLy4uL21vZHVsZS5jb21waWxlZFwiKS52ZW5kb3JlZFtcInJlYWN0LXJzY1wiXS5SZWFjdDtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cmVhY3QuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js ***!
  \*****************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\nif (false) {} else {\n    if (false) {} else {\n        if (true) {\n            module.exports = __webpack_require__(/*! next/dist/compiled/next-server/app-route.runtime.dev.js */ \"next/dist/compiled/next-server/app-route.runtime.dev.js\");\n        } else {}\n    }\n}\n\n//# sourceMappingURL=module.compiled.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/server/lib/clone-response.js":
/*!*************************************************************!*\
  !*** ./node_modules/next/dist/server/lib/clone-response.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("/**\n * Clones a response by teeing the body so we can return two independent\n * ReadableStreams from it. This avoids the bug in the undici library around\n * response cloning.\n *\n * After cloning, the original response's body will be consumed and closed.\n *\n * @see https://github.com/vercel/next.js/pull/73274\n *\n * @param original - The original response to clone.\n * @returns A tuple containing two independent clones of the original response.\n */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"cloneResponse\", ({\n    enumerable: true,\n    get: function() {\n        return cloneResponse;\n    }\n}));\nfunction cloneResponse(original) {\n    // If the response has no body, then we can just return the original response\n    // twice because it's immutable.\n    if (!original.body) {\n        return [\n            original,\n            original\n        ];\n    }\n    const [body1, body2] = original.body.tee();\n    const cloned1 = new Response(body1, {\n        status: original.status,\n        statusText: original.statusText,\n        headers: original.headers\n    });\n    Object.defineProperty(cloned1, \"url\", {\n        value: original.url\n    });\n    const cloned2 = new Response(body2, {\n        status: original.status,\n        statusText: original.statusText,\n        headers: original.headers\n    });\n    Object.defineProperty(cloned2, \"url\", {\n        value: original.url\n    });\n    return [\n        cloned1,\n        cloned2\n    ];\n}\n\n//# sourceMappingURL=clone-response.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NlcnZlci9saWIvY2xvbmUtcmVzcG9uc2UuanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQWlCO0FBQ2pCLDhDQUE2QztBQUM3QztBQUNBLENBQUMsRUFBQztBQUNGLGlEQUFnRDtBQUNoRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsRUFBQztBQUNGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zcnNyLXByb3BlcnR5LW1hbmFnZW1lbnQtYmFja2VuZC8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2VydmVyL2xpYi9jbG9uZS1yZXNwb25zZS5qcz80ZWUwIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQ2xvbmVzIGEgcmVzcG9uc2UgYnkgdGVlaW5nIHRoZSBib2R5IHNvIHdlIGNhbiByZXR1cm4gdHdvIGluZGVwZW5kZW50XG4gKiBSZWFkYWJsZVN0cmVhbXMgZnJvbSBpdC4gVGhpcyBhdm9pZHMgdGhlIGJ1ZyBpbiB0aGUgdW5kaWNpIGxpYnJhcnkgYXJvdW5kXG4gKiByZXNwb25zZSBjbG9uaW5nLlxuICpcbiAqIEFmdGVyIGNsb25pbmcsIHRoZSBvcmlnaW5hbCByZXNwb25zZSdzIGJvZHkgd2lsbCBiZSBjb25zdW1lZCBhbmQgY2xvc2VkLlxuICpcbiAqIEBzZWUgaHR0cHM6Ly9naXRodWIuY29tL3ZlcmNlbC9uZXh0LmpzL3B1bGwvNzMyNzRcbiAqXG4gKiBAcGFyYW0gb3JpZ2luYWwgLSBUaGUgb3JpZ2luYWwgcmVzcG9uc2UgdG8gY2xvbmUuXG4gKiBAcmV0dXJucyBBIHR1cGxlIGNvbnRhaW5pbmcgdHdvIGluZGVwZW5kZW50IGNsb25lcyBvZiB0aGUgb3JpZ2luYWwgcmVzcG9uc2UuXG4gKi8gXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHtcbiAgICB2YWx1ZTogdHJ1ZVxufSk7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJjbG9uZVJlc3BvbnNlXCIsIHtcbiAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgIGdldDogZnVuY3Rpb24oKSB7XG4gICAgICAgIHJldHVybiBjbG9uZVJlc3BvbnNlO1xuICAgIH1cbn0pO1xuZnVuY3Rpb24gY2xvbmVSZXNwb25zZShvcmlnaW5hbCkge1xuICAgIC8vIElmIHRoZSByZXNwb25zZSBoYXMgbm8gYm9keSwgdGhlbiB3ZSBjYW4ganVzdCByZXR1cm4gdGhlIG9yaWdpbmFsIHJlc3BvbnNlXG4gICAgLy8gdHdpY2UgYmVjYXVzZSBpdCdzIGltbXV0YWJsZS5cbiAgICBpZiAoIW9yaWdpbmFsLmJvZHkpIHtcbiAgICAgICAgcmV0dXJuIFtcbiAgICAgICAgICAgIG9yaWdpbmFsLFxuICAgICAgICAgICAgb3JpZ2luYWxcbiAgICAgICAgXTtcbiAgICB9XG4gICAgY29uc3QgW2JvZHkxLCBib2R5Ml0gPSBvcmlnaW5hbC5ib2R5LnRlZSgpO1xuICAgIGNvbnN0IGNsb25lZDEgPSBuZXcgUmVzcG9uc2UoYm9keTEsIHtcbiAgICAgICAgc3RhdHVzOiBvcmlnaW5hbC5zdGF0dXMsXG4gICAgICAgIHN0YXR1c1RleHQ6IG9yaWdpbmFsLnN0YXR1c1RleHQsXG4gICAgICAgIGhlYWRlcnM6IG9yaWdpbmFsLmhlYWRlcnNcbiAgICB9KTtcbiAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkoY2xvbmVkMSwgXCJ1cmxcIiwge1xuICAgICAgICB2YWx1ZTogb3JpZ2luYWwudXJsXG4gICAgfSk7XG4gICAgY29uc3QgY2xvbmVkMiA9IG5ldyBSZXNwb25zZShib2R5Miwge1xuICAgICAgICBzdGF0dXM6IG9yaWdpbmFsLnN0YXR1cyxcbiAgICAgICAgc3RhdHVzVGV4dDogb3JpZ2luYWwuc3RhdHVzVGV4dCxcbiAgICAgICAgaGVhZGVyczogb3JpZ2luYWwuaGVhZGVyc1xuICAgIH0pO1xuICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eShjbG9uZWQyLCBcInVybFwiLCB7XG4gICAgICAgIHZhbHVlOiBvcmlnaW5hbC51cmxcbiAgICB9KTtcbiAgICByZXR1cm4gW1xuICAgICAgICBjbG9uZWQxLFxuICAgICAgICBjbG9uZWQyXG4gICAgXTtcbn1cblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Y2xvbmUtcmVzcG9uc2UuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/server/lib/clone-response.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/server/lib/dedupe-fetch.js":
/*!***********************************************************!*\
  !*** ./node_modules/next/dist/server/lib/dedupe-fetch.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("/**\n * Based on https://github.com/facebook/react/blob/d4e78c42a94be027b4dc7ed2659a5fddfbf9bd4e/packages/react/src/ReactFetch.js\n */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"createDedupeFetch\", ({\n    enumerable: true,\n    get: function() {\n        return createDedupeFetch;\n    }\n}));\nconst _react = /*#__PURE__*/ _interop_require_wildcard(__webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\"));\nconst _cloneresponse = __webpack_require__(/*! ./clone-response */ \"(rsc)/./node_modules/next/dist/server/lib/clone-response.js\");\nfunction _getRequireWildcardCache(nodeInterop) {\n    if (typeof WeakMap !== \"function\") return null;\n    var cacheBabelInterop = new WeakMap();\n    var cacheNodeInterop = new WeakMap();\n    return (_getRequireWildcardCache = function(nodeInterop) {\n        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n    })(nodeInterop);\n}\nfunction _interop_require_wildcard(obj, nodeInterop) {\n    if (!nodeInterop && obj && obj.__esModule) {\n        return obj;\n    }\n    if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") {\n        return {\n            default: obj\n        };\n    }\n    var cache = _getRequireWildcardCache(nodeInterop);\n    if (cache && cache.has(obj)) {\n        return cache.get(obj);\n    }\n    var newObj = {\n        __proto__: null\n    };\n    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n    for(var key in obj){\n        if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n            if (desc && (desc.get || desc.set)) {\n                Object.defineProperty(newObj, key, desc);\n            } else {\n                newObj[key] = obj[key];\n            }\n        }\n    }\n    newObj.default = obj;\n    if (cache) {\n        cache.set(obj, newObj);\n    }\n    return newObj;\n}\nconst simpleCacheKey = '[\"GET\",[],null,\"follow\",null,null,null,null]' // generateCacheKey(new Request('https://blank'));\n;\nfunction generateCacheKey(request) {\n    // We pick the fields that goes into the key used to dedupe requests.\n    // We don't include the `cache` field, because we end up using whatever\n    // caching resulted from the first request.\n    // Notably we currently don't consider non-standard (or future) options.\n    // This might not be safe. TODO: warn for non-standard extensions differing.\n    // IF YOU CHANGE THIS UPDATE THE simpleCacheKey ABOVE.\n    return JSON.stringify([\n        request.method,\n        Array.from(request.headers.entries()),\n        request.mode,\n        request.redirect,\n        request.credentials,\n        request.referrer,\n        request.referrerPolicy,\n        request.integrity\n    ]);\n}\nfunction createDedupeFetch(originalFetch) {\n    const getCacheEntries = _react.cache(// eslint-disable-next-line @typescript-eslint/no-unused-vars -- url is the cache key\n    (url)=>[]);\n    return function dedupeFetch(resource, options) {\n        if (options && options.signal) {\n            // If we're passed a signal, then we assume that\n            // someone else controls the lifetime of this object and opts out of\n            // caching. It's effectively the opt-out mechanism.\n            // Ideally we should be able to check this on the Request but\n            // it always gets initialized with its own signal so we don't\n            // know if it's supposed to override - unless we also override the\n            // Request constructor.\n            return originalFetch(resource, options);\n        }\n        // Normalize the Request\n        let url;\n        let cacheKey;\n        if (typeof resource === \"string\" && !options) {\n            // Fast path.\n            cacheKey = simpleCacheKey;\n            url = resource;\n        } else {\n            // Normalize the request.\n            // if resource is not a string or a URL (its an instance of Request)\n            // then do not instantiate a new Request but instead\n            // reuse the request as to not disturb the body in the event it's a ReadableStream.\n            const request = typeof resource === \"string\" || resource instanceof URL ? new Request(resource, options) : resource;\n            if (request.method !== \"GET\" && request.method !== \"HEAD\" || request.keepalive) {\n                // We currently don't dedupe requests that might have side-effects. Those\n                // have to be explicitly cached. We assume that the request doesn't have a\n                // body if it's GET or HEAD.\n                // keepalive gets treated the same as if you passed a custom cache signal.\n                return originalFetch(resource, options);\n            }\n            cacheKey = generateCacheKey(request);\n            url = request.url;\n        }\n        const cacheEntries = getCacheEntries(url);\n        for(let i = 0, j = cacheEntries.length; i < j; i += 1){\n            const [key, promise] = cacheEntries[i];\n            if (key === cacheKey) {\n                return promise.then(()=>{\n                    const response = cacheEntries[i][2];\n                    if (!response) throw new Error(\"No cached response\");\n                    // We're cloning the response using this utility because there exists\n                    // a bug in the undici library around response cloning. See the\n                    // following pull request for more details:\n                    // https://github.com/vercel/next.js/pull/73274\n                    const [cloned1, cloned2] = (0, _cloneresponse.cloneResponse)(response);\n                    cacheEntries[i][2] = cloned2;\n                    return cloned1;\n                });\n            }\n        }\n        // We pass the original arguments here in case normalizing the Request\n        // doesn't include all the options in this environment. We also pass a\n        // signal down to the original fetch as to bypass the underlying React fetch\n        // cache.\n        const controller = new AbortController();\n        const promise = originalFetch(resource, {\n            ...options,\n            signal: controller.signal\n        });\n        const entry = [\n            cacheKey,\n            promise,\n            null\n        ];\n        cacheEntries.push(entry);\n        return promise.then((response)=>{\n            // We're cloning the response using this utility because there exists\n            // a bug in the undici library around response cloning. See the\n            // following pull request for more details:\n            // https://github.com/vercel/next.js/pull/73274\n            const [cloned1, cloned2] = (0, _cloneresponse.cloneResponse)(response);\n            entry[2] = cloned2;\n            return cloned1;\n        });\n    };\n}\n\n//# sourceMappingURL=dedupe-fetch.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/server/lib/dedupe-fetch.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js":
/*!**********************************************************!*\
  !*** ./node_modules/next/dist/server/lib/patch-fetch.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    addImplicitTags: function() {\n        return addImplicitTags;\n    },\n    patchFetch: function() {\n        return patchFetch;\n    },\n    validateRevalidate: function() {\n        return validateRevalidate;\n    },\n    validateTags: function() {\n        return validateTags;\n    }\n});\nconst _constants = __webpack_require__(/*! ./trace/constants */ \"(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\");\nconst _tracer = __webpack_require__(/*! ./trace/tracer */ \"(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js\");\nconst _constants1 = __webpack_require__(/*! ../../lib/constants */ \"(rsc)/./node_modules/next/dist/lib/constants.js\");\nconst _log = /*#__PURE__*/ _interop_require_wildcard(__webpack_require__(/*! ../../build/output/log */ \"(rsc)/./node_modules/next/dist/build/output/log.js\"));\nconst _dynamicrendering = __webpack_require__(/*! ../app-render/dynamic-rendering */ \"(rsc)/./node_modules/next/dist/server/app-render/dynamic-rendering.js\");\nconst _dedupefetch = __webpack_require__(/*! ./dedupe-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/dedupe-fetch.js\");\nconst _cloneresponse = __webpack_require__(/*! ./clone-response */ \"(rsc)/./node_modules/next/dist/server/lib/clone-response.js\");\nfunction _getRequireWildcardCache(nodeInterop) {\n    if (typeof WeakMap !== \"function\") return null;\n    var cacheBabelInterop = new WeakMap();\n    var cacheNodeInterop = new WeakMap();\n    return (_getRequireWildcardCache = function(nodeInterop) {\n        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n    })(nodeInterop);\n}\nfunction _interop_require_wildcard(obj, nodeInterop) {\n    if (!nodeInterop && obj && obj.__esModule) {\n        return obj;\n    }\n    if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") {\n        return {\n            default: obj\n        };\n    }\n    var cache = _getRequireWildcardCache(nodeInterop);\n    if (cache && cache.has(obj)) {\n        return cache.get(obj);\n    }\n    var newObj = {\n        __proto__: null\n    };\n    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n    for(var key in obj){\n        if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n            if (desc && (desc.get || desc.set)) {\n                Object.defineProperty(newObj, key, desc);\n            } else {\n                newObj[key] = obj[key];\n            }\n        }\n    }\n    newObj.default = obj;\n    if (cache) {\n        cache.set(obj, newObj);\n    }\n    return newObj;\n}\nconst isEdgeRuntime = \"nodejs\" === \"edge\";\nfunction isPatchedFetch(fetch) {\n    return \"__nextPatched\" in fetch && fetch.__nextPatched === true;\n}\nfunction validateRevalidate(revalidateVal, pathname) {\n    try {\n        let normalizedRevalidate = undefined;\n        if (revalidateVal === false) {\n            normalizedRevalidate = revalidateVal;\n        } else if (typeof revalidateVal === \"number\" && !isNaN(revalidateVal) && revalidateVal > -1) {\n            normalizedRevalidate = revalidateVal;\n        } else if (typeof revalidateVal !== \"undefined\") {\n            throw new Error(`Invalid revalidate value \"${revalidateVal}\" on \"${pathname}\", must be a non-negative number or \"false\"`);\n        }\n        return normalizedRevalidate;\n    } catch (err) {\n        // handle client component error from attempting to check revalidate value\n        if (err instanceof Error && err.message.includes(\"Invalid revalidate\")) {\n            throw err;\n        }\n        return undefined;\n    }\n}\nfunction validateTags(tags, description) {\n    const validTags = [];\n    const invalidTags = [];\n    for(let i = 0; i < tags.length; i++){\n        const tag = tags[i];\n        if (typeof tag !== \"string\") {\n            invalidTags.push({\n                tag,\n                reason: \"invalid type, must be a string\"\n            });\n        } else if (tag.length > _constants1.NEXT_CACHE_TAG_MAX_LENGTH) {\n            invalidTags.push({\n                tag,\n                reason: `exceeded max length of ${_constants1.NEXT_CACHE_TAG_MAX_LENGTH}`\n            });\n        } else {\n            validTags.push(tag);\n        }\n        if (validTags.length > _constants1.NEXT_CACHE_TAG_MAX_ITEMS) {\n            console.warn(`Warning: exceeded max tag count for ${description}, dropped tags:`, tags.slice(i).join(\", \"));\n            break;\n        }\n    }\n    if (invalidTags.length > 0) {\n        console.warn(`Warning: invalid tags passed to ${description}: `);\n        for (const { tag, reason } of invalidTags){\n            console.log(`tag: \"${tag}\" ${reason}`);\n        }\n    }\n    return validTags;\n}\nconst getDerivedTags = (pathname)=>{\n    const derivedTags = [\n        `/layout`\n    ];\n    // we automatically add the current path segments as tags\n    // for revalidatePath handling\n    if (pathname.startsWith(\"/\")) {\n        const pathnameParts = pathname.split(\"/\");\n        for(let i = 1; i < pathnameParts.length + 1; i++){\n            let curPathname = pathnameParts.slice(0, i).join(\"/\");\n            if (curPathname) {\n                // all derived tags other than the page are layout tags\n                if (!curPathname.endsWith(\"/page\") && !curPathname.endsWith(\"/route\")) {\n                    curPathname = `${curPathname}${!curPathname.endsWith(\"/\") ? \"/\" : \"\"}layout`;\n                }\n                derivedTags.push(curPathname);\n            }\n        }\n    }\n    return derivedTags;\n};\nfunction addImplicitTags(staticGenerationStore) {\n    const newTags = [];\n    const { pagePath, urlPathname } = staticGenerationStore;\n    if (!Array.isArray(staticGenerationStore.tags)) {\n        staticGenerationStore.tags = [];\n    }\n    if (pagePath) {\n        const derivedTags = getDerivedTags(pagePath);\n        for (let tag of derivedTags){\n            var _staticGenerationStore_tags;\n            tag = `${_constants1.NEXT_CACHE_IMPLICIT_TAG_ID}${tag}`;\n            if (!((_staticGenerationStore_tags = staticGenerationStore.tags) == null ? void 0 : _staticGenerationStore_tags.includes(tag))) {\n                staticGenerationStore.tags.push(tag);\n            }\n            newTags.push(tag);\n        }\n    }\n    if (urlPathname) {\n        var _staticGenerationStore_tags1;\n        const parsedPathname = new URL(urlPathname, \"http://n\").pathname;\n        const tag = `${_constants1.NEXT_CACHE_IMPLICIT_TAG_ID}${parsedPathname}`;\n        if (!((_staticGenerationStore_tags1 = staticGenerationStore.tags) == null ? void 0 : _staticGenerationStore_tags1.includes(tag))) {\n            staticGenerationStore.tags.push(tag);\n        }\n        newTags.push(tag);\n    }\n    return newTags;\n}\nfunction trackFetchMetric(staticGenerationStore, ctx) {\n    var _staticGenerationStore_requestEndedState;\n    if (!staticGenerationStore || ((_staticGenerationStore_requestEndedState = staticGenerationStore.requestEndedState) == null ? void 0 : _staticGenerationStore_requestEndedState.ended) || \"development\" !== \"development\") {\n        return;\n    }\n    staticGenerationStore.fetchMetrics ??= [];\n    const dedupeFields = [\n        \"url\",\n        \"status\",\n        \"method\"\n    ];\n    // don't add metric if one already exists for the fetch\n    if (staticGenerationStore.fetchMetrics.some((metric)=>dedupeFields.every((field)=>metric[field] === ctx[field]))) {\n        return;\n    }\n    staticGenerationStore.fetchMetrics.push({\n        ...ctx,\n        end: Date.now(),\n        idx: staticGenerationStore.nextFetchId || 0\n    });\n    // only store top 10 metrics to avoid storing too many\n    if (staticGenerationStore.fetchMetrics.length > 10) {\n        // sort slowest first as these should be highlighted\n        staticGenerationStore.fetchMetrics.sort((a, b)=>{\n            const aDur = a.end - a.start;\n            const bDur = b.end - b.start;\n            if (aDur < bDur) {\n                return 1;\n            } else if (aDur > bDur) {\n                return -1;\n            }\n            return 0;\n        });\n        // now grab top 10\n        staticGenerationStore.fetchMetrics = staticGenerationStore.fetchMetrics.slice(0, 10);\n    }\n}\nfunction createPatchedFetcher(originFetch, { serverHooks: { DynamicServerError }, staticGenerationAsyncStorage }) {\n    // Create the patched fetch function. We don't set the type here, as it's\n    // verified as the return value of this function.\n    const patched = async (input, init)=>{\n        var _init_method, _init_next;\n        let url;\n        try {\n            url = new URL(input instanceof Request ? input.url : input);\n            url.username = \"\";\n            url.password = \"\";\n        } catch  {\n            // Error caused by malformed URL should be handled by native fetch\n            url = undefined;\n        }\n        const fetchUrl = (url == null ? void 0 : url.href) ?? \"\";\n        const fetchStart = Date.now();\n        const method = (init == null ? void 0 : (_init_method = init.method) == null ? void 0 : _init_method.toUpperCase()) || \"GET\";\n        // Do create a new span trace for internal fetches in the\n        // non-verbose mode.\n        const isInternal = (init == null ? void 0 : (_init_next = init.next) == null ? void 0 : _init_next.internal) === true;\n        const hideSpan = process.env.NEXT_OTEL_FETCH_DISABLED === \"1\";\n        return (0, _tracer.getTracer)().trace(isInternal ? _constants.NextNodeServerSpan.internalFetch : _constants.AppRenderSpan.fetch, {\n            hideSpan,\n            kind: _tracer.SpanKind.CLIENT,\n            spanName: [\n                \"fetch\",\n                method,\n                fetchUrl\n            ].filter(Boolean).join(\" \"),\n            attributes: {\n                \"http.url\": fetchUrl,\n                \"http.method\": method,\n                \"net.peer.name\": url == null ? void 0 : url.hostname,\n                \"net.peer.port\": (url == null ? void 0 : url.port) || undefined\n            }\n        }, async ()=>{\n            var _getRequestMeta;\n            // If this is an internal fetch, we should not do any special treatment.\n            if (isInternal) return originFetch(input, init);\n            const staticGenerationStore = staticGenerationAsyncStorage.getStore();\n            // If the staticGenerationStore is not available, we can't do any\n            // special treatment of fetch, therefore fallback to the original\n            // fetch implementation.\n            if (!staticGenerationStore || staticGenerationStore.isDraftMode) {\n                return originFetch(input, init);\n            }\n            const isRequestInput = input && typeof input === \"object\" && typeof input.method === \"string\";\n            const getRequestMeta = (field)=>{\n                // If request input is present but init is not, retrieve from input first.\n                const value = init == null ? void 0 : init[field];\n                return value || (isRequestInput ? input[field] : null);\n            };\n            let revalidate = undefined;\n            const getNextField = (field)=>{\n                var _init_next, _init_next1, _input_next;\n                return typeof (init == null ? void 0 : (_init_next = init.next) == null ? void 0 : _init_next[field]) !== \"undefined\" ? init == null ? void 0 : (_init_next1 = init.next) == null ? void 0 : _init_next1[field] : isRequestInput ? (_input_next = input.next) == null ? void 0 : _input_next[field] : undefined;\n            };\n            // RequestInit doesn't keep extra fields e.g. next so it's\n            // only available if init is used separate\n            let curRevalidate = getNextField(\"revalidate\");\n            const tags = validateTags(getNextField(\"tags\") || [], `fetch ${input.toString()}`);\n            if (Array.isArray(tags)) {\n                if (!staticGenerationStore.tags) {\n                    staticGenerationStore.tags = [];\n                }\n                for (const tag of tags){\n                    if (!staticGenerationStore.tags.includes(tag)) {\n                        staticGenerationStore.tags.push(tag);\n                    }\n                }\n            }\n            const implicitTags = addImplicitTags(staticGenerationStore);\n            const fetchCacheMode = staticGenerationStore.fetchCache;\n            const isUsingNoStore = !!staticGenerationStore.isUnstableNoStore;\n            let _cache = getRequestMeta(\"cache\");\n            let cacheReason = \"\";\n            if (typeof _cache === \"string\" && typeof curRevalidate !== \"undefined\") {\n                // when providing fetch with a Request input, it'll automatically set a cache value of 'default'\n                // we only want to warn if the user is explicitly setting a cache value\n                if (!(isRequestInput && _cache === \"default\")) {\n                    _log.warn(`fetch for ${fetchUrl} on ${staticGenerationStore.urlPathname} specified \"cache: ${_cache}\" and \"revalidate: ${curRevalidate}\", only one should be specified.`);\n                }\n                _cache = undefined;\n            }\n            if (_cache === \"force-cache\") {\n                curRevalidate = false;\n            } else if (_cache === \"no-cache\" || _cache === \"no-store\" || fetchCacheMode === \"force-no-store\" || fetchCacheMode === \"only-no-store\") {\n                curRevalidate = 0;\n            }\n            if (_cache === \"no-cache\" || _cache === \"no-store\") {\n                cacheReason = `cache: ${_cache}`;\n            }\n            revalidate = validateRevalidate(curRevalidate, staticGenerationStore.urlPathname);\n            const _headers = getRequestMeta(\"headers\");\n            const initHeaders = typeof (_headers == null ? void 0 : _headers.get) === \"function\" ? _headers : new Headers(_headers || {});\n            const hasUnCacheableHeader = initHeaders.get(\"authorization\") || initHeaders.get(\"cookie\");\n            const isUnCacheableMethod = ![\n                \"get\",\n                \"head\"\n            ].includes(((_getRequestMeta = getRequestMeta(\"method\")) == null ? void 0 : _getRequestMeta.toLowerCase()) || \"get\");\n            // if there are authorized headers or a POST method and\n            // dynamic data usage was present above the tree we bail\n            // e.g. if cookies() is used before an authed/POST fetch\n            const autoNoCache = (hasUnCacheableHeader || isUnCacheableMethod) && staticGenerationStore.revalidate === 0;\n            switch(fetchCacheMode){\n                case \"force-no-store\":\n                    {\n                        cacheReason = \"fetchCache = force-no-store\";\n                        break;\n                    }\n                case \"only-no-store\":\n                    {\n                        if (_cache === \"force-cache\" || typeof revalidate !== \"undefined\" && (revalidate === false || revalidate > 0)) {\n                            throw new Error(`cache: 'force-cache' used on fetch for ${fetchUrl} with 'export const fetchCache = 'only-no-store'`);\n                        }\n                        cacheReason = \"fetchCache = only-no-store\";\n                        break;\n                    }\n                case \"only-cache\":\n                    {\n                        if (_cache === \"no-store\") {\n                            throw new Error(`cache: 'no-store' used on fetch for ${fetchUrl} with 'export const fetchCache = 'only-cache'`);\n                        }\n                        break;\n                    }\n                case \"force-cache\":\n                    {\n                        if (typeof curRevalidate === \"undefined\" || curRevalidate === 0) {\n                            cacheReason = \"fetchCache = force-cache\";\n                            revalidate = false;\n                        }\n                        break;\n                    }\n                default:\n            }\n            if (typeof revalidate === \"undefined\") {\n                if (fetchCacheMode === \"default-cache\") {\n                    revalidate = false;\n                    cacheReason = \"fetchCache = default-cache\";\n                } else if (autoNoCache) {\n                    revalidate = 0;\n                    cacheReason = \"auto no cache\";\n                } else if (fetchCacheMode === \"default-no-store\") {\n                    revalidate = 0;\n                    cacheReason = \"fetchCache = default-no-store\";\n                } else if (isUsingNoStore) {\n                    revalidate = 0;\n                    cacheReason = \"noStore call\";\n                } else {\n                    cacheReason = \"auto cache\";\n                    revalidate = typeof staticGenerationStore.revalidate === \"boolean\" || typeof staticGenerationStore.revalidate === \"undefined\" ? false : staticGenerationStore.revalidate;\n                }\n            } else if (!cacheReason) {\n                cacheReason = `revalidate: ${revalidate}`;\n            }\n            if (// when force static is configured we don't bail from\n            // `revalidate: 0` values\n            !(staticGenerationStore.forceStatic && revalidate === 0) && // we don't consider autoNoCache to switch to dynamic during\n            // revalidate although if it occurs during build we do\n            !autoNoCache && // If the revalidate value isn't currently set or the value is less\n            // than the current revalidate value, we should update the revalidate\n            // value.\n            (typeof staticGenerationStore.revalidate === \"undefined\" || typeof revalidate === \"number\" && (staticGenerationStore.revalidate === false || typeof staticGenerationStore.revalidate === \"number\" && revalidate < staticGenerationStore.revalidate))) {\n                // If we were setting the revalidate value to 0, we should try to\n                // postpone instead first.\n                if (revalidate === 0) {\n                    (0, _dynamicrendering.trackDynamicFetch)(staticGenerationStore, \"revalidate: 0\");\n                }\n                staticGenerationStore.revalidate = revalidate;\n            }\n            const isCacheableRevalidate = typeof revalidate === \"number\" && revalidate > 0 || revalidate === false;\n            let cacheKey;\n            if (staticGenerationStore.incrementalCache && isCacheableRevalidate) {\n                try {\n                    cacheKey = await staticGenerationStore.incrementalCache.fetchCacheKey(fetchUrl, isRequestInput ? input : init);\n                } catch (err) {\n                    console.error(`Failed to generate cache key for`, input);\n                }\n            }\n            const fetchIdx = staticGenerationStore.nextFetchId ?? 1;\n            staticGenerationStore.nextFetchId = fetchIdx + 1;\n            const normalizedRevalidate = typeof revalidate !== \"number\" ? _constants1.CACHE_ONE_YEAR : revalidate;\n            const doOriginalFetch = async (isStale, cacheReasonOverride)=>{\n                const requestInputFields = [\n                    \"cache\",\n                    \"credentials\",\n                    \"headers\",\n                    \"integrity\",\n                    \"keepalive\",\n                    \"method\",\n                    \"mode\",\n                    \"redirect\",\n                    \"referrer\",\n                    \"referrerPolicy\",\n                    \"window\",\n                    \"duplex\",\n                    // don't pass through signal when revalidating\n                    ...isStale ? [] : [\n                        \"signal\"\n                    ]\n                ];\n                if (isRequestInput) {\n                    const reqInput = input;\n                    const reqOptions = {\n                        body: reqInput._ogBody || reqInput.body\n                    };\n                    for (const field of requestInputFields){\n                        // @ts-expect-error custom fields\n                        reqOptions[field] = reqInput[field];\n                    }\n                    input = new Request(reqInput.url, reqOptions);\n                } else if (init) {\n                    const { _ogBody, body, signal, ...otherInput } = init;\n                    init = {\n                        ...otherInput,\n                        body: _ogBody || body,\n                        signal: isStale ? undefined : signal\n                    };\n                }\n                // add metadata to init without editing the original\n                const clonedInit = {\n                    ...init,\n                    next: {\n                        ...init == null ? void 0 : init.next,\n                        fetchType: \"origin\",\n                        fetchIdx\n                    }\n                };\n                return originFetch(input, clonedInit).then(async (res)=>{\n                    if (!isStale) {\n                        trackFetchMetric(staticGenerationStore, {\n                            start: fetchStart,\n                            url: fetchUrl,\n                            cacheReason: cacheReasonOverride || cacheReason,\n                            cacheStatus: revalidate === 0 || cacheReasonOverride ? \"skip\" : \"miss\",\n                            status: res.status,\n                            method: clonedInit.method || \"GET\"\n                        });\n                    }\n                    if (res.status === 200 && staticGenerationStore.incrementalCache && cacheKey && isCacheableRevalidate) {\n                        const bodyBuffer = Buffer.from(await res.arrayBuffer());\n                        try {\n                            await staticGenerationStore.incrementalCache.set(cacheKey, {\n                                kind: \"FETCH\",\n                                data: {\n                                    headers: Object.fromEntries(res.headers.entries()),\n                                    body: bodyBuffer.toString(\"base64\"),\n                                    status: res.status,\n                                    url: res.url\n                                },\n                                revalidate: normalizedRevalidate\n                            }, {\n                                fetchCache: true,\n                                revalidate,\n                                fetchUrl,\n                                fetchIdx,\n                                tags\n                            });\n                        } catch (err) {\n                            console.warn(`Failed to set fetch cache`, input, err);\n                        }\n                        const response = new Response(bodyBuffer, {\n                            headers: new Headers(res.headers),\n                            status: res.status\n                        });\n                        Object.defineProperty(response, \"url\", {\n                            value: res.url\n                        });\n                        return response;\n                    }\n                    return res;\n                });\n            };\n            let handleUnlock = ()=>Promise.resolve();\n            let cacheReasonOverride;\n            let isForegroundRevalidate = false;\n            if (cacheKey && staticGenerationStore.incrementalCache) {\n                handleUnlock = await staticGenerationStore.incrementalCache.lock(cacheKey);\n                const entry = staticGenerationStore.isOnDemandRevalidate ? null : await staticGenerationStore.incrementalCache.get(cacheKey, {\n                    kindHint: \"fetch\",\n                    revalidate,\n                    fetchUrl,\n                    fetchIdx,\n                    tags,\n                    softTags: implicitTags\n                });\n                if (entry) {\n                    await handleUnlock();\n                } else {\n                    // in dev, incremental cache response will be null in case the browser adds `cache-control: no-cache` in the request headers\n                    cacheReasonOverride = \"cache-control: no-cache (hard refresh)\";\n                }\n                if ((entry == null ? void 0 : entry.value) && entry.value.kind === \"FETCH\") {\n                    // when stale and is revalidating we wait for fresh data\n                    // so the revalidated entry has the updated data\n                    if (staticGenerationStore.isRevalidate && entry.isStale) {\n                        isForegroundRevalidate = true;\n                    } else {\n                        if (entry.isStale) {\n                            staticGenerationStore.pendingRevalidates ??= {};\n                            if (!staticGenerationStore.pendingRevalidates[cacheKey]) {\n                                const pendingRevalidate = doOriginalFetch(true).then(async (response)=>({\n                                        body: await response.arrayBuffer(),\n                                        headers: response.headers,\n                                        status: response.status,\n                                        statusText: response.statusText\n                                    })).finally(()=>{\n                                    staticGenerationStore.pendingRevalidates ??= {};\n                                    delete staticGenerationStore.pendingRevalidates[cacheKey || \"\"];\n                                });\n                                // Attach the empty catch here so we don't get a \"unhandled\n                                // promise rejection\" warning.\n                                pendingRevalidate.catch(console.error);\n                                staticGenerationStore.pendingRevalidates[cacheKey] = pendingRevalidate;\n                            }\n                        }\n                        const resData = entry.value.data;\n                        trackFetchMetric(staticGenerationStore, {\n                            start: fetchStart,\n                            url: fetchUrl,\n                            cacheReason,\n                            cacheStatus: \"hit\",\n                            status: resData.status || 200,\n                            method: (init == null ? void 0 : init.method) || \"GET\"\n                        });\n                        const response = new Response(Buffer.from(resData.body, \"base64\"), {\n                            headers: resData.headers,\n                            status: resData.status\n                        });\n                        Object.defineProperty(response, \"url\", {\n                            value: entry.value.data.url\n                        });\n                        return response;\n                    }\n                }\n            }\n            if (staticGenerationStore.isStaticGeneration && init && typeof init === \"object\") {\n                const { cache } = init;\n                // Delete `cache` property as Cloudflare Workers will throw an error\n                if (isEdgeRuntime) delete init.cache;\n                if (!staticGenerationStore.forceStatic && cache === \"no-store\") {\n                    const dynamicUsageReason = `no-store fetch ${input}${staticGenerationStore.urlPathname ? ` ${staticGenerationStore.urlPathname}` : \"\"}`;\n                    // If enabled, we should bail out of static generation.\n                    (0, _dynamicrendering.trackDynamicFetch)(staticGenerationStore, dynamicUsageReason);\n                    // PPR is not enabled, or React postpone is not available, we\n                    // should set the revalidate to 0.\n                    staticGenerationStore.revalidate = 0;\n                    const err = new DynamicServerError(dynamicUsageReason);\n                    staticGenerationStore.dynamicUsageErr = err;\n                    staticGenerationStore.dynamicUsageDescription = dynamicUsageReason;\n                    throw err;\n                }\n                const hasNextConfig = \"next\" in init;\n                const { next = {} } = init;\n                if (typeof next.revalidate === \"number\" && (typeof staticGenerationStore.revalidate === \"undefined\" || typeof staticGenerationStore.revalidate === \"number\" && next.revalidate < staticGenerationStore.revalidate)) {\n                    if (!staticGenerationStore.forceDynamic && !staticGenerationStore.forceStatic && next.revalidate === 0) {\n                        const dynamicUsageReason = `revalidate: 0 fetch ${input}${staticGenerationStore.urlPathname ? ` ${staticGenerationStore.urlPathname}` : \"\"}`;\n                        // If enabled, we should bail out of static generation.\n                        (0, _dynamicrendering.trackDynamicFetch)(staticGenerationStore, dynamicUsageReason);\n                        const err = new DynamicServerError(dynamicUsageReason);\n                        staticGenerationStore.dynamicUsageErr = err;\n                        staticGenerationStore.dynamicUsageDescription = dynamicUsageReason;\n                        throw err;\n                    }\n                    if (!staticGenerationStore.forceStatic || next.revalidate !== 0) {\n                        staticGenerationStore.revalidate = next.revalidate;\n                    }\n                }\n                if (hasNextConfig) delete init.next;\n            }\n            // if we are revalidating the whole page via time or on-demand and\n            // the fetch cache entry is stale we should still de-dupe the\n            // origin hit if it's a cache-able entry\n            if (cacheKey && isForegroundRevalidate) {\n                staticGenerationStore.pendingRevalidates ??= {};\n                let pendingRevalidate = staticGenerationStore.pendingRevalidates[cacheKey];\n                if (pendingRevalidate) {\n                    const revalidatedResult = await pendingRevalidate;\n                    return new Response(revalidatedResult.body, {\n                        headers: revalidatedResult.headers,\n                        status: revalidatedResult.status,\n                        statusText: revalidatedResult.statusText\n                    });\n                }\n                const pendingResponse = doOriginalFetch(true, cacheReasonOverride)// We're cloning the response using this utility because there\n                // exists a bug in the undici library around response cloning.\n                // See the following pull request for more details:\n                // https://github.com/vercel/next.js/pull/73274\n                .then(_cloneresponse.cloneResponse);\n                pendingRevalidate = pendingResponse.then(async (responses)=>{\n                    const response = responses[0];\n                    return {\n                        body: await response.arrayBuffer(),\n                        headers: response.headers,\n                        status: response.status,\n                        statusText: response.statusText\n                    };\n                }).finally(()=>{\n                    if (cacheKey) {\n                        var _staticGenerationStore_pendingRevalidates;\n                        // If the pending revalidate is not present in the store, then\n                        // we have nothing to delete.\n                        if (!((_staticGenerationStore_pendingRevalidates = staticGenerationStore.pendingRevalidates) == null ? void 0 : _staticGenerationStore_pendingRevalidates[cacheKey])) {\n                            return;\n                        }\n                        delete staticGenerationStore.pendingRevalidates[cacheKey];\n                    }\n                });\n                // Attach the empty catch here so we don't get a \"unhandled promise\n                // rejection\" warning\n                pendingRevalidate.catch(()=>{});\n                staticGenerationStore.pendingRevalidates[cacheKey] = pendingRevalidate;\n                return pendingResponse.then((responses)=>responses[1]);\n            } else {\n                return doOriginalFetch(false, cacheReasonOverride).finally(handleUnlock);\n            }\n        });\n    };\n    // Attach the necessary properties to the patched fetch function.\n    patched.__nextPatched = true;\n    patched.__nextGetStaticStore = ()=>staticGenerationAsyncStorage;\n    patched._nextOriginalFetch = originFetch;\n    return patched;\n}\nfunction patchFetch(options) {\n    // If we've already patched fetch, we should not patch it again.\n    if (isPatchedFetch(globalThis.fetch)) return;\n    // Grab the original fetch function. We'll attach this so we can use it in\n    // the patched fetch function.\n    const original = (0, _dedupefetch.createDedupeFetch)(globalThis.fetch);\n    // Set the global fetch to the patched fetch.\n    globalThis.fetch = createPatchedFetcher(original, options);\n}\n\n//# sourceMappingURL=patch-fetch.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NlcnZlci9saWIvcGF0Y2gtZmV0Y2guanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkM7QUFDN0M7QUFDQSxDQUFDLEVBQUM7QUFDRixNQUFNLENBS0w7QUFDRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNELG1CQUFtQixtQkFBTyxDQUFDLHVGQUFtQjtBQUM5QyxnQkFBZ0IsbUJBQU8sQ0FBQyxpRkFBZ0I7QUFDeEMsb0JBQW9CLG1CQUFPLENBQUMsNEVBQXFCO0FBQ2pELHFEQUFxRCxtQkFBTyxDQUFDLGtGQUF3QjtBQUNyRiwwQkFBMEIsbUJBQU8sQ0FBQyw4R0FBaUM7QUFDbkUscUJBQXFCLG1CQUFPLENBQUMsaUZBQWdCO0FBQzdDLHVCQUF1QixtQkFBTyxDQUFDLHFGQUFrQjtBQUNqRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGNBQWM7QUFDZDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQixRQUF3QjtBQUM5QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0EsVUFBVTtBQUNWLHlEQUF5RCxjQUFjLFFBQVEsU0FBUztBQUN4RjtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLGlCQUFpQjtBQUNwQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiLFVBQVU7QUFDVjtBQUNBO0FBQ0Esa0RBQWtELHNDQUFzQztBQUN4RixhQUFhO0FBQ2IsVUFBVTtBQUNWO0FBQ0E7QUFDQTtBQUNBLGdFQUFnRSxZQUFZO0FBQzVFO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0RBQXdELFlBQVk7QUFDcEUscUJBQXFCLGNBQWM7QUFDbkMsaUNBQWlDLElBQUksSUFBSSxPQUFPO0FBQ2hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVCQUF1Qiw4QkFBOEI7QUFDckQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQ0FBcUMsWUFBWSxFQUFFLHNDQUFzQztBQUN6RjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZLHdCQUF3QjtBQUNwQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQix1Q0FBdUMsRUFBRSxJQUFJO0FBQ2xFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVCQUF1Qix1Q0FBdUMsRUFBRSxlQUFlO0FBQy9FO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhMQUE4TCxhQUFvQjtBQUNsTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsY0FBYztBQUNkO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBLDZDQUE2QyxlQUFlLG9CQUFvQixnQ0FBZ0M7QUFDaEg7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyRUFBMkUsaUJBQWlCO0FBQzVGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkNBQTJDLFVBQVUsS0FBSyxtQ0FBbUMsb0JBQW9CLE9BQU8scUJBQXFCLGNBQWM7QUFDM0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGNBQWM7QUFDZDtBQUNBO0FBQ0E7QUFDQSx3Q0FBd0MsT0FBTztBQUMvQztBQUNBO0FBQ0E7QUFDQSx3SUFBd0k7QUFDeEk7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0ZBQXNGLFVBQVU7QUFDaEc7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtRkFBbUYsVUFBVTtBQUM3RjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCO0FBQ2xCO0FBQ0E7QUFDQSxrQkFBa0I7QUFDbEI7QUFDQTtBQUNBLGtCQUFrQjtBQUNsQjtBQUNBO0FBQ0Esa0JBQWtCO0FBQ2xCO0FBQ0E7QUFDQTtBQUNBLGNBQWM7QUFDZCw2Q0FBNkMsV0FBVztBQUN4RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQkFBa0I7QUFDbEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQjtBQUNsQiw0QkFBNEIsdUNBQXVDO0FBQ25FO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlCQUF5QjtBQUN6QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDO0FBQ2pDO0FBQ0EsNkJBQTZCO0FBQzdCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2QkFBNkI7QUFDN0IsMEJBQTBCO0FBQzFCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUI7QUFDekI7QUFDQTtBQUNBLHlCQUF5QjtBQUN6QjtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBLGtCQUFrQjtBQUNsQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCO0FBQ3RCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQ0FBcUM7QUFDckM7QUFDQTtBQUNBLGlDQUFpQztBQUNqQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUJBQXlCO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBLHlCQUF5QjtBQUN6QjtBQUNBO0FBQ0EseUJBQXlCO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IsUUFBUTtBQUNoQztBQUNBO0FBQ0E7QUFDQSxpRUFBaUUsTUFBTSxFQUFFLHdDQUF3QyxrQ0FBa0MsT0FBTztBQUMxSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLFlBQVk7QUFDcEM7QUFDQTtBQUNBLDBFQUEwRSxNQUFNLEVBQUUsd0NBQXdDLGtDQUFrQyxPQUFPO0FBQ25LO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQSw4Q0FBOEM7QUFDOUM7QUFDQTtBQUNBLGNBQWM7QUFDZDtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3Jzci1wcm9wZXJ0eS1tYW5hZ2VtZW50LWJhY2tlbmQvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NlcnZlci9saWIvcGF0Y2gtZmV0Y2guanM/MDA1ZSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwge1xuICAgIHZhbHVlOiB0cnVlXG59KTtcbjAgJiYgKG1vZHVsZS5leHBvcnRzID0ge1xuICAgIGFkZEltcGxpY2l0VGFnczogbnVsbCxcbiAgICBwYXRjaEZldGNoOiBudWxsLFxuICAgIHZhbGlkYXRlUmV2YWxpZGF0ZTogbnVsbCxcbiAgICB2YWxpZGF0ZVRhZ3M6IG51bGxcbn0pO1xuZnVuY3Rpb24gX2V4cG9ydCh0YXJnZXQsIGFsbCkge1xuICAgIGZvcih2YXIgbmFtZSBpbiBhbGwpT2JqZWN0LmRlZmluZVByb3BlcnR5KHRhcmdldCwgbmFtZSwge1xuICAgICAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgICAgICBnZXQ6IGFsbFtuYW1lXVxuICAgIH0pO1xufVxuX2V4cG9ydChleHBvcnRzLCB7XG4gICAgYWRkSW1wbGljaXRUYWdzOiBmdW5jdGlvbigpIHtcbiAgICAgICAgcmV0dXJuIGFkZEltcGxpY2l0VGFncztcbiAgICB9LFxuICAgIHBhdGNoRmV0Y2g6IGZ1bmN0aW9uKCkge1xuICAgICAgICByZXR1cm4gcGF0Y2hGZXRjaDtcbiAgICB9LFxuICAgIHZhbGlkYXRlUmV2YWxpZGF0ZTogZnVuY3Rpb24oKSB7XG4gICAgICAgIHJldHVybiB2YWxpZGF0ZVJldmFsaWRhdGU7XG4gICAgfSxcbiAgICB2YWxpZGF0ZVRhZ3M6IGZ1bmN0aW9uKCkge1xuICAgICAgICByZXR1cm4gdmFsaWRhdGVUYWdzO1xuICAgIH1cbn0pO1xuY29uc3QgX2NvbnN0YW50cyA9IHJlcXVpcmUoXCIuL3RyYWNlL2NvbnN0YW50c1wiKTtcbmNvbnN0IF90cmFjZXIgPSByZXF1aXJlKFwiLi90cmFjZS90cmFjZXJcIik7XG5jb25zdCBfY29uc3RhbnRzMSA9IHJlcXVpcmUoXCIuLi8uLi9saWIvY29uc3RhbnRzXCIpO1xuY29uc3QgX2xvZyA9IC8qI19fUFVSRV9fKi8gX2ludGVyb3BfcmVxdWlyZV93aWxkY2FyZChyZXF1aXJlKFwiLi4vLi4vYnVpbGQvb3V0cHV0L2xvZ1wiKSk7XG5jb25zdCBfZHluYW1pY3JlbmRlcmluZyA9IHJlcXVpcmUoXCIuLi9hcHAtcmVuZGVyL2R5bmFtaWMtcmVuZGVyaW5nXCIpO1xuY29uc3QgX2RlZHVwZWZldGNoID0gcmVxdWlyZShcIi4vZGVkdXBlLWZldGNoXCIpO1xuY29uc3QgX2Nsb25lcmVzcG9uc2UgPSByZXF1aXJlKFwiLi9jbG9uZS1yZXNwb25zZVwiKTtcbmZ1bmN0aW9uIF9nZXRSZXF1aXJlV2lsZGNhcmRDYWNoZShub2RlSW50ZXJvcCkge1xuICAgIGlmICh0eXBlb2YgV2Vha01hcCAhPT0gXCJmdW5jdGlvblwiKSByZXR1cm4gbnVsbDtcbiAgICB2YXIgY2FjaGVCYWJlbEludGVyb3AgPSBuZXcgV2Vha01hcCgpO1xuICAgIHZhciBjYWNoZU5vZGVJbnRlcm9wID0gbmV3IFdlYWtNYXAoKTtcbiAgICByZXR1cm4gKF9nZXRSZXF1aXJlV2lsZGNhcmRDYWNoZSA9IGZ1bmN0aW9uKG5vZGVJbnRlcm9wKSB7XG4gICAgICAgIHJldHVybiBub2RlSW50ZXJvcCA/IGNhY2hlTm9kZUludGVyb3AgOiBjYWNoZUJhYmVsSW50ZXJvcDtcbiAgICB9KShub2RlSW50ZXJvcCk7XG59XG5mdW5jdGlvbiBfaW50ZXJvcF9yZXF1aXJlX3dpbGRjYXJkKG9iaiwgbm9kZUludGVyb3ApIHtcbiAgICBpZiAoIW5vZGVJbnRlcm9wICYmIG9iaiAmJiBvYmouX19lc01vZHVsZSkge1xuICAgICAgICByZXR1cm4gb2JqO1xuICAgIH1cbiAgICBpZiAob2JqID09PSBudWxsIHx8IHR5cGVvZiBvYmogIT09IFwib2JqZWN0XCIgJiYgdHlwZW9mIG9iaiAhPT0gXCJmdW5jdGlvblwiKSB7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICBkZWZhdWx0OiBvYmpcbiAgICAgICAgfTtcbiAgICB9XG4gICAgdmFyIGNhY2hlID0gX2dldFJlcXVpcmVXaWxkY2FyZENhY2hlKG5vZGVJbnRlcm9wKTtcbiAgICBpZiAoY2FjaGUgJiYgY2FjaGUuaGFzKG9iaikpIHtcbiAgICAgICAgcmV0dXJuIGNhY2hlLmdldChvYmopO1xuICAgIH1cbiAgICB2YXIgbmV3T2JqID0ge1xuICAgICAgICBfX3Byb3RvX186IG51bGxcbiAgICB9O1xuICAgIHZhciBoYXNQcm9wZXJ0eURlc2NyaXB0b3IgPSBPYmplY3QuZGVmaW5lUHJvcGVydHkgJiYgT2JqZWN0LmdldE93blByb3BlcnR5RGVzY3JpcHRvcjtcbiAgICBmb3IodmFyIGtleSBpbiBvYmope1xuICAgICAgICBpZiAoa2V5ICE9PSBcImRlZmF1bHRcIiAmJiBPYmplY3QucHJvdG90eXBlLmhhc093blByb3BlcnR5LmNhbGwob2JqLCBrZXkpKSB7XG4gICAgICAgICAgICB2YXIgZGVzYyA9IGhhc1Byb3BlcnR5RGVzY3JpcHRvciA/IE9iamVjdC5nZXRPd25Qcm9wZXJ0eURlc2NyaXB0b3Iob2JqLCBrZXkpIDogbnVsbDtcbiAgICAgICAgICAgIGlmIChkZXNjICYmIChkZXNjLmdldCB8fCBkZXNjLnNldCkpIHtcbiAgICAgICAgICAgICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkobmV3T2JqLCBrZXksIGRlc2MpO1xuICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICBuZXdPYmpba2V5XSA9IG9ialtrZXldO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfVxuICAgIG5ld09iai5kZWZhdWx0ID0gb2JqO1xuICAgIGlmIChjYWNoZSkge1xuICAgICAgICBjYWNoZS5zZXQob2JqLCBuZXdPYmopO1xuICAgIH1cbiAgICByZXR1cm4gbmV3T2JqO1xufVxuY29uc3QgaXNFZGdlUnVudGltZSA9IHByb2Nlc3MuZW52Lk5FWFRfUlVOVElNRSA9PT0gXCJlZGdlXCI7XG5mdW5jdGlvbiBpc1BhdGNoZWRGZXRjaChmZXRjaCkge1xuICAgIHJldHVybiBcIl9fbmV4dFBhdGNoZWRcIiBpbiBmZXRjaCAmJiBmZXRjaC5fX25leHRQYXRjaGVkID09PSB0cnVlO1xufVxuZnVuY3Rpb24gdmFsaWRhdGVSZXZhbGlkYXRlKHJldmFsaWRhdGVWYWwsIHBhdGhuYW1lKSB7XG4gICAgdHJ5IHtcbiAgICAgICAgbGV0IG5vcm1hbGl6ZWRSZXZhbGlkYXRlID0gdW5kZWZpbmVkO1xuICAgICAgICBpZiAocmV2YWxpZGF0ZVZhbCA9PT0gZmFsc2UpIHtcbiAgICAgICAgICAgIG5vcm1hbGl6ZWRSZXZhbGlkYXRlID0gcmV2YWxpZGF0ZVZhbDtcbiAgICAgICAgfSBlbHNlIGlmICh0eXBlb2YgcmV2YWxpZGF0ZVZhbCA9PT0gXCJudW1iZXJcIiAmJiAhaXNOYU4ocmV2YWxpZGF0ZVZhbCkgJiYgcmV2YWxpZGF0ZVZhbCA+IC0xKSB7XG4gICAgICAgICAgICBub3JtYWxpemVkUmV2YWxpZGF0ZSA9IHJldmFsaWRhdGVWYWw7XG4gICAgICAgIH0gZWxzZSBpZiAodHlwZW9mIHJldmFsaWRhdGVWYWwgIT09IFwidW5kZWZpbmVkXCIpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihgSW52YWxpZCByZXZhbGlkYXRlIHZhbHVlIFwiJHtyZXZhbGlkYXRlVmFsfVwiIG9uIFwiJHtwYXRobmFtZX1cIiwgbXVzdCBiZSBhIG5vbi1uZWdhdGl2ZSBudW1iZXIgb3IgXCJmYWxzZVwiYCk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIG5vcm1hbGl6ZWRSZXZhbGlkYXRlO1xuICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgICAvLyBoYW5kbGUgY2xpZW50IGNvbXBvbmVudCBlcnJvciBmcm9tIGF0dGVtcHRpbmcgdG8gY2hlY2sgcmV2YWxpZGF0ZSB2YWx1ZVxuICAgICAgICBpZiAoZXJyIGluc3RhbmNlb2YgRXJyb3IgJiYgZXJyLm1lc3NhZ2UuaW5jbHVkZXMoXCJJbnZhbGlkIHJldmFsaWRhdGVcIikpIHtcbiAgICAgICAgICAgIHRocm93IGVycjtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gdW5kZWZpbmVkO1xuICAgIH1cbn1cbmZ1bmN0aW9uIHZhbGlkYXRlVGFncyh0YWdzLCBkZXNjcmlwdGlvbikge1xuICAgIGNvbnN0IHZhbGlkVGFncyA9IFtdO1xuICAgIGNvbnN0IGludmFsaWRUYWdzID0gW107XG4gICAgZm9yKGxldCBpID0gMDsgaSA8IHRhZ3MubGVuZ3RoOyBpKyspe1xuICAgICAgICBjb25zdCB0YWcgPSB0YWdzW2ldO1xuICAgICAgICBpZiAodHlwZW9mIHRhZyAhPT0gXCJzdHJpbmdcIikge1xuICAgICAgICAgICAgaW52YWxpZFRhZ3MucHVzaCh7XG4gICAgICAgICAgICAgICAgdGFnLFxuICAgICAgICAgICAgICAgIHJlYXNvbjogXCJpbnZhbGlkIHR5cGUsIG11c3QgYmUgYSBzdHJpbmdcIlxuICAgICAgICAgICAgfSk7XG4gICAgICAgIH0gZWxzZSBpZiAodGFnLmxlbmd0aCA+IF9jb25zdGFudHMxLk5FWFRfQ0FDSEVfVEFHX01BWF9MRU5HVEgpIHtcbiAgICAgICAgICAgIGludmFsaWRUYWdzLnB1c2goe1xuICAgICAgICAgICAgICAgIHRhZyxcbiAgICAgICAgICAgICAgICByZWFzb246IGBleGNlZWRlZCBtYXggbGVuZ3RoIG9mICR7X2NvbnN0YW50czEuTkVYVF9DQUNIRV9UQUdfTUFYX0xFTkdUSH1gXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIHZhbGlkVGFncy5wdXNoKHRhZyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHZhbGlkVGFncy5sZW5ndGggPiBfY29uc3RhbnRzMS5ORVhUX0NBQ0hFX1RBR19NQVhfSVRFTVMpIHtcbiAgICAgICAgICAgIGNvbnNvbGUud2FybihgV2FybmluZzogZXhjZWVkZWQgbWF4IHRhZyBjb3VudCBmb3IgJHtkZXNjcmlwdGlvbn0sIGRyb3BwZWQgdGFnczpgLCB0YWdzLnNsaWNlKGkpLmpvaW4oXCIsIFwiKSk7XG4gICAgICAgICAgICBicmVhaztcbiAgICAgICAgfVxuICAgIH1cbiAgICBpZiAoaW52YWxpZFRhZ3MubGVuZ3RoID4gMCkge1xuICAgICAgICBjb25zb2xlLndhcm4oYFdhcm5pbmc6IGludmFsaWQgdGFncyBwYXNzZWQgdG8gJHtkZXNjcmlwdGlvbn06IGApO1xuICAgICAgICBmb3IgKGNvbnN0IHsgdGFnLCByZWFzb24gfSBvZiBpbnZhbGlkVGFncyl7XG4gICAgICAgICAgICBjb25zb2xlLmxvZyhgdGFnOiBcIiR7dGFnfVwiICR7cmVhc29ufWApO1xuICAgICAgICB9XG4gICAgfVxuICAgIHJldHVybiB2YWxpZFRhZ3M7XG59XG5jb25zdCBnZXREZXJpdmVkVGFncyA9IChwYXRobmFtZSk9PntcbiAgICBjb25zdCBkZXJpdmVkVGFncyA9IFtcbiAgICAgICAgYC9sYXlvdXRgXG4gICAgXTtcbiAgICAvLyB3ZSBhdXRvbWF0aWNhbGx5IGFkZCB0aGUgY3VycmVudCBwYXRoIHNlZ21lbnRzIGFzIHRhZ3NcbiAgICAvLyBmb3IgcmV2YWxpZGF0ZVBhdGggaGFuZGxpbmdcbiAgICBpZiAocGF0aG5hbWUuc3RhcnRzV2l0aChcIi9cIikpIHtcbiAgICAgICAgY29uc3QgcGF0aG5hbWVQYXJ0cyA9IHBhdGhuYW1lLnNwbGl0KFwiL1wiKTtcbiAgICAgICAgZm9yKGxldCBpID0gMTsgaSA8IHBhdGhuYW1lUGFydHMubGVuZ3RoICsgMTsgaSsrKXtcbiAgICAgICAgICAgIGxldCBjdXJQYXRobmFtZSA9IHBhdGhuYW1lUGFydHMuc2xpY2UoMCwgaSkuam9pbihcIi9cIik7XG4gICAgICAgICAgICBpZiAoY3VyUGF0aG5hbWUpIHtcbiAgICAgICAgICAgICAgICAvLyBhbGwgZGVyaXZlZCB0YWdzIG90aGVyIHRoYW4gdGhlIHBhZ2UgYXJlIGxheW91dCB0YWdzXG4gICAgICAgICAgICAgICAgaWYgKCFjdXJQYXRobmFtZS5lbmRzV2l0aChcIi9wYWdlXCIpICYmICFjdXJQYXRobmFtZS5lbmRzV2l0aChcIi9yb3V0ZVwiKSkge1xuICAgICAgICAgICAgICAgICAgICBjdXJQYXRobmFtZSA9IGAke2N1clBhdGhuYW1lfSR7IWN1clBhdGhuYW1lLmVuZHNXaXRoKFwiL1wiKSA/IFwiL1wiIDogXCJcIn1sYXlvdXRgO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBkZXJpdmVkVGFncy5wdXNoKGN1clBhdGhuYW1lKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH1cbiAgICByZXR1cm4gZGVyaXZlZFRhZ3M7XG59O1xuZnVuY3Rpb24gYWRkSW1wbGljaXRUYWdzKHN0YXRpY0dlbmVyYXRpb25TdG9yZSkge1xuICAgIGNvbnN0IG5ld1RhZ3MgPSBbXTtcbiAgICBjb25zdCB7IHBhZ2VQYXRoLCB1cmxQYXRobmFtZSB9ID0gc3RhdGljR2VuZXJhdGlvblN0b3JlO1xuICAgIGlmICghQXJyYXkuaXNBcnJheShzdGF0aWNHZW5lcmF0aW9uU3RvcmUudGFncykpIHtcbiAgICAgICAgc3RhdGljR2VuZXJhdGlvblN0b3JlLnRhZ3MgPSBbXTtcbiAgICB9XG4gICAgaWYgKHBhZ2VQYXRoKSB7XG4gICAgICAgIGNvbnN0IGRlcml2ZWRUYWdzID0gZ2V0RGVyaXZlZFRhZ3MocGFnZVBhdGgpO1xuICAgICAgICBmb3IgKGxldCB0YWcgb2YgZGVyaXZlZFRhZ3Mpe1xuICAgICAgICAgICAgdmFyIF9zdGF0aWNHZW5lcmF0aW9uU3RvcmVfdGFncztcbiAgICAgICAgICAgIHRhZyA9IGAke19jb25zdGFudHMxLk5FWFRfQ0FDSEVfSU1QTElDSVRfVEFHX0lEfSR7dGFnfWA7XG4gICAgICAgICAgICBpZiAoISgoX3N0YXRpY0dlbmVyYXRpb25TdG9yZV90YWdzID0gc3RhdGljR2VuZXJhdGlvblN0b3JlLnRhZ3MpID09IG51bGwgPyB2b2lkIDAgOiBfc3RhdGljR2VuZXJhdGlvblN0b3JlX3RhZ3MuaW5jbHVkZXModGFnKSkpIHtcbiAgICAgICAgICAgICAgICBzdGF0aWNHZW5lcmF0aW9uU3RvcmUudGFncy5wdXNoKHRhZyk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBuZXdUYWdzLnB1c2godGFnKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBpZiAodXJsUGF0aG5hbWUpIHtcbiAgICAgICAgdmFyIF9zdGF0aWNHZW5lcmF0aW9uU3RvcmVfdGFnczE7XG4gICAgICAgIGNvbnN0IHBhcnNlZFBhdGhuYW1lID0gbmV3IFVSTCh1cmxQYXRobmFtZSwgXCJodHRwOi8vblwiKS5wYXRobmFtZTtcbiAgICAgICAgY29uc3QgdGFnID0gYCR7X2NvbnN0YW50czEuTkVYVF9DQUNIRV9JTVBMSUNJVF9UQUdfSUR9JHtwYXJzZWRQYXRobmFtZX1gO1xuICAgICAgICBpZiAoISgoX3N0YXRpY0dlbmVyYXRpb25TdG9yZV90YWdzMSA9IHN0YXRpY0dlbmVyYXRpb25TdG9yZS50YWdzKSA9PSBudWxsID8gdm9pZCAwIDogX3N0YXRpY0dlbmVyYXRpb25TdG9yZV90YWdzMS5pbmNsdWRlcyh0YWcpKSkge1xuICAgICAgICAgICAgc3RhdGljR2VuZXJhdGlvblN0b3JlLnRhZ3MucHVzaCh0YWcpO1xuICAgICAgICB9XG4gICAgICAgIG5ld1RhZ3MucHVzaCh0YWcpO1xuICAgIH1cbiAgICByZXR1cm4gbmV3VGFncztcbn1cbmZ1bmN0aW9uIHRyYWNrRmV0Y2hNZXRyaWMoc3RhdGljR2VuZXJhdGlvblN0b3JlLCBjdHgpIHtcbiAgICB2YXIgX3N0YXRpY0dlbmVyYXRpb25TdG9yZV9yZXF1ZXN0RW5kZWRTdGF0ZTtcbiAgICBpZiAoIXN0YXRpY0dlbmVyYXRpb25TdG9yZSB8fCAoKF9zdGF0aWNHZW5lcmF0aW9uU3RvcmVfcmVxdWVzdEVuZGVkU3RhdGUgPSBzdGF0aWNHZW5lcmF0aW9uU3RvcmUucmVxdWVzdEVuZGVkU3RhdGUpID09IG51bGwgPyB2b2lkIDAgOiBfc3RhdGljR2VuZXJhdGlvblN0b3JlX3JlcXVlc3RFbmRlZFN0YXRlLmVuZGVkKSB8fCBwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gXCJkZXZlbG9wbWVudFwiKSB7XG4gICAgICAgIHJldHVybjtcbiAgICB9XG4gICAgc3RhdGljR2VuZXJhdGlvblN0b3JlLmZldGNoTWV0cmljcyA/Pz0gW107XG4gICAgY29uc3QgZGVkdXBlRmllbGRzID0gW1xuICAgICAgICBcInVybFwiLFxuICAgICAgICBcInN0YXR1c1wiLFxuICAgICAgICBcIm1ldGhvZFwiXG4gICAgXTtcbiAgICAvLyBkb24ndCBhZGQgbWV0cmljIGlmIG9uZSBhbHJlYWR5IGV4aXN0cyBmb3IgdGhlIGZldGNoXG4gICAgaWYgKHN0YXRpY0dlbmVyYXRpb25TdG9yZS5mZXRjaE1ldHJpY3Muc29tZSgobWV0cmljKT0+ZGVkdXBlRmllbGRzLmV2ZXJ5KChmaWVsZCk9Pm1ldHJpY1tmaWVsZF0gPT09IGN0eFtmaWVsZF0pKSkge1xuICAgICAgICByZXR1cm47XG4gICAgfVxuICAgIHN0YXRpY0dlbmVyYXRpb25TdG9yZS5mZXRjaE1ldHJpY3MucHVzaCh7XG4gICAgICAgIC4uLmN0eCxcbiAgICAgICAgZW5kOiBEYXRlLm5vdygpLFxuICAgICAgICBpZHg6IHN0YXRpY0dlbmVyYXRpb25TdG9yZS5uZXh0RmV0Y2hJZCB8fCAwXG4gICAgfSk7XG4gICAgLy8gb25seSBzdG9yZSB0b3AgMTAgbWV0cmljcyB0byBhdm9pZCBzdG9yaW5nIHRvbyBtYW55XG4gICAgaWYgKHN0YXRpY0dlbmVyYXRpb25TdG9yZS5mZXRjaE1ldHJpY3MubGVuZ3RoID4gMTApIHtcbiAgICAgICAgLy8gc29ydCBzbG93ZXN0IGZpcnN0IGFzIHRoZXNlIHNob3VsZCBiZSBoaWdobGlnaHRlZFxuICAgICAgICBzdGF0aWNHZW5lcmF0aW9uU3RvcmUuZmV0Y2hNZXRyaWNzLnNvcnQoKGEsIGIpPT57XG4gICAgICAgICAgICBjb25zdCBhRHVyID0gYS5lbmQgLSBhLnN0YXJ0O1xuICAgICAgICAgICAgY29uc3QgYkR1ciA9IGIuZW5kIC0gYi5zdGFydDtcbiAgICAgICAgICAgIGlmIChhRHVyIDwgYkR1cikge1xuICAgICAgICAgICAgICAgIHJldHVybiAxO1xuICAgICAgICAgICAgfSBlbHNlIGlmIChhRHVyID4gYkR1cikge1xuICAgICAgICAgICAgICAgIHJldHVybiAtMTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHJldHVybiAwO1xuICAgICAgICB9KTtcbiAgICAgICAgLy8gbm93IGdyYWIgdG9wIDEwXG4gICAgICAgIHN0YXRpY0dlbmVyYXRpb25TdG9yZS5mZXRjaE1ldHJpY3MgPSBzdGF0aWNHZW5lcmF0aW9uU3RvcmUuZmV0Y2hNZXRyaWNzLnNsaWNlKDAsIDEwKTtcbiAgICB9XG59XG5mdW5jdGlvbiBjcmVhdGVQYXRjaGVkRmV0Y2hlcihvcmlnaW5GZXRjaCwgeyBzZXJ2ZXJIb29rczogeyBEeW5hbWljU2VydmVyRXJyb3IgfSwgc3RhdGljR2VuZXJhdGlvbkFzeW5jU3RvcmFnZSB9KSB7XG4gICAgLy8gQ3JlYXRlIHRoZSBwYXRjaGVkIGZldGNoIGZ1bmN0aW9uLiBXZSBkb24ndCBzZXQgdGhlIHR5cGUgaGVyZSwgYXMgaXQnc1xuICAgIC8vIHZlcmlmaWVkIGFzIHRoZSByZXR1cm4gdmFsdWUgb2YgdGhpcyBmdW5jdGlvbi5cbiAgICBjb25zdCBwYXRjaGVkID0gYXN5bmMgKGlucHV0LCBpbml0KT0+e1xuICAgICAgICB2YXIgX2luaXRfbWV0aG9kLCBfaW5pdF9uZXh0O1xuICAgICAgICBsZXQgdXJsO1xuICAgICAgICB0cnkge1xuICAgICAgICAgICAgdXJsID0gbmV3IFVSTChpbnB1dCBpbnN0YW5jZW9mIFJlcXVlc3QgPyBpbnB1dC51cmwgOiBpbnB1dCk7XG4gICAgICAgICAgICB1cmwudXNlcm5hbWUgPSBcIlwiO1xuICAgICAgICAgICAgdXJsLnBhc3N3b3JkID0gXCJcIjtcbiAgICAgICAgfSBjYXRjaCAge1xuICAgICAgICAgICAgLy8gRXJyb3IgY2F1c2VkIGJ5IG1hbGZvcm1lZCBVUkwgc2hvdWxkIGJlIGhhbmRsZWQgYnkgbmF0aXZlIGZldGNoXG4gICAgICAgICAgICB1cmwgPSB1bmRlZmluZWQ7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgZmV0Y2hVcmwgPSAodXJsID09IG51bGwgPyB2b2lkIDAgOiB1cmwuaHJlZikgPz8gXCJcIjtcbiAgICAgICAgY29uc3QgZmV0Y2hTdGFydCA9IERhdGUubm93KCk7XG4gICAgICAgIGNvbnN0IG1ldGhvZCA9IChpbml0ID09IG51bGwgPyB2b2lkIDAgOiAoX2luaXRfbWV0aG9kID0gaW5pdC5tZXRob2QpID09IG51bGwgPyB2b2lkIDAgOiBfaW5pdF9tZXRob2QudG9VcHBlckNhc2UoKSkgfHwgXCJHRVRcIjtcbiAgICAgICAgLy8gRG8gY3JlYXRlIGEgbmV3IHNwYW4gdHJhY2UgZm9yIGludGVybmFsIGZldGNoZXMgaW4gdGhlXG4gICAgICAgIC8vIG5vbi12ZXJib3NlIG1vZGUuXG4gICAgICAgIGNvbnN0IGlzSW50ZXJuYWwgPSAoaW5pdCA9PSBudWxsID8gdm9pZCAwIDogKF9pbml0X25leHQgPSBpbml0Lm5leHQpID09IG51bGwgPyB2b2lkIDAgOiBfaW5pdF9uZXh0LmludGVybmFsKSA9PT0gdHJ1ZTtcbiAgICAgICAgY29uc3QgaGlkZVNwYW4gPSBwcm9jZXNzLmVudi5ORVhUX09URUxfRkVUQ0hfRElTQUJMRUQgPT09IFwiMVwiO1xuICAgICAgICByZXR1cm4gKDAsIF90cmFjZXIuZ2V0VHJhY2VyKSgpLnRyYWNlKGlzSW50ZXJuYWwgPyBfY29uc3RhbnRzLk5leHROb2RlU2VydmVyU3Bhbi5pbnRlcm5hbEZldGNoIDogX2NvbnN0YW50cy5BcHBSZW5kZXJTcGFuLmZldGNoLCB7XG4gICAgICAgICAgICBoaWRlU3BhbixcbiAgICAgICAgICAgIGtpbmQ6IF90cmFjZXIuU3BhbktpbmQuQ0xJRU5ULFxuICAgICAgICAgICAgc3Bhbk5hbWU6IFtcbiAgICAgICAgICAgICAgICBcImZldGNoXCIsXG4gICAgICAgICAgICAgICAgbWV0aG9kLFxuICAgICAgICAgICAgICAgIGZldGNoVXJsXG4gICAgICAgICAgICBdLmZpbHRlcihCb29sZWFuKS5qb2luKFwiIFwiKSxcbiAgICAgICAgICAgIGF0dHJpYnV0ZXM6IHtcbiAgICAgICAgICAgICAgICBcImh0dHAudXJsXCI6IGZldGNoVXJsLFxuICAgICAgICAgICAgICAgIFwiaHR0cC5tZXRob2RcIjogbWV0aG9kLFxuICAgICAgICAgICAgICAgIFwibmV0LnBlZXIubmFtZVwiOiB1cmwgPT0gbnVsbCA/IHZvaWQgMCA6IHVybC5ob3N0bmFtZSxcbiAgICAgICAgICAgICAgICBcIm5ldC5wZWVyLnBvcnRcIjogKHVybCA9PSBudWxsID8gdm9pZCAwIDogdXJsLnBvcnQpIHx8IHVuZGVmaW5lZFxuICAgICAgICAgICAgfVxuICAgICAgICB9LCBhc3luYyAoKT0+e1xuICAgICAgICAgICAgdmFyIF9nZXRSZXF1ZXN0TWV0YTtcbiAgICAgICAgICAgIC8vIElmIHRoaXMgaXMgYW4gaW50ZXJuYWwgZmV0Y2gsIHdlIHNob3VsZCBub3QgZG8gYW55IHNwZWNpYWwgdHJlYXRtZW50LlxuICAgICAgICAgICAgaWYgKGlzSW50ZXJuYWwpIHJldHVybiBvcmlnaW5GZXRjaChpbnB1dCwgaW5pdCk7XG4gICAgICAgICAgICBjb25zdCBzdGF0aWNHZW5lcmF0aW9uU3RvcmUgPSBzdGF0aWNHZW5lcmF0aW9uQXN5bmNTdG9yYWdlLmdldFN0b3JlKCk7XG4gICAgICAgICAgICAvLyBJZiB0aGUgc3RhdGljR2VuZXJhdGlvblN0b3JlIGlzIG5vdCBhdmFpbGFibGUsIHdlIGNhbid0IGRvIGFueVxuICAgICAgICAgICAgLy8gc3BlY2lhbCB0cmVhdG1lbnQgb2YgZmV0Y2gsIHRoZXJlZm9yZSBmYWxsYmFjayB0byB0aGUgb3JpZ2luYWxcbiAgICAgICAgICAgIC8vIGZldGNoIGltcGxlbWVudGF0aW9uLlxuICAgICAgICAgICAgaWYgKCFzdGF0aWNHZW5lcmF0aW9uU3RvcmUgfHwgc3RhdGljR2VuZXJhdGlvblN0b3JlLmlzRHJhZnRNb2RlKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIG9yaWdpbkZldGNoKGlucHV0LCBpbml0KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNvbnN0IGlzUmVxdWVzdElucHV0ID0gaW5wdXQgJiYgdHlwZW9mIGlucHV0ID09PSBcIm9iamVjdFwiICYmIHR5cGVvZiBpbnB1dC5tZXRob2QgPT09IFwic3RyaW5nXCI7XG4gICAgICAgICAgICBjb25zdCBnZXRSZXF1ZXN0TWV0YSA9IChmaWVsZCk9PntcbiAgICAgICAgICAgICAgICAvLyBJZiByZXF1ZXN0IGlucHV0IGlzIHByZXNlbnQgYnV0IGluaXQgaXMgbm90LCByZXRyaWV2ZSBmcm9tIGlucHV0IGZpcnN0LlxuICAgICAgICAgICAgICAgIGNvbnN0IHZhbHVlID0gaW5pdCA9PSBudWxsID8gdm9pZCAwIDogaW5pdFtmaWVsZF07XG4gICAgICAgICAgICAgICAgcmV0dXJuIHZhbHVlIHx8IChpc1JlcXVlc3RJbnB1dCA/IGlucHV0W2ZpZWxkXSA6IG51bGwpO1xuICAgICAgICAgICAgfTtcbiAgICAgICAgICAgIGxldCByZXZhbGlkYXRlID0gdW5kZWZpbmVkO1xuICAgICAgICAgICAgY29uc3QgZ2V0TmV4dEZpZWxkID0gKGZpZWxkKT0+e1xuICAgICAgICAgICAgICAgIHZhciBfaW5pdF9uZXh0LCBfaW5pdF9uZXh0MSwgX2lucHV0X25leHQ7XG4gICAgICAgICAgICAgICAgcmV0dXJuIHR5cGVvZiAoaW5pdCA9PSBudWxsID8gdm9pZCAwIDogKF9pbml0X25leHQgPSBpbml0Lm5leHQpID09IG51bGwgPyB2b2lkIDAgOiBfaW5pdF9uZXh0W2ZpZWxkXSkgIT09IFwidW5kZWZpbmVkXCIgPyBpbml0ID09IG51bGwgPyB2b2lkIDAgOiAoX2luaXRfbmV4dDEgPSBpbml0Lm5leHQpID09IG51bGwgPyB2b2lkIDAgOiBfaW5pdF9uZXh0MVtmaWVsZF0gOiBpc1JlcXVlc3RJbnB1dCA/IChfaW5wdXRfbmV4dCA9IGlucHV0Lm5leHQpID09IG51bGwgPyB2b2lkIDAgOiBfaW5wdXRfbmV4dFtmaWVsZF0gOiB1bmRlZmluZWQ7XG4gICAgICAgICAgICB9O1xuICAgICAgICAgICAgLy8gUmVxdWVzdEluaXQgZG9lc24ndCBrZWVwIGV4dHJhIGZpZWxkcyBlLmcuIG5leHQgc28gaXQnc1xuICAgICAgICAgICAgLy8gb25seSBhdmFpbGFibGUgaWYgaW5pdCBpcyB1c2VkIHNlcGFyYXRlXG4gICAgICAgICAgICBsZXQgY3VyUmV2YWxpZGF0ZSA9IGdldE5leHRGaWVsZChcInJldmFsaWRhdGVcIik7XG4gICAgICAgICAgICBjb25zdCB0YWdzID0gdmFsaWRhdGVUYWdzKGdldE5leHRGaWVsZChcInRhZ3NcIikgfHwgW10sIGBmZXRjaCAke2lucHV0LnRvU3RyaW5nKCl9YCk7XG4gICAgICAgICAgICBpZiAoQXJyYXkuaXNBcnJheSh0YWdzKSkge1xuICAgICAgICAgICAgICAgIGlmICghc3RhdGljR2VuZXJhdGlvblN0b3JlLnRhZ3MpIHtcbiAgICAgICAgICAgICAgICAgICAgc3RhdGljR2VuZXJhdGlvblN0b3JlLnRhZ3MgPSBbXTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgZm9yIChjb25zdCB0YWcgb2YgdGFncyl7XG4gICAgICAgICAgICAgICAgICAgIGlmICghc3RhdGljR2VuZXJhdGlvblN0b3JlLnRhZ3MuaW5jbHVkZXModGFnKSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgc3RhdGljR2VuZXJhdGlvblN0b3JlLnRhZ3MucHVzaCh0YWcpO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY29uc3QgaW1wbGljaXRUYWdzID0gYWRkSW1wbGljaXRUYWdzKHN0YXRpY0dlbmVyYXRpb25TdG9yZSk7XG4gICAgICAgICAgICBjb25zdCBmZXRjaENhY2hlTW9kZSA9IHN0YXRpY0dlbmVyYXRpb25TdG9yZS5mZXRjaENhY2hlO1xuICAgICAgICAgICAgY29uc3QgaXNVc2luZ05vU3RvcmUgPSAhIXN0YXRpY0dlbmVyYXRpb25TdG9yZS5pc1Vuc3RhYmxlTm9TdG9yZTtcbiAgICAgICAgICAgIGxldCBfY2FjaGUgPSBnZXRSZXF1ZXN0TWV0YShcImNhY2hlXCIpO1xuICAgICAgICAgICAgbGV0IGNhY2hlUmVhc29uID0gXCJcIjtcbiAgICAgICAgICAgIGlmICh0eXBlb2YgX2NhY2hlID09PSBcInN0cmluZ1wiICYmIHR5cGVvZiBjdXJSZXZhbGlkYXRlICE9PSBcInVuZGVmaW5lZFwiKSB7XG4gICAgICAgICAgICAgICAgLy8gd2hlbiBwcm92aWRpbmcgZmV0Y2ggd2l0aCBhIFJlcXVlc3QgaW5wdXQsIGl0J2xsIGF1dG9tYXRpY2FsbHkgc2V0IGEgY2FjaGUgdmFsdWUgb2YgJ2RlZmF1bHQnXG4gICAgICAgICAgICAgICAgLy8gd2Ugb25seSB3YW50IHRvIHdhcm4gaWYgdGhlIHVzZXIgaXMgZXhwbGljaXRseSBzZXR0aW5nIGEgY2FjaGUgdmFsdWVcbiAgICAgICAgICAgICAgICBpZiAoIShpc1JlcXVlc3RJbnB1dCAmJiBfY2FjaGUgPT09IFwiZGVmYXVsdFwiKSkge1xuICAgICAgICAgICAgICAgICAgICBfbG9nLndhcm4oYGZldGNoIGZvciAke2ZldGNoVXJsfSBvbiAke3N0YXRpY0dlbmVyYXRpb25TdG9yZS51cmxQYXRobmFtZX0gc3BlY2lmaWVkIFwiY2FjaGU6ICR7X2NhY2hlfVwiIGFuZCBcInJldmFsaWRhdGU6ICR7Y3VyUmV2YWxpZGF0ZX1cIiwgb25seSBvbmUgc2hvdWxkIGJlIHNwZWNpZmllZC5gKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgX2NhY2hlID0gdW5kZWZpbmVkO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKF9jYWNoZSA9PT0gXCJmb3JjZS1jYWNoZVwiKSB7XG4gICAgICAgICAgICAgICAgY3VyUmV2YWxpZGF0ZSA9IGZhbHNlO1xuICAgICAgICAgICAgfSBlbHNlIGlmIChfY2FjaGUgPT09IFwibm8tY2FjaGVcIiB8fCBfY2FjaGUgPT09IFwibm8tc3RvcmVcIiB8fCBmZXRjaENhY2hlTW9kZSA9PT0gXCJmb3JjZS1uby1zdG9yZVwiIHx8IGZldGNoQ2FjaGVNb2RlID09PSBcIm9ubHktbm8tc3RvcmVcIikge1xuICAgICAgICAgICAgICAgIGN1clJldmFsaWRhdGUgPSAwO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKF9jYWNoZSA9PT0gXCJuby1jYWNoZVwiIHx8IF9jYWNoZSA9PT0gXCJuby1zdG9yZVwiKSB7XG4gICAgICAgICAgICAgICAgY2FjaGVSZWFzb24gPSBgY2FjaGU6ICR7X2NhY2hlfWA7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXZhbGlkYXRlID0gdmFsaWRhdGVSZXZhbGlkYXRlKGN1clJldmFsaWRhdGUsIHN0YXRpY0dlbmVyYXRpb25TdG9yZS51cmxQYXRobmFtZSk7XG4gICAgICAgICAgICBjb25zdCBfaGVhZGVycyA9IGdldFJlcXVlc3RNZXRhKFwiaGVhZGVyc1wiKTtcbiAgICAgICAgICAgIGNvbnN0IGluaXRIZWFkZXJzID0gdHlwZW9mIChfaGVhZGVycyA9PSBudWxsID8gdm9pZCAwIDogX2hlYWRlcnMuZ2V0KSA9PT0gXCJmdW5jdGlvblwiID8gX2hlYWRlcnMgOiBuZXcgSGVhZGVycyhfaGVhZGVycyB8fCB7fSk7XG4gICAgICAgICAgICBjb25zdCBoYXNVbkNhY2hlYWJsZUhlYWRlciA9IGluaXRIZWFkZXJzLmdldChcImF1dGhvcml6YXRpb25cIikgfHwgaW5pdEhlYWRlcnMuZ2V0KFwiY29va2llXCIpO1xuICAgICAgICAgICAgY29uc3QgaXNVbkNhY2hlYWJsZU1ldGhvZCA9ICFbXG4gICAgICAgICAgICAgICAgXCJnZXRcIixcbiAgICAgICAgICAgICAgICBcImhlYWRcIlxuICAgICAgICAgICAgXS5pbmNsdWRlcygoKF9nZXRSZXF1ZXN0TWV0YSA9IGdldFJlcXVlc3RNZXRhKFwibWV0aG9kXCIpKSA9PSBudWxsID8gdm9pZCAwIDogX2dldFJlcXVlc3RNZXRhLnRvTG93ZXJDYXNlKCkpIHx8IFwiZ2V0XCIpO1xuICAgICAgICAgICAgLy8gaWYgdGhlcmUgYXJlIGF1dGhvcml6ZWQgaGVhZGVycyBvciBhIFBPU1QgbWV0aG9kIGFuZFxuICAgICAgICAgICAgLy8gZHluYW1pYyBkYXRhIHVzYWdlIHdhcyBwcmVzZW50IGFib3ZlIHRoZSB0cmVlIHdlIGJhaWxcbiAgICAgICAgICAgIC8vIGUuZy4gaWYgY29va2llcygpIGlzIHVzZWQgYmVmb3JlIGFuIGF1dGhlZC9QT1NUIGZldGNoXG4gICAgICAgICAgICBjb25zdCBhdXRvTm9DYWNoZSA9IChoYXNVbkNhY2hlYWJsZUhlYWRlciB8fCBpc1VuQ2FjaGVhYmxlTWV0aG9kKSAmJiBzdGF0aWNHZW5lcmF0aW9uU3RvcmUucmV2YWxpZGF0ZSA9PT0gMDtcbiAgICAgICAgICAgIHN3aXRjaChmZXRjaENhY2hlTW9kZSl7XG4gICAgICAgICAgICAgICAgY2FzZSBcImZvcmNlLW5vLXN0b3JlXCI6XG4gICAgICAgICAgICAgICAgICAgIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNhY2hlUmVhc29uID0gXCJmZXRjaENhY2hlID0gZm9yY2Utbm8tc3RvcmVcIjtcbiAgICAgICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgY2FzZSBcIm9ubHktbm8tc3RvcmVcIjpcbiAgICAgICAgICAgICAgICAgICAge1xuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKF9jYWNoZSA9PT0gXCJmb3JjZS1jYWNoZVwiIHx8IHR5cGVvZiByZXZhbGlkYXRlICE9PSBcInVuZGVmaW5lZFwiICYmIChyZXZhbGlkYXRlID09PSBmYWxzZSB8fCByZXZhbGlkYXRlID4gMCkpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYGNhY2hlOiAnZm9yY2UtY2FjaGUnIHVzZWQgb24gZmV0Y2ggZm9yICR7ZmV0Y2hVcmx9IHdpdGggJ2V4cG9ydCBjb25zdCBmZXRjaENhY2hlID0gJ29ubHktbm8tc3RvcmUnYCk7XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICBjYWNoZVJlYXNvbiA9IFwiZmV0Y2hDYWNoZSA9IG9ubHktbm8tc3RvcmVcIjtcbiAgICAgICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgY2FzZSBcIm9ubHktY2FjaGVcIjpcbiAgICAgICAgICAgICAgICAgICAge1xuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKF9jYWNoZSA9PT0gXCJuby1zdG9yZVwiKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBjYWNoZTogJ25vLXN0b3JlJyB1c2VkIG9uIGZldGNoIGZvciAke2ZldGNoVXJsfSB3aXRoICdleHBvcnQgY29uc3QgZmV0Y2hDYWNoZSA9ICdvbmx5LWNhY2hlJ2ApO1xuICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBjYXNlIFwiZm9yY2UtY2FjaGVcIjpcbiAgICAgICAgICAgICAgICAgICAge1xuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHR5cGVvZiBjdXJSZXZhbGlkYXRlID09PSBcInVuZGVmaW5lZFwiIHx8IGN1clJldmFsaWRhdGUgPT09IDApIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjYWNoZVJlYXNvbiA9IFwiZmV0Y2hDYWNoZSA9IGZvcmNlLWNhY2hlXCI7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV2YWxpZGF0ZSA9IGZhbHNlO1xuICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBkZWZhdWx0OlxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKHR5cGVvZiByZXZhbGlkYXRlID09PSBcInVuZGVmaW5lZFwiKSB7XG4gICAgICAgICAgICAgICAgaWYgKGZldGNoQ2FjaGVNb2RlID09PSBcImRlZmF1bHQtY2FjaGVcIikge1xuICAgICAgICAgICAgICAgICAgICByZXZhbGlkYXRlID0gZmFsc2U7XG4gICAgICAgICAgICAgICAgICAgIGNhY2hlUmVhc29uID0gXCJmZXRjaENhY2hlID0gZGVmYXVsdC1jYWNoZVwiO1xuICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAoYXV0b05vQ2FjaGUpIHtcbiAgICAgICAgICAgICAgICAgICAgcmV2YWxpZGF0ZSA9IDA7XG4gICAgICAgICAgICAgICAgICAgIGNhY2hlUmVhc29uID0gXCJhdXRvIG5vIGNhY2hlXCI7XG4gICAgICAgICAgICAgICAgfSBlbHNlIGlmIChmZXRjaENhY2hlTW9kZSA9PT0gXCJkZWZhdWx0LW5vLXN0b3JlXCIpIHtcbiAgICAgICAgICAgICAgICAgICAgcmV2YWxpZGF0ZSA9IDA7XG4gICAgICAgICAgICAgICAgICAgIGNhY2hlUmVhc29uID0gXCJmZXRjaENhY2hlID0gZGVmYXVsdC1uby1zdG9yZVwiO1xuICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAoaXNVc2luZ05vU3RvcmUpIHtcbiAgICAgICAgICAgICAgICAgICAgcmV2YWxpZGF0ZSA9IDA7XG4gICAgICAgICAgICAgICAgICAgIGNhY2hlUmVhc29uID0gXCJub1N0b3JlIGNhbGxcIjtcbiAgICAgICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICBjYWNoZVJlYXNvbiA9IFwiYXV0byBjYWNoZVwiO1xuICAgICAgICAgICAgICAgICAgICByZXZhbGlkYXRlID0gdHlwZW9mIHN0YXRpY0dlbmVyYXRpb25TdG9yZS5yZXZhbGlkYXRlID09PSBcImJvb2xlYW5cIiB8fCB0eXBlb2Ygc3RhdGljR2VuZXJhdGlvblN0b3JlLnJldmFsaWRhdGUgPT09IFwidW5kZWZpbmVkXCIgPyBmYWxzZSA6IHN0YXRpY0dlbmVyYXRpb25TdG9yZS5yZXZhbGlkYXRlO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0gZWxzZSBpZiAoIWNhY2hlUmVhc29uKSB7XG4gICAgICAgICAgICAgICAgY2FjaGVSZWFzb24gPSBgcmV2YWxpZGF0ZTogJHtyZXZhbGlkYXRlfWA7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoLy8gd2hlbiBmb3JjZSBzdGF0aWMgaXMgY29uZmlndXJlZCB3ZSBkb24ndCBiYWlsIGZyb21cbiAgICAgICAgICAgIC8vIGByZXZhbGlkYXRlOiAwYCB2YWx1ZXNcbiAgICAgICAgICAgICEoc3RhdGljR2VuZXJhdGlvblN0b3JlLmZvcmNlU3RhdGljICYmIHJldmFsaWRhdGUgPT09IDApICYmIC8vIHdlIGRvbid0IGNvbnNpZGVyIGF1dG9Ob0NhY2hlIHRvIHN3aXRjaCB0byBkeW5hbWljIGR1cmluZ1xuICAgICAgICAgICAgLy8gcmV2YWxpZGF0ZSBhbHRob3VnaCBpZiBpdCBvY2N1cnMgZHVyaW5nIGJ1aWxkIHdlIGRvXG4gICAgICAgICAgICAhYXV0b05vQ2FjaGUgJiYgLy8gSWYgdGhlIHJldmFsaWRhdGUgdmFsdWUgaXNuJ3QgY3VycmVudGx5IHNldCBvciB0aGUgdmFsdWUgaXMgbGVzc1xuICAgICAgICAgICAgLy8gdGhhbiB0aGUgY3VycmVudCByZXZhbGlkYXRlIHZhbHVlLCB3ZSBzaG91bGQgdXBkYXRlIHRoZSByZXZhbGlkYXRlXG4gICAgICAgICAgICAvLyB2YWx1ZS5cbiAgICAgICAgICAgICh0eXBlb2Ygc3RhdGljR2VuZXJhdGlvblN0b3JlLnJldmFsaWRhdGUgPT09IFwidW5kZWZpbmVkXCIgfHwgdHlwZW9mIHJldmFsaWRhdGUgPT09IFwibnVtYmVyXCIgJiYgKHN0YXRpY0dlbmVyYXRpb25TdG9yZS5yZXZhbGlkYXRlID09PSBmYWxzZSB8fCB0eXBlb2Ygc3RhdGljR2VuZXJhdGlvblN0b3JlLnJldmFsaWRhdGUgPT09IFwibnVtYmVyXCIgJiYgcmV2YWxpZGF0ZSA8IHN0YXRpY0dlbmVyYXRpb25TdG9yZS5yZXZhbGlkYXRlKSkpIHtcbiAgICAgICAgICAgICAgICAvLyBJZiB3ZSB3ZXJlIHNldHRpbmcgdGhlIHJldmFsaWRhdGUgdmFsdWUgdG8gMCwgd2Ugc2hvdWxkIHRyeSB0b1xuICAgICAgICAgICAgICAgIC8vIHBvc3Rwb25lIGluc3RlYWQgZmlyc3QuXG4gICAgICAgICAgICAgICAgaWYgKHJldmFsaWRhdGUgPT09IDApIHtcbiAgICAgICAgICAgICAgICAgICAgKDAsIF9keW5hbWljcmVuZGVyaW5nLnRyYWNrRHluYW1pY0ZldGNoKShzdGF0aWNHZW5lcmF0aW9uU3RvcmUsIFwicmV2YWxpZGF0ZTogMFwiKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgc3RhdGljR2VuZXJhdGlvblN0b3JlLnJldmFsaWRhdGUgPSByZXZhbGlkYXRlO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY29uc3QgaXNDYWNoZWFibGVSZXZhbGlkYXRlID0gdHlwZW9mIHJldmFsaWRhdGUgPT09IFwibnVtYmVyXCIgJiYgcmV2YWxpZGF0ZSA+IDAgfHwgcmV2YWxpZGF0ZSA9PT0gZmFsc2U7XG4gICAgICAgICAgICBsZXQgY2FjaGVLZXk7XG4gICAgICAgICAgICBpZiAoc3RhdGljR2VuZXJhdGlvblN0b3JlLmluY3JlbWVudGFsQ2FjaGUgJiYgaXNDYWNoZWFibGVSZXZhbGlkYXRlKSB7XG4gICAgICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICAgICAgY2FjaGVLZXkgPSBhd2FpdCBzdGF0aWNHZW5lcmF0aW9uU3RvcmUuaW5jcmVtZW50YWxDYWNoZS5mZXRjaENhY2hlS2V5KGZldGNoVXJsLCBpc1JlcXVlc3RJbnB1dCA/IGlucHV0IDogaW5pdCk7XG4gICAgICAgICAgICAgICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoYEZhaWxlZCB0byBnZW5lcmF0ZSBjYWNoZSBrZXkgZm9yYCwgaW5wdXQpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNvbnN0IGZldGNoSWR4ID0gc3RhdGljR2VuZXJhdGlvblN0b3JlLm5leHRGZXRjaElkID8/IDE7XG4gICAgICAgICAgICBzdGF0aWNHZW5lcmF0aW9uU3RvcmUubmV4dEZldGNoSWQgPSBmZXRjaElkeCArIDE7XG4gICAgICAgICAgICBjb25zdCBub3JtYWxpemVkUmV2YWxpZGF0ZSA9IHR5cGVvZiByZXZhbGlkYXRlICE9PSBcIm51bWJlclwiID8gX2NvbnN0YW50czEuQ0FDSEVfT05FX1lFQVIgOiByZXZhbGlkYXRlO1xuICAgICAgICAgICAgY29uc3QgZG9PcmlnaW5hbEZldGNoID0gYXN5bmMgKGlzU3RhbGUsIGNhY2hlUmVhc29uT3ZlcnJpZGUpPT57XG4gICAgICAgICAgICAgICAgY29uc3QgcmVxdWVzdElucHV0RmllbGRzID0gW1xuICAgICAgICAgICAgICAgICAgICBcImNhY2hlXCIsXG4gICAgICAgICAgICAgICAgICAgIFwiY3JlZGVudGlhbHNcIixcbiAgICAgICAgICAgICAgICAgICAgXCJoZWFkZXJzXCIsXG4gICAgICAgICAgICAgICAgICAgIFwiaW50ZWdyaXR5XCIsXG4gICAgICAgICAgICAgICAgICAgIFwia2VlcGFsaXZlXCIsXG4gICAgICAgICAgICAgICAgICAgIFwibWV0aG9kXCIsXG4gICAgICAgICAgICAgICAgICAgIFwibW9kZVwiLFxuICAgICAgICAgICAgICAgICAgICBcInJlZGlyZWN0XCIsXG4gICAgICAgICAgICAgICAgICAgIFwicmVmZXJyZXJcIixcbiAgICAgICAgICAgICAgICAgICAgXCJyZWZlcnJlclBvbGljeVwiLFxuICAgICAgICAgICAgICAgICAgICBcIndpbmRvd1wiLFxuICAgICAgICAgICAgICAgICAgICBcImR1cGxleFwiLFxuICAgICAgICAgICAgICAgICAgICAvLyBkb24ndCBwYXNzIHRocm91Z2ggc2lnbmFsIHdoZW4gcmV2YWxpZGF0aW5nXG4gICAgICAgICAgICAgICAgICAgIC4uLmlzU3RhbGUgPyBbXSA6IFtcbiAgICAgICAgICAgICAgICAgICAgICAgIFwic2lnbmFsXCJcbiAgICAgICAgICAgICAgICAgICAgXVxuICAgICAgICAgICAgICAgIF07XG4gICAgICAgICAgICAgICAgaWYgKGlzUmVxdWVzdElucHV0KSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHJlcUlucHV0ID0gaW5wdXQ7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHJlcU9wdGlvbnMgPSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBib2R5OiByZXFJbnB1dC5fb2dCb2R5IHx8IHJlcUlucHV0LmJvZHlcbiAgICAgICAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgICAgICAgICAgZm9yIChjb25zdCBmaWVsZCBvZiByZXF1ZXN0SW5wdXRGaWVsZHMpe1xuICAgICAgICAgICAgICAgICAgICAgICAgLy8gQHRzLWV4cGVjdC1lcnJvciBjdXN0b20gZmllbGRzXG4gICAgICAgICAgICAgICAgICAgICAgICByZXFPcHRpb25zW2ZpZWxkXSA9IHJlcUlucHV0W2ZpZWxkXTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICBpbnB1dCA9IG5ldyBSZXF1ZXN0KHJlcUlucHV0LnVybCwgcmVxT3B0aW9ucyk7XG4gICAgICAgICAgICAgICAgfSBlbHNlIGlmIChpbml0KSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHsgX29nQm9keSwgYm9keSwgc2lnbmFsLCAuLi5vdGhlcklucHV0IH0gPSBpbml0O1xuICAgICAgICAgICAgICAgICAgICBpbml0ID0ge1xuICAgICAgICAgICAgICAgICAgICAgICAgLi4ub3RoZXJJbnB1dCxcbiAgICAgICAgICAgICAgICAgICAgICAgIGJvZHk6IF9vZ0JvZHkgfHwgYm9keSxcbiAgICAgICAgICAgICAgICAgICAgICAgIHNpZ25hbDogaXNTdGFsZSA/IHVuZGVmaW5lZCA6IHNpZ25hbFxuICAgICAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAvLyBhZGQgbWV0YWRhdGEgdG8gaW5pdCB3aXRob3V0IGVkaXRpbmcgdGhlIG9yaWdpbmFsXG4gICAgICAgICAgICAgICAgY29uc3QgY2xvbmVkSW5pdCA9IHtcbiAgICAgICAgICAgICAgICAgICAgLi4uaW5pdCxcbiAgICAgICAgICAgICAgICAgICAgbmV4dDoge1xuICAgICAgICAgICAgICAgICAgICAgICAgLi4uaW5pdCA9PSBudWxsID8gdm9pZCAwIDogaW5pdC5uZXh0LFxuICAgICAgICAgICAgICAgICAgICAgICAgZmV0Y2hUeXBlOiBcIm9yaWdpblwiLFxuICAgICAgICAgICAgICAgICAgICAgICAgZmV0Y2hJZHhcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICAgICAgcmV0dXJuIG9yaWdpbkZldGNoKGlucHV0LCBjbG9uZWRJbml0KS50aGVuKGFzeW5jIChyZXMpPT57XG4gICAgICAgICAgICAgICAgICAgIGlmICghaXNTdGFsZSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgdHJhY2tGZXRjaE1ldHJpYyhzdGF0aWNHZW5lcmF0aW9uU3RvcmUsIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdGFydDogZmV0Y2hTdGFydCxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB1cmw6IGZldGNoVXJsLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNhY2hlUmVhc29uOiBjYWNoZVJlYXNvbk92ZXJyaWRlIHx8IGNhY2hlUmVhc29uLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNhY2hlU3RhdHVzOiByZXZhbGlkYXRlID09PSAwIHx8IGNhY2hlUmVhc29uT3ZlcnJpZGUgPyBcInNraXBcIiA6IFwibWlzc1wiLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0YXR1czogcmVzLnN0YXR1cyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBtZXRob2Q6IGNsb25lZEluaXQubWV0aG9kIHx8IFwiR0VUXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIGlmIChyZXMuc3RhdHVzID09PSAyMDAgJiYgc3RhdGljR2VuZXJhdGlvblN0b3JlLmluY3JlbWVudGFsQ2FjaGUgJiYgY2FjaGVLZXkgJiYgaXNDYWNoZWFibGVSZXZhbGlkYXRlKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBib2R5QnVmZmVyID0gQnVmZmVyLmZyb20oYXdhaXQgcmVzLmFycmF5QnVmZmVyKCkpO1xuICAgICAgICAgICAgICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBhd2FpdCBzdGF0aWNHZW5lcmF0aW9uU3RvcmUuaW5jcmVtZW50YWxDYWNoZS5zZXQoY2FjaGVLZXksIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAga2luZDogXCJGRVRDSFwiLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkYXRhOiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBoZWFkZXJzOiBPYmplY3QuZnJvbUVudHJpZXMocmVzLmhlYWRlcnMuZW50cmllcygpKSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJvZHk6IGJvZHlCdWZmZXIudG9TdHJpbmcoXCJiYXNlNjRcIiksXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdGF0dXM6IHJlcy5zdGF0dXMsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB1cmw6IHJlcy51cmxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV2YWxpZGF0ZTogbm9ybWFsaXplZFJldmFsaWRhdGVcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LCB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZldGNoQ2FjaGU6IHRydWUsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldmFsaWRhdGUsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZldGNoVXJsLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmZXRjaElkeCxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGFnc1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgICAgICAgICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc29sZS53YXJuKGBGYWlsZWQgdG8gc2V0IGZldGNoIGNhY2hlYCwgaW5wdXQsIGVycik7XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCByZXNwb25zZSA9IG5ldyBSZXNwb25zZShib2R5QnVmZmVyLCB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaGVhZGVyczogbmV3IEhlYWRlcnMocmVzLmhlYWRlcnMpLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0YXR1czogcmVzLnN0YXR1c1xuICAgICAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgICAgICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkocmVzcG9uc2UsIFwidXJsXCIsIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZTogcmVzLnVybFxuICAgICAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gcmVzcG9uc2U7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHJlcztcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIH07XG4gICAgICAgICAgICBsZXQgaGFuZGxlVW5sb2NrID0gKCk9PlByb21pc2UucmVzb2x2ZSgpO1xuICAgICAgICAgICAgbGV0IGNhY2hlUmVhc29uT3ZlcnJpZGU7XG4gICAgICAgICAgICBsZXQgaXNGb3JlZ3JvdW5kUmV2YWxpZGF0ZSA9IGZhbHNlO1xuICAgICAgICAgICAgaWYgKGNhY2hlS2V5ICYmIHN0YXRpY0dlbmVyYXRpb25TdG9yZS5pbmNyZW1lbnRhbENhY2hlKSB7XG4gICAgICAgICAgICAgICAgaGFuZGxlVW5sb2NrID0gYXdhaXQgc3RhdGljR2VuZXJhdGlvblN0b3JlLmluY3JlbWVudGFsQ2FjaGUubG9jayhjYWNoZUtleSk7XG4gICAgICAgICAgICAgICAgY29uc3QgZW50cnkgPSBzdGF0aWNHZW5lcmF0aW9uU3RvcmUuaXNPbkRlbWFuZFJldmFsaWRhdGUgPyBudWxsIDogYXdhaXQgc3RhdGljR2VuZXJhdGlvblN0b3JlLmluY3JlbWVudGFsQ2FjaGUuZ2V0KGNhY2hlS2V5LCB7XG4gICAgICAgICAgICAgICAgICAgIGtpbmRIaW50OiBcImZldGNoXCIsXG4gICAgICAgICAgICAgICAgICAgIHJldmFsaWRhdGUsXG4gICAgICAgICAgICAgICAgICAgIGZldGNoVXJsLFxuICAgICAgICAgICAgICAgICAgICBmZXRjaElkeCxcbiAgICAgICAgICAgICAgICAgICAgdGFncyxcbiAgICAgICAgICAgICAgICAgICAgc29mdFRhZ3M6IGltcGxpY2l0VGFnc1xuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgIGlmIChlbnRyeSkge1xuICAgICAgICAgICAgICAgICAgICBhd2FpdCBoYW5kbGVVbmxvY2soKTtcbiAgICAgICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICAvLyBpbiBkZXYsIGluY3JlbWVudGFsIGNhY2hlIHJlc3BvbnNlIHdpbGwgYmUgbnVsbCBpbiBjYXNlIHRoZSBicm93c2VyIGFkZHMgYGNhY2hlLWNvbnRyb2w6IG5vLWNhY2hlYCBpbiB0aGUgcmVxdWVzdCBoZWFkZXJzXG4gICAgICAgICAgICAgICAgICAgIGNhY2hlUmVhc29uT3ZlcnJpZGUgPSBcImNhY2hlLWNvbnRyb2w6IG5vLWNhY2hlIChoYXJkIHJlZnJlc2gpXCI7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGlmICgoZW50cnkgPT0gbnVsbCA/IHZvaWQgMCA6IGVudHJ5LnZhbHVlKSAmJiBlbnRyeS52YWx1ZS5raW5kID09PSBcIkZFVENIXCIpIHtcbiAgICAgICAgICAgICAgICAgICAgLy8gd2hlbiBzdGFsZSBhbmQgaXMgcmV2YWxpZGF0aW5nIHdlIHdhaXQgZm9yIGZyZXNoIGRhdGFcbiAgICAgICAgICAgICAgICAgICAgLy8gc28gdGhlIHJldmFsaWRhdGVkIGVudHJ5IGhhcyB0aGUgdXBkYXRlZCBkYXRhXG4gICAgICAgICAgICAgICAgICAgIGlmIChzdGF0aWNHZW5lcmF0aW9uU3RvcmUuaXNSZXZhbGlkYXRlICYmIGVudHJ5LmlzU3RhbGUpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGlzRm9yZWdyb3VuZFJldmFsaWRhdGUgPSB0cnVlO1xuICAgICAgICAgICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGVudHJ5LmlzU3RhbGUpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdGF0aWNHZW5lcmF0aW9uU3RvcmUucGVuZGluZ1JldmFsaWRhdGVzID8/PSB7fTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAoIXN0YXRpY0dlbmVyYXRpb25TdG9yZS5wZW5kaW5nUmV2YWxpZGF0ZXNbY2FjaGVLZXldKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHBlbmRpbmdSZXZhbGlkYXRlID0gZG9PcmlnaW5hbEZldGNoKHRydWUpLnRoZW4oYXN5bmMgKHJlc3BvbnNlKT0+KHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBib2R5OiBhd2FpdCByZXNwb25zZS5hcnJheUJ1ZmZlcigpLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhlYWRlcnM6IHJlc3BvbnNlLmhlYWRlcnMsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3RhdHVzOiByZXNwb25zZS5zdGF0dXMsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3RhdHVzVGV4dDogcmVzcG9uc2Uuc3RhdHVzVGV4dFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSkpLmZpbmFsbHkoKCk9PntcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0YXRpY0dlbmVyYXRpb25TdG9yZS5wZW5kaW5nUmV2YWxpZGF0ZXMgPz89IHt9O1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGVsZXRlIHN0YXRpY0dlbmVyYXRpb25TdG9yZS5wZW5kaW5nUmV2YWxpZGF0ZXNbY2FjaGVLZXkgfHwgXCJcIl07XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyBBdHRhY2ggdGhlIGVtcHR5IGNhdGNoIGhlcmUgc28gd2UgZG9uJ3QgZ2V0IGEgXCJ1bmhhbmRsZWRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8gcHJvbWlzZSByZWplY3Rpb25cIiB3YXJuaW5nLlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwZW5kaW5nUmV2YWxpZGF0ZS5jYXRjaChjb25zb2xlLmVycm9yKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3RhdGljR2VuZXJhdGlvblN0b3JlLnBlbmRpbmdSZXZhbGlkYXRlc1tjYWNoZUtleV0gPSBwZW5kaW5nUmV2YWxpZGF0ZTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCByZXNEYXRhID0gZW50cnkudmFsdWUuZGF0YTtcbiAgICAgICAgICAgICAgICAgICAgICAgIHRyYWNrRmV0Y2hNZXRyaWMoc3RhdGljR2VuZXJhdGlvblN0b3JlLCB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc3RhcnQ6IGZldGNoU3RhcnQsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdXJsOiBmZXRjaFVybCxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjYWNoZVJlYXNvbixcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjYWNoZVN0YXR1czogXCJoaXRcIixcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdGF0dXM6IHJlc0RhdGEuc3RhdHVzIHx8IDIwMCxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBtZXRob2Q6IChpbml0ID09IG51bGwgPyB2b2lkIDAgOiBpbml0Lm1ldGhvZCkgfHwgXCJHRVRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCByZXNwb25zZSA9IG5ldyBSZXNwb25zZShCdWZmZXIuZnJvbShyZXNEYXRhLmJvZHksIFwiYmFzZTY0XCIpLCB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaGVhZGVyczogcmVzRGF0YS5oZWFkZXJzLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0YXR1czogcmVzRGF0YS5zdGF0dXNcbiAgICAgICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgICAgICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KHJlc3BvbnNlLCBcInVybFwiLCB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU6IGVudHJ5LnZhbHVlLmRhdGEudXJsXG4gICAgICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiByZXNwb25zZTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmIChzdGF0aWNHZW5lcmF0aW9uU3RvcmUuaXNTdGF0aWNHZW5lcmF0aW9uICYmIGluaXQgJiYgdHlwZW9mIGluaXQgPT09IFwib2JqZWN0XCIpIHtcbiAgICAgICAgICAgICAgICBjb25zdCB7IGNhY2hlIH0gPSBpbml0O1xuICAgICAgICAgICAgICAgIC8vIERlbGV0ZSBgY2FjaGVgIHByb3BlcnR5IGFzIENsb3VkZmxhcmUgV29ya2VycyB3aWxsIHRocm93IGFuIGVycm9yXG4gICAgICAgICAgICAgICAgaWYgKGlzRWRnZVJ1bnRpbWUpIGRlbGV0ZSBpbml0LmNhY2hlO1xuICAgICAgICAgICAgICAgIGlmICghc3RhdGljR2VuZXJhdGlvblN0b3JlLmZvcmNlU3RhdGljICYmIGNhY2hlID09PSBcIm5vLXN0b3JlXCIpIHtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgZHluYW1pY1VzYWdlUmVhc29uID0gYG5vLXN0b3JlIGZldGNoICR7aW5wdXR9JHtzdGF0aWNHZW5lcmF0aW9uU3RvcmUudXJsUGF0aG5hbWUgPyBgICR7c3RhdGljR2VuZXJhdGlvblN0b3JlLnVybFBhdGhuYW1lfWAgOiBcIlwifWA7XG4gICAgICAgICAgICAgICAgICAgIC8vIElmIGVuYWJsZWQsIHdlIHNob3VsZCBiYWlsIG91dCBvZiBzdGF0aWMgZ2VuZXJhdGlvbi5cbiAgICAgICAgICAgICAgICAgICAgKDAsIF9keW5hbWljcmVuZGVyaW5nLnRyYWNrRHluYW1pY0ZldGNoKShzdGF0aWNHZW5lcmF0aW9uU3RvcmUsIGR5bmFtaWNVc2FnZVJlYXNvbik7XG4gICAgICAgICAgICAgICAgICAgIC8vIFBQUiBpcyBub3QgZW5hYmxlZCwgb3IgUmVhY3QgcG9zdHBvbmUgaXMgbm90IGF2YWlsYWJsZSwgd2VcbiAgICAgICAgICAgICAgICAgICAgLy8gc2hvdWxkIHNldCB0aGUgcmV2YWxpZGF0ZSB0byAwLlxuICAgICAgICAgICAgICAgICAgICBzdGF0aWNHZW5lcmF0aW9uU3RvcmUucmV2YWxpZGF0ZSA9IDA7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGVyciA9IG5ldyBEeW5hbWljU2VydmVyRXJyb3IoZHluYW1pY1VzYWdlUmVhc29uKTtcbiAgICAgICAgICAgICAgICAgICAgc3RhdGljR2VuZXJhdGlvblN0b3JlLmR5bmFtaWNVc2FnZUVyciA9IGVycjtcbiAgICAgICAgICAgICAgICAgICAgc3RhdGljR2VuZXJhdGlvblN0b3JlLmR5bmFtaWNVc2FnZURlc2NyaXB0aW9uID0gZHluYW1pY1VzYWdlUmVhc29uO1xuICAgICAgICAgICAgICAgICAgICB0aHJvdyBlcnI7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGNvbnN0IGhhc05leHRDb25maWcgPSBcIm5leHRcIiBpbiBpbml0O1xuICAgICAgICAgICAgICAgIGNvbnN0IHsgbmV4dCA9IHt9IH0gPSBpbml0O1xuICAgICAgICAgICAgICAgIGlmICh0eXBlb2YgbmV4dC5yZXZhbGlkYXRlID09PSBcIm51bWJlclwiICYmICh0eXBlb2Ygc3RhdGljR2VuZXJhdGlvblN0b3JlLnJldmFsaWRhdGUgPT09IFwidW5kZWZpbmVkXCIgfHwgdHlwZW9mIHN0YXRpY0dlbmVyYXRpb25TdG9yZS5yZXZhbGlkYXRlID09PSBcIm51bWJlclwiICYmIG5leHQucmV2YWxpZGF0ZSA8IHN0YXRpY0dlbmVyYXRpb25TdG9yZS5yZXZhbGlkYXRlKSkge1xuICAgICAgICAgICAgICAgICAgICBpZiAoIXN0YXRpY0dlbmVyYXRpb25TdG9yZS5mb3JjZUR5bmFtaWMgJiYgIXN0YXRpY0dlbmVyYXRpb25TdG9yZS5mb3JjZVN0YXRpYyAmJiBuZXh0LnJldmFsaWRhdGUgPT09IDApIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGR5bmFtaWNVc2FnZVJlYXNvbiA9IGByZXZhbGlkYXRlOiAwIGZldGNoICR7aW5wdXR9JHtzdGF0aWNHZW5lcmF0aW9uU3RvcmUudXJsUGF0aG5hbWUgPyBgICR7c3RhdGljR2VuZXJhdGlvblN0b3JlLnVybFBhdGhuYW1lfWAgOiBcIlwifWA7XG4gICAgICAgICAgICAgICAgICAgICAgICAvLyBJZiBlbmFibGVkLCB3ZSBzaG91bGQgYmFpbCBvdXQgb2Ygc3RhdGljIGdlbmVyYXRpb24uXG4gICAgICAgICAgICAgICAgICAgICAgICAoMCwgX2R5bmFtaWNyZW5kZXJpbmcudHJhY2tEeW5hbWljRmV0Y2gpKHN0YXRpY0dlbmVyYXRpb25TdG9yZSwgZHluYW1pY1VzYWdlUmVhc29uKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGVyciA9IG5ldyBEeW5hbWljU2VydmVyRXJyb3IoZHluYW1pY1VzYWdlUmVhc29uKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIHN0YXRpY0dlbmVyYXRpb25TdG9yZS5keW5hbWljVXNhZ2VFcnIgPSBlcnI7XG4gICAgICAgICAgICAgICAgICAgICAgICBzdGF0aWNHZW5lcmF0aW9uU3RvcmUuZHluYW1pY1VzYWdlRGVzY3JpcHRpb24gPSBkeW5hbWljVXNhZ2VSZWFzb247XG4gICAgICAgICAgICAgICAgICAgICAgICB0aHJvdyBlcnI7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgaWYgKCFzdGF0aWNHZW5lcmF0aW9uU3RvcmUuZm9yY2VTdGF0aWMgfHwgbmV4dC5yZXZhbGlkYXRlICE9PSAwKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBzdGF0aWNHZW5lcmF0aW9uU3RvcmUucmV2YWxpZGF0ZSA9IG5leHQucmV2YWxpZGF0ZTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBpZiAoaGFzTmV4dENvbmZpZykgZGVsZXRlIGluaXQubmV4dDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIC8vIGlmIHdlIGFyZSByZXZhbGlkYXRpbmcgdGhlIHdob2xlIHBhZ2UgdmlhIHRpbWUgb3Igb24tZGVtYW5kIGFuZFxuICAgICAgICAgICAgLy8gdGhlIGZldGNoIGNhY2hlIGVudHJ5IGlzIHN0YWxlIHdlIHNob3VsZCBzdGlsbCBkZS1kdXBlIHRoZVxuICAgICAgICAgICAgLy8gb3JpZ2luIGhpdCBpZiBpdCdzIGEgY2FjaGUtYWJsZSBlbnRyeVxuICAgICAgICAgICAgaWYgKGNhY2hlS2V5ICYmIGlzRm9yZWdyb3VuZFJldmFsaWRhdGUpIHtcbiAgICAgICAgICAgICAgICBzdGF0aWNHZW5lcmF0aW9uU3RvcmUucGVuZGluZ1JldmFsaWRhdGVzID8/PSB7fTtcbiAgICAgICAgICAgICAgICBsZXQgcGVuZGluZ1JldmFsaWRhdGUgPSBzdGF0aWNHZW5lcmF0aW9uU3RvcmUucGVuZGluZ1JldmFsaWRhdGVzW2NhY2hlS2V5XTtcbiAgICAgICAgICAgICAgICBpZiAocGVuZGluZ1JldmFsaWRhdGUpIHtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgcmV2YWxpZGF0ZWRSZXN1bHQgPSBhd2FpdCBwZW5kaW5nUmV2YWxpZGF0ZTtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIG5ldyBSZXNwb25zZShyZXZhbGlkYXRlZFJlc3VsdC5ib2R5LCB7XG4gICAgICAgICAgICAgICAgICAgICAgICBoZWFkZXJzOiByZXZhbGlkYXRlZFJlc3VsdC5oZWFkZXJzLFxuICAgICAgICAgICAgICAgICAgICAgICAgc3RhdHVzOiByZXZhbGlkYXRlZFJlc3VsdC5zdGF0dXMsXG4gICAgICAgICAgICAgICAgICAgICAgICBzdGF0dXNUZXh0OiByZXZhbGlkYXRlZFJlc3VsdC5zdGF0dXNUZXh0XG4gICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBjb25zdCBwZW5kaW5nUmVzcG9uc2UgPSBkb09yaWdpbmFsRmV0Y2godHJ1ZSwgY2FjaGVSZWFzb25PdmVycmlkZSkvLyBXZSdyZSBjbG9uaW5nIHRoZSByZXNwb25zZSB1c2luZyB0aGlzIHV0aWxpdHkgYmVjYXVzZSB0aGVyZVxuICAgICAgICAgICAgICAgIC8vIGV4aXN0cyBhIGJ1ZyBpbiB0aGUgdW5kaWNpIGxpYnJhcnkgYXJvdW5kIHJlc3BvbnNlIGNsb25pbmcuXG4gICAgICAgICAgICAgICAgLy8gU2VlIHRoZSBmb2xsb3dpbmcgcHVsbCByZXF1ZXN0IGZvciBtb3JlIGRldGFpbHM6XG4gICAgICAgICAgICAgICAgLy8gaHR0cHM6Ly9naXRodWIuY29tL3ZlcmNlbC9uZXh0LmpzL3B1bGwvNzMyNzRcbiAgICAgICAgICAgICAgICAudGhlbihfY2xvbmVyZXNwb25zZS5jbG9uZVJlc3BvbnNlKTtcbiAgICAgICAgICAgICAgICBwZW5kaW5nUmV2YWxpZGF0ZSA9IHBlbmRpbmdSZXNwb25zZS50aGVuKGFzeW5jIChyZXNwb25zZXMpPT57XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHJlc3BvbnNlID0gcmVzcG9uc2VzWzBdO1xuICAgICAgICAgICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgYm9keTogYXdhaXQgcmVzcG9uc2UuYXJyYXlCdWZmZXIoKSxcbiAgICAgICAgICAgICAgICAgICAgICAgIGhlYWRlcnM6IHJlc3BvbnNlLmhlYWRlcnMsXG4gICAgICAgICAgICAgICAgICAgICAgICBzdGF0dXM6IHJlc3BvbnNlLnN0YXR1cyxcbiAgICAgICAgICAgICAgICAgICAgICAgIHN0YXR1c1RleHQ6IHJlc3BvbnNlLnN0YXR1c1RleHRcbiAgICAgICAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgICAgICB9KS5maW5hbGx5KCgpPT57XG4gICAgICAgICAgICAgICAgICAgIGlmIChjYWNoZUtleSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgdmFyIF9zdGF0aWNHZW5lcmF0aW9uU3RvcmVfcGVuZGluZ1JldmFsaWRhdGVzO1xuICAgICAgICAgICAgICAgICAgICAgICAgLy8gSWYgdGhlIHBlbmRpbmcgcmV2YWxpZGF0ZSBpcyBub3QgcHJlc2VudCBpbiB0aGUgc3RvcmUsIHRoZW5cbiAgICAgICAgICAgICAgICAgICAgICAgIC8vIHdlIGhhdmUgbm90aGluZyB0byBkZWxldGUuXG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoISgoX3N0YXRpY0dlbmVyYXRpb25TdG9yZV9wZW5kaW5nUmV2YWxpZGF0ZXMgPSBzdGF0aWNHZW5lcmF0aW9uU3RvcmUucGVuZGluZ1JldmFsaWRhdGVzKSA9PSBudWxsID8gdm9pZCAwIDogX3N0YXRpY0dlbmVyYXRpb25TdG9yZV9wZW5kaW5nUmV2YWxpZGF0ZXNbY2FjaGVLZXldKSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgIGRlbGV0ZSBzdGF0aWNHZW5lcmF0aW9uU3RvcmUucGVuZGluZ1JldmFsaWRhdGVzW2NhY2hlS2V5XTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgIC8vIEF0dGFjaCB0aGUgZW1wdHkgY2F0Y2ggaGVyZSBzbyB3ZSBkb24ndCBnZXQgYSBcInVuaGFuZGxlZCBwcm9taXNlXG4gICAgICAgICAgICAgICAgLy8gcmVqZWN0aW9uXCIgd2FybmluZ1xuICAgICAgICAgICAgICAgIHBlbmRpbmdSZXZhbGlkYXRlLmNhdGNoKCgpPT57fSk7XG4gICAgICAgICAgICAgICAgc3RhdGljR2VuZXJhdGlvblN0b3JlLnBlbmRpbmdSZXZhbGlkYXRlc1tjYWNoZUtleV0gPSBwZW5kaW5nUmV2YWxpZGF0ZTtcbiAgICAgICAgICAgICAgICByZXR1cm4gcGVuZGluZ1Jlc3BvbnNlLnRoZW4oKHJlc3BvbnNlcyk9PnJlc3BvbnNlc1sxXSk7XG4gICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgIHJldHVybiBkb09yaWdpbmFsRmV0Y2goZmFsc2UsIGNhY2hlUmVhc29uT3ZlcnJpZGUpLmZpbmFsbHkoaGFuZGxlVW5sb2NrKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSk7XG4gICAgfTtcbiAgICAvLyBBdHRhY2ggdGhlIG5lY2Vzc2FyeSBwcm9wZXJ0aWVzIHRvIHRoZSBwYXRjaGVkIGZldGNoIGZ1bmN0aW9uLlxuICAgIHBhdGNoZWQuX19uZXh0UGF0Y2hlZCA9IHRydWU7XG4gICAgcGF0Y2hlZC5fX25leHRHZXRTdGF0aWNTdG9yZSA9ICgpPT5zdGF0aWNHZW5lcmF0aW9uQXN5bmNTdG9yYWdlO1xuICAgIHBhdGNoZWQuX25leHRPcmlnaW5hbEZldGNoID0gb3JpZ2luRmV0Y2g7XG4gICAgcmV0dXJuIHBhdGNoZWQ7XG59XG5mdW5jdGlvbiBwYXRjaEZldGNoKG9wdGlvbnMpIHtcbiAgICAvLyBJZiB3ZSd2ZSBhbHJlYWR5IHBhdGNoZWQgZmV0Y2gsIHdlIHNob3VsZCBub3QgcGF0Y2ggaXQgYWdhaW4uXG4gICAgaWYgKGlzUGF0Y2hlZEZldGNoKGdsb2JhbFRoaXMuZmV0Y2gpKSByZXR1cm47XG4gICAgLy8gR3JhYiB0aGUgb3JpZ2luYWwgZmV0Y2ggZnVuY3Rpb24uIFdlJ2xsIGF0dGFjaCB0aGlzIHNvIHdlIGNhbiB1c2UgaXQgaW5cbiAgICAvLyB0aGUgcGF0Y2hlZCBmZXRjaCBmdW5jdGlvbi5cbiAgICBjb25zdCBvcmlnaW5hbCA9ICgwLCBfZGVkdXBlZmV0Y2guY3JlYXRlRGVkdXBlRmV0Y2gpKGdsb2JhbFRoaXMuZmV0Y2gpO1xuICAgIC8vIFNldCB0aGUgZ2xvYmFsIGZldGNoIHRvIHRoZSBwYXRjaGVkIGZldGNoLlxuICAgIGdsb2JhbFRoaXMuZmV0Y2ggPSBjcmVhdGVQYXRjaGVkRmV0Y2hlcihvcmlnaW5hbCwgb3B0aW9ucyk7XG59XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXBhdGNoLWZldGNoLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/server/lib/trace/constants.js":
/*!**************************************************************!*\
  !*** ./node_modules/next/dist/server/lib/trace/constants.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("/**\n * Contains predefined constants for the trace span name in next/server.\n *\n * Currently, next/server/tracer is internal implementation only for tracking\n * next.js's implementation only with known span names defined here.\n **/ // eslint typescript has a bug with TS enums\n/* eslint-disable no-shadow */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    AppRenderSpan: function() {\n        return AppRenderSpan;\n    },\n    AppRouteRouteHandlersSpan: function() {\n        return AppRouteRouteHandlersSpan;\n    },\n    BaseServerSpan: function() {\n        return BaseServerSpan;\n    },\n    LoadComponentsSpan: function() {\n        return LoadComponentsSpan;\n    },\n    LogSpanAllowList: function() {\n        return LogSpanAllowList;\n    },\n    MiddlewareSpan: function() {\n        return MiddlewareSpan;\n    },\n    NextNodeServerSpan: function() {\n        return NextNodeServerSpan;\n    },\n    NextServerSpan: function() {\n        return NextServerSpan;\n    },\n    NextVanillaSpanAllowlist: function() {\n        return NextVanillaSpanAllowlist;\n    },\n    NodeSpan: function() {\n        return NodeSpan;\n    },\n    RenderSpan: function() {\n        return RenderSpan;\n    },\n    ResolveMetadataSpan: function() {\n        return ResolveMetadataSpan;\n    },\n    RouterSpan: function() {\n        return RouterSpan;\n    },\n    StartServerSpan: function() {\n        return StartServerSpan;\n    }\n});\nvar BaseServerSpan;\n(function(BaseServerSpan) {\n    BaseServerSpan[\"handleRequest\"] = \"BaseServer.handleRequest\";\n    BaseServerSpan[\"run\"] = \"BaseServer.run\";\n    BaseServerSpan[\"pipe\"] = \"BaseServer.pipe\";\n    BaseServerSpan[\"getStaticHTML\"] = \"BaseServer.getStaticHTML\";\n    BaseServerSpan[\"render\"] = \"BaseServer.render\";\n    BaseServerSpan[\"renderToResponseWithComponents\"] = \"BaseServer.renderToResponseWithComponents\";\n    BaseServerSpan[\"renderToResponse\"] = \"BaseServer.renderToResponse\";\n    BaseServerSpan[\"renderToHTML\"] = \"BaseServer.renderToHTML\";\n    BaseServerSpan[\"renderError\"] = \"BaseServer.renderError\";\n    BaseServerSpan[\"renderErrorToResponse\"] = \"BaseServer.renderErrorToResponse\";\n    BaseServerSpan[\"renderErrorToHTML\"] = \"BaseServer.renderErrorToHTML\";\n    BaseServerSpan[\"render404\"] = \"BaseServer.render404\";\n})(BaseServerSpan || (BaseServerSpan = {}));\nvar LoadComponentsSpan;\n(function(LoadComponentsSpan) {\n    LoadComponentsSpan[\"loadDefaultErrorComponents\"] = \"LoadComponents.loadDefaultErrorComponents\";\n    LoadComponentsSpan[\"loadComponents\"] = \"LoadComponents.loadComponents\";\n})(LoadComponentsSpan || (LoadComponentsSpan = {}));\nvar NextServerSpan;\n(function(NextServerSpan) {\n    NextServerSpan[\"getRequestHandler\"] = \"NextServer.getRequestHandler\";\n    NextServerSpan[\"getServer\"] = \"NextServer.getServer\";\n    NextServerSpan[\"getServerRequestHandler\"] = \"NextServer.getServerRequestHandler\";\n    NextServerSpan[\"createServer\"] = \"createServer.createServer\";\n})(NextServerSpan || (NextServerSpan = {}));\nvar NextNodeServerSpan;\n(function(NextNodeServerSpan) {\n    NextNodeServerSpan[\"compression\"] = \"NextNodeServer.compression\";\n    NextNodeServerSpan[\"getBuildId\"] = \"NextNodeServer.getBuildId\";\n    NextNodeServerSpan[\"createComponentTree\"] = \"NextNodeServer.createComponentTree\";\n    NextNodeServerSpan[\"clientComponentLoading\"] = \"NextNodeServer.clientComponentLoading\";\n    NextNodeServerSpan[\"getLayoutOrPageModule\"] = \"NextNodeServer.getLayoutOrPageModule\";\n    NextNodeServerSpan[\"generateStaticRoutes\"] = \"NextNodeServer.generateStaticRoutes\";\n    NextNodeServerSpan[\"generateFsStaticRoutes\"] = \"NextNodeServer.generateFsStaticRoutes\";\n    NextNodeServerSpan[\"generatePublicRoutes\"] = \"NextNodeServer.generatePublicRoutes\";\n    NextNodeServerSpan[\"generateImageRoutes\"] = \"NextNodeServer.generateImageRoutes.route\";\n    NextNodeServerSpan[\"sendRenderResult\"] = \"NextNodeServer.sendRenderResult\";\n    NextNodeServerSpan[\"proxyRequest\"] = \"NextNodeServer.proxyRequest\";\n    NextNodeServerSpan[\"runApi\"] = \"NextNodeServer.runApi\";\n    NextNodeServerSpan[\"render\"] = \"NextNodeServer.render\";\n    NextNodeServerSpan[\"renderHTML\"] = \"NextNodeServer.renderHTML\";\n    NextNodeServerSpan[\"imageOptimizer\"] = \"NextNodeServer.imageOptimizer\";\n    NextNodeServerSpan[\"getPagePath\"] = \"NextNodeServer.getPagePath\";\n    NextNodeServerSpan[\"getRoutesManifest\"] = \"NextNodeServer.getRoutesManifest\";\n    NextNodeServerSpan[\"findPageComponents\"] = \"NextNodeServer.findPageComponents\";\n    NextNodeServerSpan[\"getFontManifest\"] = \"NextNodeServer.getFontManifest\";\n    NextNodeServerSpan[\"getServerComponentManifest\"] = \"NextNodeServer.getServerComponentManifest\";\n    NextNodeServerSpan[\"getRequestHandler\"] = \"NextNodeServer.getRequestHandler\";\n    NextNodeServerSpan[\"renderToHTML\"] = \"NextNodeServer.renderToHTML\";\n    NextNodeServerSpan[\"renderError\"] = \"NextNodeServer.renderError\";\n    NextNodeServerSpan[\"renderErrorToHTML\"] = \"NextNodeServer.renderErrorToHTML\";\n    NextNodeServerSpan[\"render404\"] = \"NextNodeServer.render404\";\n    NextNodeServerSpan[\"startResponse\"] = \"NextNodeServer.startResponse\";\n    // nested inner span, does not require parent scope name\n    NextNodeServerSpan[\"route\"] = \"route\";\n    NextNodeServerSpan[\"onProxyReq\"] = \"onProxyReq\";\n    NextNodeServerSpan[\"apiResolver\"] = \"apiResolver\";\n    NextNodeServerSpan[\"internalFetch\"] = \"internalFetch\";\n})(NextNodeServerSpan || (NextNodeServerSpan = {}));\nvar StartServerSpan;\n(function(StartServerSpan) {\n    StartServerSpan[\"startServer\"] = \"startServer.startServer\";\n})(StartServerSpan || (StartServerSpan = {}));\nvar RenderSpan;\n(function(RenderSpan) {\n    RenderSpan[\"getServerSideProps\"] = \"Render.getServerSideProps\";\n    RenderSpan[\"getStaticProps\"] = \"Render.getStaticProps\";\n    RenderSpan[\"renderToString\"] = \"Render.renderToString\";\n    RenderSpan[\"renderDocument\"] = \"Render.renderDocument\";\n    RenderSpan[\"createBodyResult\"] = \"Render.createBodyResult\";\n})(RenderSpan || (RenderSpan = {}));\nvar AppRenderSpan;\n(function(AppRenderSpan) {\n    AppRenderSpan[\"renderToString\"] = \"AppRender.renderToString\";\n    AppRenderSpan[\"renderToReadableStream\"] = \"AppRender.renderToReadableStream\";\n    AppRenderSpan[\"getBodyResult\"] = \"AppRender.getBodyResult\";\n    AppRenderSpan[\"fetch\"] = \"AppRender.fetch\";\n})(AppRenderSpan || (AppRenderSpan = {}));\nvar RouterSpan;\n(function(RouterSpan) {\n    RouterSpan[\"executeRoute\"] = \"Router.executeRoute\";\n})(RouterSpan || (RouterSpan = {}));\nvar NodeSpan;\n(function(NodeSpan) {\n    NodeSpan[\"runHandler\"] = \"Node.runHandler\";\n})(NodeSpan || (NodeSpan = {}));\nvar AppRouteRouteHandlersSpan;\n(function(AppRouteRouteHandlersSpan) {\n    AppRouteRouteHandlersSpan[\"runHandler\"] = \"AppRouteRouteHandlers.runHandler\";\n})(AppRouteRouteHandlersSpan || (AppRouteRouteHandlersSpan = {}));\nvar ResolveMetadataSpan;\n(function(ResolveMetadataSpan) {\n    ResolveMetadataSpan[\"generateMetadata\"] = \"ResolveMetadata.generateMetadata\";\n    ResolveMetadataSpan[\"generateViewport\"] = \"ResolveMetadata.generateViewport\";\n})(ResolveMetadataSpan || (ResolveMetadataSpan = {}));\nvar MiddlewareSpan;\n(function(MiddlewareSpan) {\n    MiddlewareSpan[\"execute\"] = \"Middleware.execute\";\n})(MiddlewareSpan || (MiddlewareSpan = {}));\nconst NextVanillaSpanAllowlist = [\n    \"Middleware.execute\",\n    \"BaseServer.handleRequest\",\n    \"Render.getServerSideProps\",\n    \"Render.getStaticProps\",\n    \"AppRender.fetch\",\n    \"AppRender.getBodyResult\",\n    \"Render.renderDocument\",\n    \"Node.runHandler\",\n    \"AppRouteRouteHandlers.runHandler\",\n    \"ResolveMetadata.generateMetadata\",\n    \"ResolveMetadata.generateViewport\",\n    \"NextNodeServer.createComponentTree\",\n    \"NextNodeServer.findPageComponents\",\n    \"NextNodeServer.getLayoutOrPageModule\",\n    \"NextNodeServer.startResponse\",\n    \"NextNodeServer.clientComponentLoading\"\n];\nconst LogSpanAllowList = [\n    \"NextNodeServer.findPageComponents\",\n    \"NextNodeServer.createComponentTree\",\n    \"NextNodeServer.clientComponentLoading\"\n];\n\n//# sourceMappingURL=constants.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js":
/*!***********************************************************!*\
  !*** ./node_modules/next/dist/server/lib/trace/tracer.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    SpanKind: function() {\n        return SpanKind;\n    },\n    SpanStatusCode: function() {\n        return SpanStatusCode;\n    },\n    getTracer: function() {\n        return getTracer;\n    }\n});\nconst _constants = __webpack_require__(/*! ./constants */ \"(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\");\nlet api;\n// we want to allow users to use their own version of @opentelemetry/api if they\n// want to, so we try to require it first, and if it fails we fall back to the\n// version that is bundled with Next.js\n// this is because @opentelemetry/api has to be synced with the version of\n// @opentelemetry/tracing that is used, and we don't want to force users to use\n// the version that is bundled with Next.js.\n// the API is ~stable, so this should be fine\nif (false) {} else {\n    try {\n        api = __webpack_require__(/*! @opentelemetry/api */ \"(rsc)/./node_modules/next/dist/compiled/@opentelemetry/api/index.js\");\n    } catch (err) {\n        api = __webpack_require__(/*! next/dist/compiled/@opentelemetry/api */ \"(rsc)/./node_modules/next/dist/compiled/@opentelemetry/api/index.js\");\n    }\n}\nconst { context, propagation, trace, SpanStatusCode, SpanKind, ROOT_CONTEXT } = api;\nconst isPromise = (p)=>{\n    return p !== null && typeof p === \"object\" && typeof p.then === \"function\";\n};\nconst closeSpanWithError = (span, error)=>{\n    if ((error == null ? void 0 : error.bubble) === true) {\n        span.setAttribute(\"next.bubble\", true);\n    } else {\n        if (error) {\n            span.recordException(error);\n        }\n        span.setStatus({\n            code: SpanStatusCode.ERROR,\n            message: error == null ? void 0 : error.message\n        });\n    }\n    span.end();\n};\n/** we use this map to propagate attributes from nested spans to the top span */ const rootSpanAttributesStore = new Map();\nconst rootSpanIdKey = api.createContextKey(\"next.rootSpanId\");\nlet lastSpanId = 0;\nconst getSpanId = ()=>lastSpanId++;\nclass NextTracerImpl {\n    /**\n   * Returns an instance to the trace with configured name.\n   * Since wrap / trace can be defined in any place prior to actual trace subscriber initialization,\n   * This should be lazily evaluated.\n   */ getTracerInstance() {\n        return trace.getTracer(\"next.js\", \"0.0.1\");\n    }\n    getContext() {\n        return context;\n    }\n    getActiveScopeSpan() {\n        return trace.getSpan(context == null ? void 0 : context.active());\n    }\n    withPropagatedContext(carrier, fn, getter) {\n        const activeContext = context.active();\n        if (trace.getSpanContext(activeContext)) {\n            // Active span is already set, too late to propagate.\n            return fn();\n        }\n        const remoteContext = propagation.extract(activeContext, carrier, getter);\n        return context.with(remoteContext, fn);\n    }\n    trace(...args) {\n        var _trace_getSpanContext;\n        const [type, fnOrOptions, fnOrEmpty] = args;\n        // coerce options form overload\n        const { fn, options } = typeof fnOrOptions === \"function\" ? {\n            fn: fnOrOptions,\n            options: {}\n        } : {\n            fn: fnOrEmpty,\n            options: {\n                ...fnOrOptions\n            }\n        };\n        const spanName = options.spanName ?? type;\n        if (!_constants.NextVanillaSpanAllowlist.includes(type) && process.env.NEXT_OTEL_VERBOSE !== \"1\" || options.hideSpan) {\n            return fn();\n        }\n        // Trying to get active scoped span to assign parent. If option specifies parent span manually, will try to use it.\n        let spanContext = this.getSpanContext((options == null ? void 0 : options.parentSpan) ?? this.getActiveScopeSpan());\n        let isRootSpan = false;\n        if (!spanContext) {\n            spanContext = (context == null ? void 0 : context.active()) ?? ROOT_CONTEXT;\n            isRootSpan = true;\n        } else if ((_trace_getSpanContext = trace.getSpanContext(spanContext)) == null ? void 0 : _trace_getSpanContext.isRemote) {\n            isRootSpan = true;\n        }\n        const spanId = getSpanId();\n        options.attributes = {\n            \"next.span_name\": spanName,\n            \"next.span_type\": type,\n            ...options.attributes\n        };\n        return context.with(spanContext.setValue(rootSpanIdKey, spanId), ()=>this.getTracerInstance().startActiveSpan(spanName, options, (span)=>{\n                const startTime = \"performance\" in globalThis ? globalThis.performance.now() : undefined;\n                const onCleanup = ()=>{\n                    rootSpanAttributesStore.delete(spanId);\n                    if (startTime && process.env.NEXT_OTEL_PERFORMANCE_PREFIX && _constants.LogSpanAllowList.includes(type || \"\")) {\n                        performance.measure(`${process.env.NEXT_OTEL_PERFORMANCE_PREFIX}:next-${(type.split(\".\").pop() || \"\").replace(/[A-Z]/g, (match)=>\"-\" + match.toLowerCase())}`, {\n                            start: startTime,\n                            end: performance.now()\n                        });\n                    }\n                };\n                if (isRootSpan) {\n                    rootSpanAttributesStore.set(spanId, new Map(Object.entries(options.attributes ?? {})));\n                }\n                try {\n                    if (fn.length > 1) {\n                        return fn(span, (err)=>closeSpanWithError(span, err));\n                    }\n                    const result = fn(span);\n                    if (isPromise(result)) {\n                        // If there's error make sure it throws\n                        return result.then((res)=>{\n                            span.end();\n                            // Need to pass down the promise result,\n                            // it could be react stream response with error { error, stream }\n                            return res;\n                        }).catch((err)=>{\n                            closeSpanWithError(span, err);\n                            throw err;\n                        }).finally(onCleanup);\n                    } else {\n                        span.end();\n                        onCleanup();\n                    }\n                    return result;\n                } catch (err) {\n                    closeSpanWithError(span, err);\n                    onCleanup();\n                    throw err;\n                }\n            }));\n    }\n    wrap(...args) {\n        const tracer = this;\n        const [name, options, fn] = args.length === 3 ? args : [\n            args[0],\n            {},\n            args[1]\n        ];\n        if (!_constants.NextVanillaSpanAllowlist.includes(name) && process.env.NEXT_OTEL_VERBOSE !== \"1\") {\n            return fn;\n        }\n        return function() {\n            let optionsObj = options;\n            if (typeof optionsObj === \"function\" && typeof fn === \"function\") {\n                optionsObj = optionsObj.apply(this, arguments);\n            }\n            const lastArgId = arguments.length - 1;\n            const cb = arguments[lastArgId];\n            if (typeof cb === \"function\") {\n                const scopeBoundCb = tracer.getContext().bind(context.active(), cb);\n                return tracer.trace(name, optionsObj, (_span, done)=>{\n                    arguments[lastArgId] = function(err) {\n                        done == null ? void 0 : done(err);\n                        return scopeBoundCb.apply(this, arguments);\n                    };\n                    return fn.apply(this, arguments);\n                });\n            } else {\n                return tracer.trace(name, optionsObj, ()=>fn.apply(this, arguments));\n            }\n        };\n    }\n    startSpan(...args) {\n        const [type, options] = args;\n        const spanContext = this.getSpanContext((options == null ? void 0 : options.parentSpan) ?? this.getActiveScopeSpan());\n        return this.getTracerInstance().startSpan(type, options, spanContext);\n    }\n    getSpanContext(parentSpan) {\n        const spanContext = parentSpan ? trace.setSpan(context.active(), parentSpan) : undefined;\n        return spanContext;\n    }\n    getRootSpanAttributes() {\n        const spanId = context.active().getValue(rootSpanIdKey);\n        return rootSpanAttributesStore.get(spanId);\n    }\n}\nconst getTracer = (()=>{\n    const tracer = new NextTracerImpl();\n    return ()=>tracer;\n})();\n\n//# sourceMappingURL=tracer.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js\n");

/***/ })

};
;