const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkAdminPermissions() {
  try {
    console.log('🔍 Checking admin user permissions in database...');

    const adminUser = await prisma.user.findFirst({
      where: { email: '<EMAIL>' },
      select: {
        id: true,
        email: true,
        permissions: true,
        userRoles: {
          include: {
            role: {
              select: {
                name: true
              }
            }
          }
        }
      }
    });

    if (!adminUser) {
      console.log('❌ Admin user not found!');
      return;
    }

    console.log(`✅ Admin user found: ${adminUser.email}`);
    console.log(`🆔 User ID: ${adminUser.id}`);
    console.log(`🎭 Roles: ${adminUser.userRoles.map(ur => ur.role.name).join(', ')}`);
    console.log(`🔑 Permissions count: ${adminUser.permissions.length}`);
    console.log(`📋 Permissions: ${adminUser.permissions.join(', ')}`);

    // Check if key dashboard permissions are present
    const keyPermissions = ['view_dashboard', 'view_properties', 'view_maintenance', 'view_alerts'];
    console.log('\n🎯 Key dashboard permissions:');
    keyPermissions.forEach(perm => {
      const hasPermission = adminUser.permissions.includes(perm);
      console.log(`  ${hasPermission ? '✅' : '❌'} ${perm}`);
    });

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkAdminPermissions();
