import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import 'package:json_annotation/json_annotation.dart';

import '../../../shared/models/api_response.dart';

part 'threshold_api_service.g.dart';

// Helper function to parse string numbers from API
double _doubleFromJson(dynamic value) {
  if (value is String) {
    return double.parse(value);
  } else if (value is num) {
    return value.toDouble();
  }
  throw ArgumentError('Cannot convert $value to double');
}

@RestApi()
abstract class ThresholdApiService {
  factory ThresholdApiService(Dio dio) = _ThresholdApiService;

  @GET('/api/thresholds')
  Future<ApiResponse<List<ThresholdConfig>>> getThresholds({
    @Query('functional_area') String? functionalArea,
    @Query('property_type') String? propertyType,
    @Query('metric_name') String? metricName,
  });

  @POST('/api/thresholds')
  Future<ApiResponse<ThresholdConfig>> createThreshold(@Body() CreateThresholdRequest request);

  @GET('/api/thresholds/{id}')
  Future<ApiResponse<ThresholdConfig>> getThresholdById(@Path('id') String id);

  @PUT('/api/thresholds/{id}')
  Future<ApiResponse<ThresholdConfig>> updateThreshold(
    @Path('id') String id,
    @Body() UpdateThresholdRequest request,
  );

  @DELETE('/api/thresholds/{id}')
  Future<ApiResponse<VoidResponse>> deleteThreshold(@Path('id') String id);

  @POST('/api/thresholds/bulk')
  Future<ApiResponse<List<ThresholdConfig>>> bulkCreateThresholds(@Body() BulkCreateThresholdsRequest request);

  @PUT('/api/thresholds/bulk')
  Future<ApiResponse<List<ThresholdConfig>>> bulkUpdateThresholds(@Body() BulkUpdateThresholdsRequest request);
}

@JsonSerializable()
class ThresholdConfig {
  final String id;
  @JsonKey(name: 'service_type')
  final String serviceType;
  @JsonKey(name: 'metric_name')
  final String metricName;
  @JsonKey(name: 'warning_threshold', fromJson: _doubleFromJson)
  final double warningThreshold;
  @JsonKey(name: 'critical_threshold', fromJson: _doubleFromJson)
  final double criticalThreshold;
  final String unit;
  final String description;
  @JsonKey(name: 'is_active')
  final bool isActive;
  @JsonKey(name: 'created_at')
  final DateTime createdAt;
  @JsonKey(name: 'updated_at')
  final DateTime updatedAt;

  const ThresholdConfig({
    required this.id,
    required this.serviceType,
    required this.metricName,
    required this.warningThreshold,
    required this.criticalThreshold,
    required this.unit,
    required this.description,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
  });

  factory ThresholdConfig.fromJson(Map<String, dynamic> json) => _$ThresholdConfigFromJson(json);
  Map<String, dynamic> toJson() => _$ThresholdConfigToJson(this);
}

@JsonSerializable()
class CreateThresholdRequest {
  @JsonKey(name: 'service_type')
  final String serviceType;
  @JsonKey(name: 'metric_name')
  final String metricName;
  @JsonKey(name: 'warning_threshold')
  final double warningThreshold;
  @JsonKey(name: 'critical_threshold')
  final double criticalThreshold;
  final String unit;
  final String description;
  @JsonKey(name: 'is_active')
  final bool? isActive;

  const CreateThresholdRequest({
    required this.serviceType,
    required this.metricName,
    required this.warningThreshold,
    required this.criticalThreshold,
    required this.unit,
    required this.description,
    this.isActive,
  });

  factory CreateThresholdRequest.fromJson(Map<String, dynamic> json) => _$CreateThresholdRequestFromJson(json);
  Map<String, dynamic> toJson() => _$CreateThresholdRequestToJson(this);
}

@JsonSerializable()
class UpdateThresholdRequest {
  @JsonKey(name: 'service_type')
  final String? serviceType;
  @JsonKey(name: 'metric_name')
  final String? metricName;
  @JsonKey(name: 'warning_threshold')
  final double? warningThreshold;
  @JsonKey(name: 'critical_threshold')
  final double? criticalThreshold;
  final String? unit;
  final String? description;
  @JsonKey(name: 'is_active')
  final bool? isActive;

  const UpdateThresholdRequest({
    this.serviceType,
    this.metricName,
    this.warningThreshold,
    this.criticalThreshold,
    this.unit,
    this.description,
    this.isActive,
  });

  factory UpdateThresholdRequest.fromJson(Map<String, dynamic> json) => _$UpdateThresholdRequestFromJson(json);
  Map<String, dynamic> toJson() => _$UpdateThresholdRequestToJson(this);
}

@JsonSerializable()
class BulkCreateThresholdsRequest {
  final List<CreateThresholdRequest> thresholds;

  const BulkCreateThresholdsRequest({required this.thresholds});

  factory BulkCreateThresholdsRequest.fromJson(Map<String, dynamic> json) => _$BulkCreateThresholdsRequestFromJson(json);
  Map<String, dynamic> toJson() => _$BulkCreateThresholdsRequestToJson(this);
}

@JsonSerializable()
class BulkUpdateThresholdsRequest {
  final List<ThresholdUpdateItem> thresholds;

  const BulkUpdateThresholdsRequest({required this.thresholds});

  factory BulkUpdateThresholdsRequest.fromJson(Map<String, dynamic> json) => _$BulkUpdateThresholdsRequestFromJson(json);
  Map<String, dynamic> toJson() => _$BulkUpdateThresholdsRequestToJson(this);
}

@JsonSerializable()
class ThresholdUpdateItem {
  final String id;
  final UpdateThresholdRequest data;

  const ThresholdUpdateItem({required this.id, required this.data});

  factory ThresholdUpdateItem.fromJson(Map<String, dynamic> json) => _$ThresholdUpdateItemFromJson(json);
  Map<String, dynamic> toJson() => _$ThresholdUpdateItemToJson(this);
}

@JsonSerializable()
class VoidResponse {
  final String message;

  const VoidResponse({required this.message});

  factory VoidResponse.fromJson(Map<String, dynamic> json) => _$VoidResponseFromJson(json);
  Map<String, dynamic> toJson() => _$VoidResponseToJson(this);
}
