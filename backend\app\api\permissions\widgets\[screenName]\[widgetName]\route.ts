import { NextRequest } from 'next/server';
import { requireAuth } from '@/lib/auth';
import { createApiResponse, getRequestBody, handleError, corsHeaders } from '@/lib/utils';
import { prisma } from '@/lib/prisma';
import { validateRequest } from '@/lib/validation';
import Joi from 'joi';

const updateWidgetPermissionSchema = Joi.object({
  required_permissions: Joi.array().items(Joi.string()).optional(),
  allowed_roles: Joi.array().items(Joi.string()).optional(),
  is_visible: Joi.boolean().optional(),
  is_enabled: Joi.boolean().optional(),
});

async function getWidgetPermissionHandler(
  request: NextRequest,
  context: { params: { screenName: string; widgetName: string } },
  currentUser: any
) {
  try {
    const { screenName, widgetName } = context.params;

    const widgetPermission = await prisma.widgetPermission.findUnique({
      where: { 
        screenName_widgetName: {
          screenName: decodeURIComponent(screenName),
          widgetName: decodeURIComponent(widgetName),
        }
      },
    });

    if (!widgetPermission) {
      return Response.json(
        createApiResponse(null, 'Widget permission not found', 'NOT_FOUND'),
        { status: 404 }
      );
    }

    return Response.json(
      createApiResponse({
        permission: {
          id: widgetPermission.id,
          screen_name: widgetPermission.screenName,
          widget_name: widgetPermission.widgetName,
          required_permissions: widgetPermission.requiredPermissions,
          allowed_roles: widgetPermission.allowedRoles,
          is_visible: widgetPermission.isVisible,
          is_enabled: widgetPermission.isEnabled,
          created_at: widgetPermission.createdAt,
          updated_at: widgetPermission.updatedAt,
        },
      }),
      {
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to get widget permission');
  }
}

async function updateWidgetPermissionHandler(
  request: NextRequest,
  context: { params: { screenName: string; widgetName: string } },
  currentUser: any
) {
  try {
    // Check if user has admin permissions
    const hasAdminRole = currentUser.userRoles?.some((ur: any) => ur.role.name === 'admin');
    if (!hasAdminRole) {
      return Response.json(
        createApiResponse(null, 'Insufficient permissions', 'FORBIDDEN'),
        { status: 403 }
      );
    }

    const { screenName, widgetName } = context.params;
    const body = await getRequestBody(request);
    
    // Validate request body
    const validation = validateRequest(updateWidgetPermissionSchema, body);
    if (!validation.isValid) {
      return Response.json(
        createApiResponse(null, 'Validation failed', 'VALIDATION_ERROR'),
        { status: 400 }
      );
    }

    const updateData: any = {};
    if (validation.data.required_permissions !== undefined) {
      updateData.requiredPermissions = validation.data.required_permissions;
    }
    if (validation.data.allowed_roles !== undefined) {
      updateData.allowedRoles = validation.data.allowed_roles;
    }
    if (validation.data.is_visible !== undefined) {
      updateData.isVisible = validation.data.is_visible;
    }
    if (validation.data.is_enabled !== undefined) {
      updateData.isEnabled = validation.data.is_enabled;
    }

    const widgetPermission = await prisma.widgetPermission.update({
      where: { 
        screenName_widgetName: {
          screenName: decodeURIComponent(screenName),
          widgetName: decodeURIComponent(widgetName),
        }
      },
      data: updateData,
    });

    return Response.json(
      createApiResponse({
        permission: {
          id: widgetPermission.id,
          screen_name: widgetPermission.screenName,
          widget_name: widgetPermission.widgetName,
          required_permissions: widgetPermission.requiredPermissions,
          allowed_roles: widgetPermission.allowedRoles,
          is_visible: widgetPermission.isVisible,
          is_enabled: widgetPermission.isEnabled,
          created_at: widgetPermission.createdAt,
          updated_at: widgetPermission.updatedAt,
        },
      }),
      {
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to update widget permission');
  }
}

async function deleteWidgetPermissionHandler(
  request: NextRequest,
  context: { params: { screenName: string; widgetName: string } },
  currentUser: any
) {
  try {
    // Check if user has admin permissions
    const hasAdminRole = currentUser.userRoles?.some((ur: any) => ur.role.name === 'admin');
    if (!hasAdminRole) {
      return Response.json(
        createApiResponse(null, 'Insufficient permissions', 'FORBIDDEN'),
        { status: 403 }
      );
    }

    const { screenName, widgetName } = context.params;

    await prisma.widgetPermission.delete({
      where: { 
        screenName_widgetName: {
          screenName: decodeURIComponent(screenName),
          widgetName: decodeURIComponent(widgetName),
        }
      },
    });

    return Response.json(
      createApiResponse({ message: 'Widget permission deleted successfully' }),
      {
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to delete widget permission');
  }
}

// GET /api/permissions/widgets/[screenName]/[widgetName] - Get specific widget permission
export const GET = requireAuth(getWidgetPermissionHandler);

// PUT /api/permissions/widgets/[screenName]/[widgetName] - Update widget permission
export const PUT = requireAuth(updateWidgetPermissionHandler);

// DELETE /api/permissions/widgets/[screenName]/[widgetName] - Delete widget permission
export const DELETE = requireAuth(deleteWidgetPermissionHandler);

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: corsHeaders(),
  });
}
