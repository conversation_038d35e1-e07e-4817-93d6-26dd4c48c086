#!/usr/bin/env node

const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function resetUserPassword() {
  try {
    // Get command line arguments
    const args = process.argv.slice(2);
    
    if (args.length < 1) {
      console.log('❌ Usage: npm run reset-user-password <email> [password]');
      console.log('   Example: npm run reset-user-password <EMAIL> 123456');
      console.log('   If password is not provided, defaults to "123456"');
      process.exit(1);
    }

    const email = args[0];
    const newPassword = args[1] || '123456';

    console.log(`🔐 Resetting password for user: ${email}\n`);

    // Check if user exists
    const user = await prisma.user.findUnique({
      where: { email },
      select: {
        id: true,
        email: true,
        fullName: true,
        isActive: true,
      },
    });

    if (!user) {
      console.log(`❌ User with email "${email}" not found.`);
      process.exit(1);
    }

    console.log(`👤 Found user: ${user.fullName} (${user.email})`);
    console.log(`📊 Status: ${user.isActive ? 'Active' : 'Inactive'}\n`);

    // Hash the password using the same method as your backend (salt rounds: 12)
    console.log('🔒 Hashing password...');
    const hashedPassword = await bcrypt.hash(newPassword, 12);
    console.log('✅ Password hashed successfully\n');

    // Update user password
    console.log('🔄 Updating password...');
    await prisma.user.update({
      where: { email },
      data: {
        passwordHash: hashedPassword,
        updatedAt: new Date(),
      },
    });

    console.log(`✅ Successfully updated password for ${user.email}`);
    console.log(`🔑 User can now login with:`);
    console.log(`   Email: ${user.email}`);
    console.log(`   Password: ${newPassword}`);

  } catch (error) {
    console.error('❌ Error resetting password:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
resetUserPassword();
