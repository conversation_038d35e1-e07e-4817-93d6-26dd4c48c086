import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../features/auth/presentation/providers/auth_providers.dart';

/// A safe wrapper for BottomNavigationBar that ensures minimum requirements are met
class SafeBottomNavigationBar extends ConsumerWidget {
  final int currentIndex;
  final Function(int) onTap;
  final BottomNavigationBarType? type;
  final Color? selectedItemColor;
  final Color? unselectedItemColor;

  const SafeBottomNavigationBar({
    super.key,
    required this.currentIndex,
    required this.onTap,
    this.type,
    this.selectedItemColor,
    this.unselectedItemColor,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentUser = ref.watch(currentUserProvider);

    return currentUser.when(
      data: (user) {
        if (user == null) {
          return const SizedBox.shrink();
        }

        final items = user.getBottomNavItems();
        final roleColor = user.roleColor;

        // Safety check: BottomNavigationBar requires at least 2 items
        if (items.length < 2) {
          // If we have less than 2 items, don't show the bottom navigation
          return const SizedBox.shrink();
        }

        return BottomNavigationBar(
          type: type ?? BottomNavigationBarType.fixed,
          currentIndex: currentIndex.clamp(0, items.length - 1).toInt(),
          onTap: onTap,
          items: items,
          selectedItemColor: selectedItemColor ?? roleColor,
          unselectedItemColor: unselectedItemColor ?? Colors.grey,
        );
      },
      loading: () => const SizedBox.shrink(),
      error: (error, stackTrace) => const SizedBox.shrink(),
    );
  }
}

/// A widget that provides safe bottom padding for screens with bottom navigation
class SafeBottomPadding extends StatelessWidget {
  final Widget child;
  final double? additionalPadding;

  const SafeBottomPadding({
    super.key,
    required this.child,
    this.additionalPadding,
  });

  @override
  Widget build(BuildContext context) {
    final bottomPadding = MediaQuery.of(context).padding.bottom;
    final navigationBarHeight = kBottomNavigationBarHeight;
    final totalPadding = bottomPadding + navigationBarHeight + (additionalPadding ?? 16.0);

    return Padding(
      padding: EdgeInsets.only(bottom: totalPadding),
      child: child,
    );
  }
}

/// A safe scaffold that handles bottom navigation properly
class SafeScaffoldWithBottomNav extends ConsumerWidget {
  final PreferredSizeWidget? appBar;
  final Widget body;
  final int currentIndex;
  final Function(int) onBottomNavTap;
  final Widget? floatingActionButton;
  final Widget? drawer;
  final Widget? endDrawer;

  const SafeScaffoldWithBottomNav({
    super.key,
    this.appBar,
    required this.body,
    required this.currentIndex,
    required this.onBottomNavTap,
    this.floatingActionButton,
    this.drawer,
    this.endDrawer,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: appBar,
      body: body,
      floatingActionButton: floatingActionButton,
      drawer: drawer,
      endDrawer: endDrawer,
      bottomNavigationBar: SafeBottomNavigationBar(
        currentIndex: currentIndex,
        onTap: onBottomNavTap,
      ),
    );
  }
}
