import { NextRequest } from 'next/server';
import { requireAuth } from '@/lib/auth';
import { createApiResponse, handleError, corsHeaders } from '@/lib/utils';

async function validate<PERSON>oute<PERSON>and<PERSON>(request: NextRequest, context: any, currentUser: any) {
  try {
    // Check if user has admin permissions
    const hasAdminRole = currentUser.roles?.includes('admin');
    if (!hasAdminRole) {
      return Response.json(
        createApiResponse(null, 'Insufficient permissions', 'FORBIDDEN'),
        { status: 403 }
      );
    }

    const url = new URL(request.url);
    const route = url.searchParams.get('route');

    if (!route) {
      return Response.json(
        createApiResponse(false, 'Route parameter is required', 'VALIDATION_ERROR'),
        { status: 400 }
      );
    }

    // Define reserved/existing routes that cannot be used for custom screens
    const reservedRoutes = [
      '/',
      '/login',
      '/dashboard',
      '/properties',
      '/maintenance',
      '/attendance',
      '/security',
      '/reports',
      '/admin',
      '/admin/users',
      '/admin/roles',
      '/admin/permissions',
      '/admin/widgets',
      '/admin/screens',
      '/profile',
      '/settings',
    ];

    // Check if route is reserved
    const isReserved = reservedRoutes.includes(route.toLowerCase());
    
    // Check if route follows valid format (starts with /, contains only valid characters)
    const routePattern = /^\/[a-zA-Z0-9\-_\/]*$/;
    const isValidFormat = routePattern.test(route);

    // Route is valid if it's not reserved and follows valid format
    const isValid = !isReserved && isValidFormat;

    return Response.json(
      createApiResponse(isValid),
      {
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to validate route');
  }
}

// GET /admin/screens/validate-route - Validate route availability
export const GET = requireAuth(validateRouteHandler);

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: corsHeaders(),
  });
}
