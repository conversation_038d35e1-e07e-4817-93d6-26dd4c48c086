import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth, requireRole } from '@/lib/auth';
import { createApiResponse, getRequestBody, handleError, corsHeaders } from '@/lib/utils';
import { validateRequest } from '@/lib/validation';
import Joi from 'joi';

const updateThresholdSchema = Joi.object({
  service_type: Joi.string().optional(),
  metric_name: Joi.string().optional(),
  warning_threshold: Joi.number().optional(),
  critical_threshold: Joi.number().optional(),
  unit: Joi.string().optional(),
  description: Joi.string().optional(),
  is_active: Joi.boolean().optional(),
});

async function getThresholdByIdHandler(
  request: NextRequest,
  context: { params: { id: string } },
  currentUser: any
) {
  try {
    const { id } = context.params;

    // Get threshold configuration by ID
    const threshold = await prisma.thresholdConfig.findUnique({
      where: { id },
    });

    if (!threshold) {
      return Response.json(
        createApiResponse(null, 'Threshold configuration not found', 'NOT_FOUND'),
        { status: 404 }
      );
    }

    // Transform data to match API response format
    const transformedThreshold = {
      id: threshold.id,
      service_type: threshold.serviceType,
      metric_name: threshold.metricName,
      warning_threshold: threshold.warningThreshold,
      critical_threshold: threshold.criticalThreshold,
      unit: threshold.unit,
      description: threshold.description,
      is_active: threshold.isActive,
      created_at: threshold.createdAt,
      updated_at: threshold.updatedAt,
    };

    return Response.json(
      createApiResponse(transformedThreshold),
      { 
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to fetch threshold configuration');
  }
}

async function updateThresholdHandler(
  request: NextRequest,
  context: { params: { id: string } },
  currentUser: any
) {
  try {
    const { id } = context.params;
    const body = await getRequestBody(request);

    // Validate request body
    const validation = validateRequest(updateThresholdSchema, body);
    if (!validation.isValid) {
      return Response.json(
        createApiResponse(null, 'Validation failed', 'VALIDATION_ERROR'),
        { status: 400 }
      );
    }

    // Check if threshold exists
    const existingThreshold = await prisma.thresholdConfig.findUnique({
      where: { id },
    });

    if (!existingThreshold) {
      return Response.json(
        createApiResponse(null, 'Threshold configuration not found', 'NOT_FOUND'),
        { status: 404 }
      );
    }

    const { 
      service_type, 
      metric_name, 
      warning_threshold, 
      critical_threshold, 
      unit, 
      description, 
      is_active 
    } = validation.data;

    // Update threshold configuration
    const updatedThreshold = await prisma.thresholdConfig.update({
      where: { id },
      data: {
        ...(service_type && { serviceType: service_type }),
        ...(metric_name && { metricName: metric_name }),
        ...(warning_threshold !== undefined && { warningThreshold: warning_threshold }),
        ...(critical_threshold !== undefined && { criticalThreshold: critical_threshold }),
        ...(unit && { unit }),
        ...(description !== undefined && { description }),
        ...(is_active !== undefined && { isActive: is_active }),
      },
    });

    return Response.json(
      createApiResponse({
        message: 'Threshold configuration updated successfully',
        threshold: {
          id: updatedThreshold.id,
          service_type: updatedThreshold.serviceType,
          metric_name: updatedThreshold.metricName,
          warning_threshold: updatedThreshold.warningThreshold,
          critical_threshold: updatedThreshold.criticalThreshold,
          unit: updatedThreshold.unit,
          description: updatedThreshold.description,
          is_active: updatedThreshold.isActive,
          created_at: updatedThreshold.createdAt,
          updated_at: updatedThreshold.updatedAt,
        },
      }),
      { 
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to update threshold configuration');
  }
}

async function deleteThresholdHandler(
  request: NextRequest,
  context: { params: { id: string } },
  currentUser: any
) {
  try {
    const { id } = context.params;

    // Check if threshold exists
    const existingThreshold = await prisma.thresholdConfig.findUnique({
      where: { id },
    });

    if (!existingThreshold) {
      return Response.json(
        createApiResponse(null, 'Threshold configuration not found', 'NOT_FOUND'),
        { status: 404 }
      );
    }

    // Delete threshold configuration
    await prisma.thresholdConfig.delete({
      where: { id },
    });

    return Response.json(
      createApiResponse({
        message: 'Threshold configuration deleted successfully',
      }),
      { 
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to delete threshold configuration');
  }
}

export const GET = requireAuth(getThresholdByIdHandler);
export const PUT = requireRole(['admin'])(updateThresholdHandler);
export const DELETE = requireRole(['admin'])(deleteThresholdHandler);

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: corsHeaders(),
  });
}
