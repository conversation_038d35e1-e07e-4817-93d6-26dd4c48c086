const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function cleanupPermissions() {
  try {
    console.log('🧹 Cleaning up existing permission configurations...');

    // Delete all screen permissions
    const deletedScreens = await prisma.screenPermission.deleteMany();
    console.log(`✅ Deleted ${deletedScreens.count} screen permissions`);

    // Delete all widget permissions
    const deletedWidgets = await prisma.widgetPermission.deleteMany();
    console.log(`✅ Deleted ${deletedWidgets.count} widget permissions`);

    console.log('🎉 Cleanup completed successfully!');

  } catch (error) {
    console.error('❌ Error cleaning up permissions:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

cleanupPermissions();
