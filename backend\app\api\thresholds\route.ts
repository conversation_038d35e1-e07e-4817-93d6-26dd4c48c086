import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth, requireRole } from '@/lib/auth';
import { createApiResponse, getQueryParams, getRequestBody, handleError, corsHeaders } from '@/lib/utils';
import { validateRequest } from '@/lib/validation';
import Jo<PERSON> from 'joi';

const createThresholdSchema = Joi.object({
  service_type: Joi.string().required(),
  metric_name: Joi.string().required(),
  warning_threshold: Joi.number().required(),
  critical_threshold: Joi.number().required(),
  unit: Joi.string().required(),
  description: Joi.string().optional(),
  is_active: Joi.boolean().default(true),
});

async function getThresholdsHandler(request: NextRequest, context: any, currentUser: any) {
  try {
    const params = getQueryParams(request);
    const { service_type, metric_name, is_active } = params;

    // Build where clause
    const where: any = {};
    
    if (service_type) {
      where.serviceType = service_type;
    }
    
    if (metric_name) {
      where.metricName = metric_name;
    }
    
    if (is_active !== undefined) {
      where.isActive = is_active === 'true';
    }

    // Get threshold configurations
    const thresholds = await prisma.thresholdConfig.findMany({
      where,
      orderBy: [
        { serviceType: 'asc' },
        { metricName: 'asc' },
      ],
    });

    // Transform data to match API response format
    const transformedThresholds = thresholds.map(threshold => ({
      id: threshold.id,
      service_type: threshold.serviceType,
      metric_name: threshold.metricName,
      warning_threshold: threshold.warningThreshold,
      critical_threshold: threshold.criticalThreshold,
      unit: threshold.unit,
      description: threshold.description,
      is_active: threshold.isActive,
      created_at: threshold.createdAt,
      updated_at: threshold.updatedAt,
    }));

    return Response.json(
      createApiResponse(transformedThresholds),
      { 
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to fetch threshold configurations');
  }
}

async function createThresholdHandler(request: NextRequest, context: any, currentUser: any) {
  try {
    const body = await getRequestBody(request);
    
    // Validate request body
    const validation = validateRequest(createThresholdSchema, body);
    if (!validation.isValid) {
      return Response.json(
        createApiResponse(null, 'Validation failed', 'VALIDATION_ERROR'),
        { status: 400 }
      );
    }

    const { 
      service_type, 
      metric_name, 
      warning_threshold, 
      critical_threshold, 
      unit, 
      description, 
      is_active 
    } = validation.data;

    // Check if threshold already exists for this service type and metric
    const existingThreshold = await prisma.thresholdConfig.findFirst({
      where: {
        serviceType: service_type,
        metricName: metric_name,
      },
    });

    if (existingThreshold) {
      return Response.json(
        createApiResponse(null, 'Threshold configuration already exists for this service type and metric', 'DUPLICATE_THRESHOLD'),
        { status: 409 }
      );
    }

    // Create threshold configuration
    const threshold = await prisma.thresholdConfig.create({
      data: {
        serviceType: service_type,
        metricName: metric_name,
        warningThreshold: warning_threshold,
        criticalThreshold: critical_threshold,
        unit,
        description,
        isActive: is_active,
      },
    });

    return Response.json(
      createApiResponse({
        message: 'Threshold configuration created successfully',
        threshold: {
          id: threshold.id,
          service_type: threshold.serviceType,
          metric_name: threshold.metricName,
          warning_threshold: threshold.warningThreshold,
          critical_threshold: threshold.criticalThreshold,
          unit: threshold.unit,
          description: threshold.description,
          is_active: threshold.isActive,
          created_at: threshold.createdAt,
          updated_at: threshold.updatedAt,
        },
      }),
      { 
        status: 201,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to create threshold configuration');
  }
}

export const GET = requireAuth(getThresholdsHandler);
export const POST = requireRole(['admin'])(createThresholdHandler);

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: corsHeaders(),
  });
}
