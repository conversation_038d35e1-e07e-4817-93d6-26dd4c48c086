// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CreatePropertyRequest _$CreatePropertyRequestFromJson(
        Map<String, dynamic> json) =>
    CreatePropertyRequest(
      name: json['name'] as String,
      type: json['type'] as String,
      parentPropertyId: json['parent_property_id'] as String?,
      address: json['address'] as String?,
      description: json['description'] as String?,
      imageUrl: json['image_url'] as String?,
      location: json['location'] as String?,
      capacity: (json['capacity'] as num?)?.toInt(),
      department: json['department'] as String?,
      projectType: json['project_type'] as String?,
      startDate: json['start_date'] == null
          ? null
          : DateTime.parse(json['start_date'] as String),
      expectedEndDate: json['expected_end_date'] == null
          ? null
          : DateTime.parse(json['expected_end_date'] as String),
      hourlyRateStandard: (json['hourly_rate_standard'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$CreatePropertyRequestToJson(
        CreatePropertyRequest instance) =>
    <String, dynamic>{
      'name': instance.name,
      'type': instance.type,
      'parent_property_id': instance.parentPropertyId,
      'address': instance.address,
      'description': instance.description,
      'image_url': instance.imageUrl,
      'location': instance.location,
      'capacity': instance.capacity,
      'department': instance.department,
      'project_type': instance.projectType,
      'start_date': instance.startDate?.toIso8601String(),
      'expected_end_date': instance.expectedEndDate?.toIso8601String(),
      'hourly_rate_standard': instance.hourlyRateStandard,
    };

UpdatePropertyRequest _$UpdatePropertyRequestFromJson(
        Map<String, dynamic> json) =>
    UpdatePropertyRequest(
      name: json['name'] as String?,
      type: json['type'] as String?,
      address: json['address'] as String?,
      description: json['description'] as String?,
      imageUrl: json['image_url'] as String?,
      isActive: json['is_active'] as bool?,
    );

Map<String, dynamic> _$UpdatePropertyRequestToJson(
        UpdatePropertyRequest instance) =>
    <String, dynamic>{
      'name': instance.name,
      'type': instance.type,
      'address': instance.address,
      'description': instance.description,
      'image_url': instance.imageUrl,
      'is_active': instance.isActive,
    };

CreatePropertyServiceRequest _$CreatePropertyServiceRequestFromJson(
        Map<String, dynamic> json) =>
    CreatePropertyServiceRequest(
      serviceType: json['service_type'] as String,
      status: json['status'] as String,
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$CreatePropertyServiceRequestToJson(
        CreatePropertyServiceRequest instance) =>
    <String, dynamic>{
      'service_type': instance.serviceType,
      'status': instance.status,
      'notes': instance.notes,
    };

UpdatePropertyServiceRequest _$UpdatePropertyServiceRequestFromJson(
        Map<String, dynamic> json) =>
    UpdatePropertyServiceRequest(
      status: json['status'] as String?,
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$UpdatePropertyServiceRequestToJson(
        UpdatePropertyServiceRequest instance) =>
    <String, dynamic>{
      'status': instance.status,
      'notes': instance.notes,
    };
