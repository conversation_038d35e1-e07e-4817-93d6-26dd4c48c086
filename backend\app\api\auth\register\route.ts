import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { hashPassword, requireRole } from '@/lib/auth';
import { validateRequest, registerSchema } from '@/lib/validation';
import { createApiResponse, getRequestBody, handleError, corsHeaders } from '@/lib/utils';

async function registerHandler(request: NextRequest, context: any, currentUser: any) {
  try {
    const body = await getRequestBody(request);

    // Validate request body
    const validation = validateRequest(registerSchema, body);
    if (!validation.isValid) {
      return Response.json(
        createApiResponse(null, 'Validation failed', 'VALIDATION_ERROR'),
        { status: 400 }
      );
    }

    const { email, username, password, full_name, phone } = validation.data;

    // Check if user already exists (email, username, or phone)
    const existingUser = await prisma.user.findFirst({
      where: {
        OR: [
          { email },
          ...(username ? [{ username }] : []),
          ...(phone ? [{ phone }] : []),
        ],
      },
    });

    if (existingUser) {
      let conflictField = 'email';
      if (existingUser.username === username) conflictField = 'username';
      if (existingUser.phone === phone) conflictField = 'phone';

      return Response.json(
        createApiResponse(null, `User with this ${conflictField} already exists`, 'USER_EXISTS'),
        { status: 409 }
      );
    }

    // Hash password
    const hashedPassword = await hashPassword(password);

    // Create new user
    const newUser = await prisma.user.create({
      data: {
        email,
        username,
        passwordHash: hashedPassword,
        fullName: full_name,
        phone,
        isActive: true,
      },
      select: {
        id: true,
        email: true,
        fullName: true,
        phone: true,
        isActive: true,
        createdAt: true,
      },
    });

    // Assign default user role
    const userRole = await prisma.role.findUnique({
      where: { name: 'user' },
    });

    if (userRole) {
      await prisma.userRole.create({
        data: {
          userId: newUser.id,
          roleId: userRole.id,
          assignedBy: currentUser.id,
        },
      });
    }

    return Response.json(
      createApiResponse({
        message: 'User created successfully',
        user: {
          id: newUser.id,
          email: newUser.email,
          username: newUser.username,
          full_name: newUser.fullName,
          phone: newUser.phone,
          is_active: newUser.isActive,
          roles: ['user'],
          created_at: newUser.createdAt,
        },
      }),
      {
        status: 201,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Registration failed');
  }
}

// Only admin users can register new users
export const POST = requireRole(['admin'])(registerHandler);

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: corsHeaders(),
  });
}
