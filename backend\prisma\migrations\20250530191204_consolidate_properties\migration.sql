-- CreateEnum
CREATE TYPE "PropertyType" AS ENUM ('residential', 'office', 'construction_site');

-- <PERSON><PERSON><PERSON>num
CREATE TYPE "ServiceType" AS ENUM ('electricity', 'water', 'internet', 'security', 'ott');

-- CreateEnum
CREATE TYPE "ServiceStatus" AS ENUM ('operational', 'warning', 'critical', 'maintenance');

-- CreateEnum
CREATE TYPE "Priority" AS ENUM ('low', 'medium', 'high', 'critical');

-- CreateEnum
CREATE TYPE "MaintenanceStatus" AS ENUM ('open', 'in_progress', 'resolved', 'closed');

-- CreateEnum
CREATE TYPE "OttStatus" AS ENUM ('active', 'inactive', 'expired');

-- CreateEnum
CREATE TYPE "FunctionProcessStatus" AS ENUM ('active', 'inactive', 'maintenance');

-- CreateEnum
CREATE TYPE "FunctionLogStatus" AS ENUM ('success', 'failure', 'warning');

-- CreateTable
CREATE TABLE "users" (
    "id" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "password_hash" TEXT NOT NULL,
    "full_name" TEXT NOT NULL,
    "phone" TEXT,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "users_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "roles" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "is_system_role" BOOLEAN NOT NULL DEFAULT false,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "roles_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "permissions" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "resource" TEXT NOT NULL,
    "action" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "permissions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "role_permissions" (
    "id" TEXT NOT NULL,
    "role_id" TEXT NOT NULL,
    "permission_id" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "role_permissions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "user_roles" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "role_id" TEXT NOT NULL,
    "assigned_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "assigned_by" TEXT,

    CONSTRAINT "user_roles_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "properties" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "type" "PropertyType" NOT NULL,
    "parent_property_id" TEXT,
    "address" TEXT,
    "description" TEXT,
    "capacity" INTEGER,
    "department" TEXT,
    "project_type" TEXT,
    "start_date" DATE,
    "expected_end_date" DATE,
    "hourly_rate_standard" DECIMAL(10,2),
    "location" TEXT,
    "image_url" TEXT,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "properties_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "property_services" (
    "id" TEXT NOT NULL,
    "property_id" TEXT NOT NULL,
    "service_type" "ServiceType" NOT NULL,
    "status" "ServiceStatus" NOT NULL,
    "last_checked" TIMESTAMP(3),
    "notes" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "property_services_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "maintenance_issues" (
    "id" TEXT NOT NULL,
    "property_id" TEXT NOT NULL,
    "service_type" TEXT,
    "title" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "priority" "Priority" NOT NULL DEFAULT 'medium',
    "status" "MaintenanceStatus" NOT NULL DEFAULT 'open',
    "department" TEXT,
    "assigned_to" TEXT,
    "reported_by" TEXT NOT NULL,
    "resolved_by" TEXT,
    "estimated_cost" DECIMAL(10,2),
    "actual_cost" DECIMAL(10,2),
    "due_date" DATE,
    "resolved_at" TIMESTAMP(3),
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "maintenance_issues_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "property_members" (
    "id" TEXT NOT NULL,
    "property_id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "role" TEXT,
    "position" TEXT,
    "department" TEXT,
    "hourly_rate" DECIMAL(10,2),
    "start_date" DATE,
    "end_date" DATE,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "property_members_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "property_attendance" (
    "id" TEXT NOT NULL,
    "property_id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "date" DATE NOT NULL,
    "check_in_time" TEXT,
    "check_out_time" TEXT,
    "hours_worked" DECIMAL(4,2),
    "notes" TEXT,
    "recorded_by" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "property_attendance_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "generator_fuel_logs" (
    "id" TEXT NOT NULL,
    "property_id" TEXT NOT NULL,
    "fuel_level_liters" DECIMAL(8,2) NOT NULL,
    "consumption_rate" DECIMAL(6,2),
    "runtime_hours" DECIMAL(8,2),
    "efficiency_percentage" DECIMAL(5,2),
    "last_maintenance" DATE,
    "next_maintenance" DATE,
    "notes" TEXT,
    "recorded_by" TEXT,
    "recorded_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "generator_fuel_logs_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "diesel_additions" (
    "id" TEXT NOT NULL,
    "property_id" TEXT NOT NULL,
    "quantity_liters" DECIMAL(8,2) NOT NULL,
    "cost_per_liter" DECIMAL(6,2),
    "total_cost" DECIMAL(10,2),
    "supplier" TEXT,
    "receipt_number" TEXT,
    "added_by" TEXT,
    "added_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "diesel_additions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "security_guard_logs" (
    "id" TEXT NOT NULL,
    "property_id" TEXT NOT NULL,
    "guard_name" TEXT NOT NULL,
    "shift_start" TIMESTAMP(3) NOT NULL,
    "shift_end" TIMESTAMP(3),
    "patrol_rounds" INTEGER NOT NULL DEFAULT 0,
    "incidents_reported" INTEGER NOT NULL DEFAULT 0,
    "visitors_logged" INTEGER NOT NULL DEFAULT 0,
    "notes" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "security_guard_logs_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ott_services" (
    "id" TEXT NOT NULL,
    "property_id" TEXT NOT NULL,
    "service_name" TEXT NOT NULL,
    "subscription_type" TEXT,
    "monthly_cost" DECIMAL(10,2),
    "renewal_date" DATE,
    "status" "OttStatus" NOT NULL DEFAULT 'active',
    "login_credentials" JSONB,
    "notes" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ott_services_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "uptime_reports" (
    "id" TEXT NOT NULL,
    "property_id" TEXT NOT NULL,
    "service_type" TEXT NOT NULL,
    "date" DATE NOT NULL,
    "uptime_percentage" DECIMAL(5,2),
    "downtime_minutes" INTEGER NOT NULL DEFAULT 0,
    "incidents_count" INTEGER NOT NULL DEFAULT 0,
    "notes" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "uptime_reports_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "service_status_logs" (
    "id" TEXT NOT NULL,
    "property_service_id" TEXT NOT NULL,
    "status" TEXT NOT NULL,
    "message" TEXT,
    "details" JSONB,
    "logged_by" TEXT,
    "logged_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "service_status_logs_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "function_processes" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "category" TEXT,
    "input_parameters" JSONB,
    "output_parameters" JSONB,
    "execution_frequency" TEXT,
    "last_executed" TIMESTAMP(3),
    "status" "FunctionProcessStatus" NOT NULL DEFAULT 'active',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "function_processes_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "function_process_logs" (
    "id" TEXT NOT NULL,
    "function_process_id" TEXT NOT NULL,
    "execution_time" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "status" "FunctionLogStatus" NOT NULL,
    "input_data" JSONB,
    "output_data" JSONB,
    "error_message" TEXT,
    "execution_duration_ms" INTEGER,
    "executed_by" TEXT,

    CONSTRAINT "function_process_logs_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "threshold_configs" (
    "id" TEXT NOT NULL,
    "service_type" TEXT NOT NULL,
    "metric_name" TEXT NOT NULL,
    "warning_threshold" DECIMAL(10,2),
    "critical_threshold" DECIMAL(10,2),
    "unit" TEXT,
    "description" TEXT,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "threshold_configs_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "escalation_logs" (
    "id" TEXT NOT NULL,
    "issue_id" TEXT NOT NULL,
    "escalation_level" INTEGER NOT NULL,
    "escalated_to" TEXT,
    "escalated_by" TEXT,
    "escalation_reason" TEXT,
    "escalated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "resolved_at" TIMESTAMP(3),
    "resolution_notes" TEXT,

    CONSTRAINT "escalation_logs_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "users_email_key" ON "users"("email");

-- CreateIndex
CREATE UNIQUE INDEX "roles_name_key" ON "roles"("name");

-- CreateIndex
CREATE UNIQUE INDEX "permissions_name_key" ON "permissions"("name");

-- CreateIndex
CREATE UNIQUE INDEX "role_permissions_role_id_permission_id_key" ON "role_permissions"("role_id", "permission_id");

-- CreateIndex
CREATE UNIQUE INDEX "user_roles_user_id_role_id_key" ON "user_roles"("user_id", "role_id");

-- CreateIndex
CREATE UNIQUE INDEX "property_services_property_id_service_type_key" ON "property_services"("property_id", "service_type");

-- CreateIndex
CREATE UNIQUE INDEX "property_members_property_id_user_id_key" ON "property_members"("property_id", "user_id");

-- CreateIndex
CREATE UNIQUE INDEX "property_attendance_property_id_user_id_date_key" ON "property_attendance"("property_id", "user_id", "date");

-- CreateIndex
CREATE UNIQUE INDEX "uptime_reports_property_id_service_type_date_key" ON "uptime_reports"("property_id", "service_type", "date");

-- CreateIndex
CREATE UNIQUE INDEX "threshold_configs_service_type_metric_name_key" ON "threshold_configs"("service_type", "metric_name");

-- AddForeignKey
ALTER TABLE "role_permissions" ADD CONSTRAINT "role_permissions_role_id_fkey" FOREIGN KEY ("role_id") REFERENCES "roles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "role_permissions" ADD CONSTRAINT "role_permissions_permission_id_fkey" FOREIGN KEY ("permission_id") REFERENCES "permissions"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_roles" ADD CONSTRAINT "user_roles_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_roles" ADD CONSTRAINT "user_roles_role_id_fkey" FOREIGN KEY ("role_id") REFERENCES "roles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_roles" ADD CONSTRAINT "user_roles_assigned_by_fkey" FOREIGN KEY ("assigned_by") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "properties" ADD CONSTRAINT "properties_parent_property_id_fkey" FOREIGN KEY ("parent_property_id") REFERENCES "properties"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "property_services" ADD CONSTRAINT "property_services_property_id_fkey" FOREIGN KEY ("property_id") REFERENCES "properties"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "maintenance_issues" ADD CONSTRAINT "maintenance_issues_property_id_fkey" FOREIGN KEY ("property_id") REFERENCES "properties"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "maintenance_issues" ADD CONSTRAINT "maintenance_issues_reported_by_fkey" FOREIGN KEY ("reported_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "maintenance_issues" ADD CONSTRAINT "maintenance_issues_assigned_to_fkey" FOREIGN KEY ("assigned_to") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "maintenance_issues" ADD CONSTRAINT "maintenance_issues_resolved_by_fkey" FOREIGN KEY ("resolved_by") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "property_members" ADD CONSTRAINT "property_members_property_id_fkey" FOREIGN KEY ("property_id") REFERENCES "properties"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "property_members" ADD CONSTRAINT "property_members_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "property_attendance" ADD CONSTRAINT "property_attendance_property_id_fkey" FOREIGN KEY ("property_id") REFERENCES "properties"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "property_attendance" ADD CONSTRAINT "property_attendance_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "property_attendance" ADD CONSTRAINT "property_attendance_recorded_by_fkey" FOREIGN KEY ("recorded_by") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "generator_fuel_logs" ADD CONSTRAINT "generator_fuel_logs_property_id_fkey" FOREIGN KEY ("property_id") REFERENCES "properties"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "generator_fuel_logs" ADD CONSTRAINT "generator_fuel_logs_recorded_by_fkey" FOREIGN KEY ("recorded_by") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "diesel_additions" ADD CONSTRAINT "diesel_additions_property_id_fkey" FOREIGN KEY ("property_id") REFERENCES "properties"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "diesel_additions" ADD CONSTRAINT "diesel_additions_added_by_fkey" FOREIGN KEY ("added_by") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "security_guard_logs" ADD CONSTRAINT "security_guard_logs_property_id_fkey" FOREIGN KEY ("property_id") REFERENCES "properties"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ott_services" ADD CONSTRAINT "ott_services_property_id_fkey" FOREIGN KEY ("property_id") REFERENCES "properties"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "uptime_reports" ADD CONSTRAINT "uptime_reports_property_id_fkey" FOREIGN KEY ("property_id") REFERENCES "properties"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "service_status_logs" ADD CONSTRAINT "service_status_logs_property_service_id_fkey" FOREIGN KEY ("property_service_id") REFERENCES "property_services"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "service_status_logs" ADD CONSTRAINT "service_status_logs_logged_by_fkey" FOREIGN KEY ("logged_by") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "function_process_logs" ADD CONSTRAINT "function_process_logs_function_process_id_fkey" FOREIGN KEY ("function_process_id") REFERENCES "function_processes"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "function_process_logs" ADD CONSTRAINT "function_process_logs_executed_by_fkey" FOREIGN KEY ("executed_by") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "escalation_logs" ADD CONSTRAINT "escalation_logs_issue_id_fkey" FOREIGN KEY ("issue_id") REFERENCES "maintenance_issues"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "escalation_logs" ADD CONSTRAINT "escalation_logs_escalated_to_fkey" FOREIGN KEY ("escalated_to") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "escalation_logs" ADD CONSTRAINT "escalation_logs_escalated_by_fkey" FOREIGN KEY ("escalated_by") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;
