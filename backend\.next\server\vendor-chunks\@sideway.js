"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@sideway";
exports.ids = ["vendor-chunks/@sideway"];
exports.modules = {

/***/ "(rsc)/./node_modules/@sideway/address/lib/domain.js":
/*!*****************************************************!*\
  !*** ./node_modules/@sideway/address/lib/domain.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nconst Url = __webpack_require__(/*! url */ \"url\");\n\nconst Errors = __webpack_require__(/*! ./errors */ \"(rsc)/./node_modules/@sideway/address/lib/errors.js\");\n\n\nconst internals = {\n    minDomainSegments: 2,\n    nonAsciiRx: /[^\\x00-\\x7f]/,\n    domainControlRx: /[\\x00-\\x20@\\:\\/\\\\#!\\$&\\'\\(\\)\\*\\+,;=\\?]/,                          // Control + space + separators\n    tldSegmentRx: /^[a-zA-Z](?:[a-zA-Z0-9\\-]*[a-zA-Z0-9])?$/,\n    domainSegmentRx: /^[a-zA-Z0-9](?:[a-zA-Z0-9\\-]*[a-zA-Z0-9])?$/,\n    URL: Url.URL || URL                                                                 // $lab:coverage:ignore$\n};\n\n\nexports.analyze = function (domain, options = {}) {\n\n    if (!domain) {                                                                      // Catch null / undefined\n        return Errors.code('DOMAIN_NON_EMPTY_STRING');\n    }\n\n    if (typeof domain !== 'string') {\n        throw new Error('Invalid input: domain must be a string');\n    }\n\n    if (domain.length > 256) {\n        return Errors.code('DOMAIN_TOO_LONG');\n    }\n\n    const ascii = !internals.nonAsciiRx.test(domain);\n    if (!ascii) {\n        if (options.allowUnicode === false) {                                           // Defaults to true\n            return Errors.code('DOMAIN_INVALID_UNICODE_CHARS');\n        }\n\n        domain = domain.normalize('NFC');\n    }\n\n    if (internals.domainControlRx.test(domain)) {\n        return Errors.code('DOMAIN_INVALID_CHARS');\n    }\n\n    domain = internals.punycode(domain);\n\n    // https://tools.ietf.org/html/rfc1035 section 2.3.1\n\n    if (options.allowFullyQualified &&\n        domain[domain.length - 1] === '.') {\n\n        domain = domain.slice(0, -1);\n    }\n\n    const minDomainSegments = options.minDomainSegments || internals.minDomainSegments;\n\n    const segments = domain.split('.');\n    if (segments.length < minDomainSegments) {\n        return Errors.code('DOMAIN_SEGMENTS_COUNT');\n    }\n\n    if (options.maxDomainSegments) {\n        if (segments.length > options.maxDomainSegments) {\n            return Errors.code('DOMAIN_SEGMENTS_COUNT_MAX');\n        }\n    }\n\n    const tlds = options.tlds;\n    if (tlds) {\n        const tld = segments[segments.length - 1].toLowerCase();\n        if (tlds.deny && tlds.deny.has(tld) ||\n            tlds.allow && !tlds.allow.has(tld)) {\n\n            return Errors.code('DOMAIN_FORBIDDEN_TLDS');\n        }\n    }\n\n    for (let i = 0; i < segments.length; ++i) {\n        const segment = segments[i];\n\n        if (!segment.length) {\n            return Errors.code('DOMAIN_EMPTY_SEGMENT');\n        }\n\n        if (segment.length > 63) {\n            return Errors.code('DOMAIN_LONG_SEGMENT');\n        }\n\n        if (i < segments.length - 1) {\n            if (!internals.domainSegmentRx.test(segment)) {\n                return Errors.code('DOMAIN_INVALID_CHARS');\n            }\n        }\n        else {\n            if (!internals.tldSegmentRx.test(segment)) {\n                return Errors.code('DOMAIN_INVALID_TLDS_CHARS');\n            }\n        }\n    }\n\n    return null;\n};\n\n\nexports.isValid = function (domain, options) {\n\n    return !exports.analyze(domain, options);\n};\n\n\ninternals.punycode = function (domain) {\n\n    if (domain.includes('%')) {\n        domain = domain.replace(/%/g, '%25');\n    }\n\n    try {\n        return new internals.URL(`http://${domain}`).host;\n    }\n    catch (err) {\n        return domain;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@sideway/address/lib/domain.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@sideway/address/lib/email.js":
/*!****************************************************!*\
  !*** ./node_modules/@sideway/address/lib/email.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nconst Util = __webpack_require__(/*! util */ \"util\");\n\nconst Domain = __webpack_require__(/*! ./domain */ \"(rsc)/./node_modules/@sideway/address/lib/domain.js\");\nconst Errors = __webpack_require__(/*! ./errors */ \"(rsc)/./node_modules/@sideway/address/lib/errors.js\");\n\n\nconst internals = {\n    nonAsciiRx: /[^\\x00-\\x7f]/,\n    encoder: new (Util.TextEncoder || TextEncoder)()                                            // $lab:coverage:ignore$\n};\n\n\nexports.analyze = function (email, options) {\n\n    return internals.email(email, options);\n};\n\n\nexports.isValid = function (email, options) {\n\n    return !internals.email(email, options);\n};\n\n\ninternals.email = function (email, options = {}) {\n\n    if (typeof email !== 'string') {\n        throw new Error('Invalid input: email must be a string');\n    }\n\n    if (!email) {\n        return Errors.code('EMPTY_STRING');\n    }\n\n    // Unicode\n\n    const ascii = !internals.nonAsciiRx.test(email);\n    if (!ascii) {\n        if (options.allowUnicode === false) {                                                   // Defaults to true\n            return Errors.code('FORBIDDEN_UNICODE');\n        }\n\n        email = email.normalize('NFC');\n    }\n\n    // Basic structure\n\n    const parts = email.split('@');\n    if (parts.length !== 2) {\n        return parts.length > 2 ? Errors.code('MULTIPLE_AT_CHAR') : Errors.code('MISSING_AT_CHAR');\n    }\n\n    const [local, domain] = parts;\n\n    if (!local) {\n        return Errors.code('EMPTY_LOCAL');\n    }\n\n    if (!options.ignoreLength) {\n        if (email.length > 254) {                                           // http://tools.ietf.org/html/rfc5321#section-*******.3\n            return Errors.code('ADDRESS_TOO_LONG');\n        }\n\n        if (internals.encoder.encode(local).length > 64) {                  // http://tools.ietf.org/html/rfc5321#section-*******.1\n            return Errors.code('LOCAL_TOO_LONG');\n        }\n    }\n\n    // Validate parts\n\n    return internals.local(local, ascii) || Domain.analyze(domain, options);\n};\n\n\ninternals.local = function (local, ascii) {\n\n    const segments = local.split('.');\n    for (const segment of segments) {\n        if (!segment.length) {\n            return Errors.code('EMPTY_LOCAL_SEGMENT');\n        }\n\n        if (ascii) {\n            if (!internals.atextRx.test(segment)) {\n                return Errors.code('INVALID_LOCAL_CHARS');\n            }\n\n            continue;\n        }\n\n        for (const char of segment) {\n            if (internals.atextRx.test(char)) {\n                continue;\n            }\n\n            const binary = internals.binary(char);\n            if (!internals.atomRx.test(binary)) {\n                return Errors.code('INVALID_LOCAL_CHARS');\n            }\n        }\n    }\n};\n\n\ninternals.binary = function (char) {\n\n    return Array.from(internals.encoder.encode(char)).map((v) => String.fromCharCode(v)).join('');\n};\n\n\n/*\n    From RFC 5321:\n\n        Mailbox         =   Local-part \"@\" ( Domain / address-literal )\n\n        Local-part      =   Dot-string / Quoted-string\n        Dot-string      =   Atom *(\".\"  Atom)\n        Atom            =   1*atext\n        atext           =   ALPHA / DIGIT / \"!\" / \"#\" / \"$\" / \"%\" / \"&\" / \"'\" / \"*\" / \"+\" / \"-\" / \"/\" / \"=\" / \"?\" / \"^\" / \"_\" / \"`\" / \"{\" / \"|\" / \"}\" / \"~\"\n\n        Domain          =   sub-domain *(\".\" sub-domain)\n        sub-domain      =   Let-dig [Ldh-str]\n        Let-dig         =   ALPHA / DIGIT\n        Ldh-str         =   *( ALPHA / DIGIT / \"-\" ) Let-dig\n\n        ALPHA           =   %x41-5A / %x61-7A        ; a-z, A-Z\n        DIGIT           =   %x30-39                  ; 0-9\n\n    From RFC 6531:\n\n        sub-domain      =/  U-label\n        atext           =/  UTF8-non-ascii\n\n        UTF8-non-ascii  =   UTF8-2 / UTF8-3 / UTF8-4\n\n        UTF8-2          =   %xC2-DF UTF8-tail\n        UTF8-3          =   %xE0 %xA0-BF UTF8-tail /\n                            %xE1-EC 2( UTF8-tail ) /\n                            %xED %x80-9F UTF8-tail /\n                            %xEE-EF 2( UTF8-tail )\n        UTF8-4          =   %xF0 %x90-BF 2( UTF8-tail ) /\n                            %xF1-F3 3( UTF8-tail ) /\n                            %xF4 %x80-8F 2( UTF8-tail )\n\n        UTF8-tail       =   %x80-BF\n\n    Note: The following are not supported:\n\n        RFC 5321: address-literal, Quoted-string\n        RFC 5322: obs-*, CFWS\n*/\n\n\ninternals.atextRx = /^[\\w!#\\$%&'\\*\\+\\-/=\\?\\^`\\{\\|\\}~]+$/;               // _ included in \\w\n\n\ninternals.atomRx = new RegExp([\n\n    //  %xC2-DF UTF8-tail\n    '(?:[\\\\xc2-\\\\xdf][\\\\x80-\\\\xbf])',\n\n    //  %xE0 %xA0-BF UTF8-tail              %xE1-EC 2( UTF8-tail )            %xED %x80-9F UTF8-tail              %xEE-EF 2( UTF8-tail )\n    '(?:\\\\xe0[\\\\xa0-\\\\xbf][\\\\x80-\\\\xbf])|(?:[\\\\xe1-\\\\xec][\\\\x80-\\\\xbf]{2})|(?:\\\\xed[\\\\x80-\\\\x9f][\\\\x80-\\\\xbf])|(?:[\\\\xee-\\\\xef][\\\\x80-\\\\xbf]{2})',\n\n    //  %xF0 %x90-BF 2( UTF8-tail )            %xF1-F3 3( UTF8-tail )            %xF4 %x80-8F 2( UTF8-tail )\n    '(?:\\\\xf0[\\\\x90-\\\\xbf][\\\\x80-\\\\xbf]{2})|(?:[\\\\xf1-\\\\xf3][\\\\x80-\\\\xbf]{3})|(?:\\\\xf4[\\\\x80-\\\\x8f][\\\\x80-\\\\xbf]{2})'\n\n].join('|'));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@sideway/address/lib/email.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@sideway/address/lib/errors.js":
/*!*****************************************************!*\
  !*** ./node_modules/@sideway/address/lib/errors.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n\nexports.codes = {\n    EMPTY_STRING: 'Address must be a non-empty string',\n    FORBIDDEN_UNICODE: 'Address contains forbidden Unicode characters',\n    MULTIPLE_AT_CHAR: 'Address cannot contain more than one @ character',\n    MISSING_AT_CHAR: 'Address must contain one @ character',\n    EMPTY_LOCAL: 'Address local part cannot be empty',\n    ADDRESS_TOO_LONG: 'Address too long',\n    LOCAL_TOO_LONG: 'Address local part too long',\n    EMPTY_LOCAL_SEGMENT: 'Address local part contains empty dot-separated segment',\n    INVALID_LOCAL_CHARS: 'Address local part contains invalid character',\n    DOMAIN_NON_EMPTY_STRING: 'Domain must be a non-empty string',\n    DOMAIN_TOO_LONG: 'Domain too long',\n    DOMAIN_INVALID_UNICODE_CHARS: 'Domain contains forbidden Unicode characters',\n    DOMAIN_INVALID_CHARS: 'Domain contains invalid character',\n    DOMAIN_INVALID_TLDS_CHARS: 'Domain contains invalid tld character',\n    DOMAIN_SEGMENTS_COUNT: 'Domain lacks the minimum required number of segments',\n    DOMAIN_SEGMENTS_COUNT_MAX: 'Domain contains too many segments',\n    DOMAIN_FORBIDDEN_TLDS: 'Domain uses forbidden TLD',\n    DOMAIN_EMPTY_SEGMENT: 'Domain contains empty dot-separated segment',\n    DOMAIN_LONG_SEGMENT: 'Domain contains dot-separated segment that is too long'\n};\n\n\nexports.code = function (code) {\n\n    return { code, error: exports.codes[code] };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@sideway/address/lib/errors.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@sideway/address/lib/ip.js":
/*!*************************************************!*\
  !*** ./node_modules/@sideway/address/lib/ip.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nconst Assert = __webpack_require__(/*! @hapi/hoek/lib/assert */ \"(rsc)/./node_modules/@hapi/hoek/lib/assert.js\");\n\nconst Uri = __webpack_require__(/*! ./uri */ \"(rsc)/./node_modules/@sideway/address/lib/uri.js\");\n\n\nconst internals = {};\n\n\nexports.regex = function (options = {}) {\n\n    // CIDR\n\n    Assert(options.cidr === undefined || typeof options.cidr === 'string', 'options.cidr must be a string');\n    const cidr = options.cidr ? options.cidr.toLowerCase() : 'optional';\n    Assert(['required', 'optional', 'forbidden'].includes(cidr), 'options.cidr must be one of required, optional, forbidden');\n\n    // Versions\n\n    Assert(options.version === undefined || typeof options.version === 'string' || Array.isArray(options.version), 'options.version must be a string or an array of string');\n    let versions = options.version || ['ipv4', 'ipv6', 'ipvfuture'];\n    if (!Array.isArray(versions)) {\n        versions = [versions];\n    }\n\n    Assert(versions.length >= 1, 'options.version must have at least 1 version specified');\n\n    for (let i = 0; i < versions.length; ++i) {\n        Assert(typeof versions[i] === 'string', 'options.version must only contain strings');\n        versions[i] = versions[i].toLowerCase();\n        Assert(['ipv4', 'ipv6', 'ipvfuture'].includes(versions[i]), 'options.version contains unknown version ' + versions[i] + ' - must be one of ipv4, ipv6, ipvfuture');\n    }\n\n    versions = Array.from(new Set(versions));\n\n    // Regex\n\n    const parts = versions.map((version) => {\n\n        // Forbidden\n\n        if (cidr === 'forbidden') {\n            return Uri.ip[version];\n        }\n\n        // Required\n\n        const cidrpart = `\\\\/${version === 'ipv4' ? Uri.ip.v4Cidr : Uri.ip.v6Cidr}`;\n\n        if (cidr === 'required') {\n            return `${Uri.ip[version]}${cidrpart}`;\n        }\n\n        // Optional\n\n        return `${Uri.ip[version]}(?:${cidrpart})?`;\n    });\n\n    const raw = `(?:${parts.join('|')})`;\n    const regex = new RegExp(`^${raw}$`);\n    return { cidr, versions, regex, raw };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@sideway/address/lib/ip.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@sideway/address/lib/tlds.js":
/*!***************************************************!*\
  !*** ./node_modules/@sideway/address/lib/tlds.js ***!
  \***************************************************/
/***/ ((module) => {

eval("\n\nconst internals = {};\n\n\n// http://data.iana.org/TLD/tlds-alpha-by-domain.txt\n// # Version **********, Last Updated Mon Jan 29 07:07:01 2024 UTC\n\n\ninternals.tlds = [\n    'AAA',\n    'AARP',\n    'ABB',\n    'ABBOTT',\n    'ABBVIE',\n    'ABC',\n    'ABLE',\n    'ABOGADO',\n    'ABUDHABI',\n    'AC',\n    'ACADEMY',\n    'ACCENTURE',\n    'ACCOUNTANT',\n    'ACCOUNTANTS',\n    'ACO',\n    'ACTOR',\n    'AD',\n    'ADS',\n    'ADULT',\n    'AE',\n    'AEG',\n    'AERO',\n    'AETNA',\n    'AF',\n    'AFL',\n    'AFRICA',\n    'AG',\n    'AGAKHAN',\n    'AGENCY',\n    'AI',\n    'AIG',\n    'AIRBUS',\n    'AIRFORCE',\n    'AIRTEL',\n    'AKDN',\n    'AL',\n    'ALIBABA',\n    'ALIPAY',\n    'ALLFINANZ',\n    'ALLSTATE',\n    'ALLY',\n    'ALSACE',\n    'ALSTOM',\n    'AM',\n    'AMAZON',\n    'AMERICANEXPRESS',\n    'AMERICANFAMILY',\n    'AMEX',\n    'AMFAM',\n    'AMICA',\n    'AMSTERDAM',\n    'ANALYTICS',\n    'ANDROID',\n    'ANQUAN',\n    'ANZ',\n    'AO',\n    'AOL',\n    'APARTMENTS',\n    'APP',\n    'APPLE',\n    'AQ',\n    'AQUARELLE',\n    'AR',\n    'ARAB',\n    'ARAMCO',\n    'ARCHI',\n    'ARMY',\n    'ARPA',\n    'ART',\n    'ARTE',\n    'AS',\n    'ASDA',\n    'ASIA',\n    'ASSOCIATES',\n    'AT',\n    'ATHLETA',\n    'ATTORNEY',\n    'AU',\n    'AUCTION',\n    'AUDI',\n    'AUDIBLE',\n    'AUDIO',\n    'AUSPOST',\n    'AUTHOR',\n    'AUTO',\n    'AUTOS',\n    'AVIANCA',\n    'AW',\n    'AWS',\n    'AX',\n    'AXA',\n    'AZ',\n    'AZURE',\n    'BA',\n    'BABY',\n    'BAIDU',\n    'BANAMEX',\n    'BAND',\n    'BANK',\n    'BAR',\n    'BARCELONA',\n    'BARCLAYCARD',\n    'BARCLAYS',\n    'BAREFOOT',\n    'BARGAINS',\n    'BASEBALL',\n    'BASKETBALL',\n    'BAUHAUS',\n    'BAYERN',\n    'BB',\n    'BBC',\n    'BBT',\n    'BBVA',\n    'BCG',\n    'BCN',\n    'BD',\n    'BE',\n    'BEATS',\n    'BEAUTY',\n    'BEER',\n    'BENTLEY',\n    'BERLIN',\n    'BEST',\n    'BESTBUY',\n    'BET',\n    'BF',\n    'BG',\n    'BH',\n    'BHARTI',\n    'BI',\n    'BIBLE',\n    'BID',\n    'BIKE',\n    'BING',\n    'BINGO',\n    'BIO',\n    'BIZ',\n    'BJ',\n    'BLACK',\n    'BLACKFRIDAY',\n    'BLOCKBUSTER',\n    'BLOG',\n    'BLOOMBERG',\n    'BLUE',\n    'BM',\n    'BMS',\n    'BMW',\n    'BN',\n    'BNPPARIBAS',\n    'BO',\n    'BOATS',\n    'BOEHRINGER',\n    'BOFA',\n    'BOM',\n    'BOND',\n    'BOO',\n    'BOOK',\n    'BOOKING',\n    'BOSCH',\n    'BOSTIK',\n    'BOSTON',\n    'BOT',\n    'BOUTIQUE',\n    'BOX',\n    'BR',\n    'BRADESCO',\n    'BRIDGESTONE',\n    'BROADWAY',\n    'BROKER',\n    'BROTHER',\n    'BRUSSELS',\n    'BS',\n    'BT',\n    'BUILD',\n    'BUILDERS',\n    'BUSINESS',\n    'BUY',\n    'BUZZ',\n    'BV',\n    'BW',\n    'BY',\n    'BZ',\n    'BZH',\n    'CA',\n    'CAB',\n    'CAFE',\n    'CAL',\n    'CALL',\n    'CALVINKLEIN',\n    'CAM',\n    'CAMERA',\n    'CAMP',\n    'CANON',\n    'CAPETOWN',\n    'CAPITAL',\n    'CAPITALONE',\n    'CAR',\n    'CARAVAN',\n    'CARDS',\n    'CARE',\n    'CAREER',\n    'CAREERS',\n    'CARS',\n    'CASA',\n    'CASE',\n    'CASH',\n    'CASINO',\n    'CAT',\n    'CATERING',\n    'CATHOLIC',\n    'CBA',\n    'CBN',\n    'CBRE',\n    'CC',\n    'CD',\n    'CENTER',\n    'CEO',\n    'CERN',\n    'CF',\n    'CFA',\n    'CFD',\n    'CG',\n    'CH',\n    'CHANEL',\n    'CHANNEL',\n    'CHARITY',\n    'CHASE',\n    'CHAT',\n    'CHEAP',\n    'CHINTAI',\n    'CHRISTMAS',\n    'CHROME',\n    'CHURCH',\n    'CI',\n    'CIPRIANI',\n    'CIRCLE',\n    'CISCO',\n    'CITADEL',\n    'CITI',\n    'CITIC',\n    'CITY',\n    'CK',\n    'CL',\n    'CLAIMS',\n    'CLEANING',\n    'CLICK',\n    'CLINIC',\n    'CLINIQUE',\n    'CLOTHING',\n    'CLOUD',\n    'CLUB',\n    'CLUBMED',\n    'CM',\n    'CN',\n    'CO',\n    'COACH',\n    'CODES',\n    'COFFEE',\n    'COLLEGE',\n    'COLOGNE',\n    'COM',\n    'COMCAST',\n    'COMMBANK',\n    'COMMUNITY',\n    'COMPANY',\n    'COMPARE',\n    'COMPUTER',\n    'COMSEC',\n    'CONDOS',\n    'CONSTRUCTION',\n    'CONSULTING',\n    'CONTACT',\n    'CONTRACTORS',\n    'COOKING',\n    'COOL',\n    'COOP',\n    'CORSICA',\n    'COUNTRY',\n    'COUPON',\n    'COUPONS',\n    'COURSES',\n    'CPA',\n    'CR',\n    'CREDIT',\n    'CREDITCARD',\n    'CREDITUNION',\n    'CRICKET',\n    'CROWN',\n    'CRS',\n    'CRUISE',\n    'CRUISES',\n    'CU',\n    'CUISINELLA',\n    'CV',\n    'CW',\n    'CX',\n    'CY',\n    'CYMRU',\n    'CYOU',\n    'CZ',\n    'DABUR',\n    'DAD',\n    'DANCE',\n    'DATA',\n    'DATE',\n    'DATING',\n    'DATSUN',\n    'DAY',\n    'DCLK',\n    'DDS',\n    'DE',\n    'DEAL',\n    'DEALER',\n    'DEALS',\n    'DEGREE',\n    'DELIVERY',\n    'DELL',\n    'DELOITTE',\n    'DELTA',\n    'DEMOCRAT',\n    'DENTAL',\n    'DENTIST',\n    'DESI',\n    'DESIGN',\n    'DEV',\n    'DHL',\n    'DIAMONDS',\n    'DIET',\n    'DIGITAL',\n    'DIRECT',\n    'DIRECTORY',\n    'DISCOUNT',\n    'DISCOVER',\n    'DISH',\n    'DIY',\n    'DJ',\n    'DK',\n    'DM',\n    'DNP',\n    'DO',\n    'DOCS',\n    'DOCTOR',\n    'DOG',\n    'DOMAINS',\n    'DOT',\n    'DOWNLOAD',\n    'DRIVE',\n    'DTV',\n    'DUBAI',\n    'DUNLOP',\n    'DUPONT',\n    'DURBAN',\n    'DVAG',\n    'DVR',\n    'DZ',\n    'EARTH',\n    'EAT',\n    'EC',\n    'ECO',\n    'EDEKA',\n    'EDU',\n    'EDUCATION',\n    'EE',\n    'EG',\n    'EMAIL',\n    'EMERCK',\n    'ENERGY',\n    'ENGINEER',\n    'ENGINEERING',\n    'ENTERPRISES',\n    'EPSON',\n    'EQUIPMENT',\n    'ER',\n    'ERICSSON',\n    'ERNI',\n    'ES',\n    'ESQ',\n    'ESTATE',\n    'ET',\n    'EU',\n    'EUROVISION',\n    'EUS',\n    'EVENTS',\n    'EXCHANGE',\n    'EXPERT',\n    'EXPOSED',\n    'EXPRESS',\n    'EXTRASPACE',\n    'FAGE',\n    'FAIL',\n    'FAIRWINDS',\n    'FAITH',\n    'FAMILY',\n    'FAN',\n    'FANS',\n    'FARM',\n    'FARMERS',\n    'FASHION',\n    'FAST',\n    'FEDEX',\n    'FEEDBACK',\n    'FERRARI',\n    'FERRERO',\n    'FI',\n    'FIDELITY',\n    'FIDO',\n    'FILM',\n    'FINAL',\n    'FINANCE',\n    'FINANCIAL',\n    'FIRE',\n    'FIRESTONE',\n    'FIRMDALE',\n    'FISH',\n    'FISHING',\n    'FIT',\n    'FITNESS',\n    'FJ',\n    'FK',\n    'FLICKR',\n    'FLIGHTS',\n    'FLIR',\n    'FLORIST',\n    'FLOWERS',\n    'FLY',\n    'FM',\n    'FO',\n    'FOO',\n    'FOOD',\n    'FOOTBALL',\n    'FORD',\n    'FOREX',\n    'FORSALE',\n    'FORUM',\n    'FOUNDATION',\n    'FOX',\n    'FR',\n    'FREE',\n    'FRESENIUS',\n    'FRL',\n    'FROGANS',\n    'FRONTIER',\n    'FTR',\n    'FUJITSU',\n    'FUN',\n    'FUND',\n    'FURNITURE',\n    'FUTBOL',\n    'FYI',\n    'GA',\n    'GAL',\n    'GALLERY',\n    'GALLO',\n    'GALLUP',\n    'GAME',\n    'GAMES',\n    'GAP',\n    'GARDEN',\n    'GAY',\n    'GB',\n    'GBIZ',\n    'GD',\n    'GDN',\n    'GE',\n    'GEA',\n    'GENT',\n    'GENTING',\n    'GEORGE',\n    'GF',\n    'GG',\n    'GGEE',\n    'GH',\n    'GI',\n    'GIFT',\n    'GIFTS',\n    'GIVES',\n    'GIVING',\n    'GL',\n    'GLASS',\n    'GLE',\n    'GLOBAL',\n    'GLOBO',\n    'GM',\n    'GMAIL',\n    'GMBH',\n    'GMO',\n    'GMX',\n    'GN',\n    'GODADDY',\n    'GOLD',\n    'GOLDPOINT',\n    'GOLF',\n    'GOO',\n    'GOODYEAR',\n    'GOOG',\n    'GOOGLE',\n    'GOP',\n    'GOT',\n    'GOV',\n    'GP',\n    'GQ',\n    'GR',\n    'GRAINGER',\n    'GRAPHICS',\n    'GRATIS',\n    'GREEN',\n    'GRIPE',\n    'GROCERY',\n    'GROUP',\n    'GS',\n    'GT',\n    'GU',\n    'GUARDIAN',\n    'GUCCI',\n    'GUGE',\n    'GUIDE',\n    'GUITARS',\n    'GURU',\n    'GW',\n    'GY',\n    'HAIR',\n    'HAMBURG',\n    'HANGOUT',\n    'HAUS',\n    'HBO',\n    'HDFC',\n    'HDFCBANK',\n    'HEALTH',\n    'HEALTHCARE',\n    'HELP',\n    'HELSINKI',\n    'HERE',\n    'HERMES',\n    'HIPHOP',\n    'HISAMITSU',\n    'HITACHI',\n    'HIV',\n    'HK',\n    'HKT',\n    'HM',\n    'HN',\n    'HOCKEY',\n    'HOLDINGS',\n    'HOLIDAY',\n    'HOMEDEPOT',\n    'HOMEGOODS',\n    'HOMES',\n    'HOMESENSE',\n    'HONDA',\n    'HORSE',\n    'HOSPITAL',\n    'HOST',\n    'HOSTING',\n    'HOT',\n    'HOTELS',\n    'HOTMAIL',\n    'HOUSE',\n    'HOW',\n    'HR',\n    'HSBC',\n    'HT',\n    'HU',\n    'HUGHES',\n    'HYATT',\n    'HYUNDAI',\n    'IBM',\n    'ICBC',\n    'ICE',\n    'ICU',\n    'ID',\n    'IE',\n    'IEEE',\n    'IFM',\n    'IKANO',\n    'IL',\n    'IM',\n    'IMAMAT',\n    'IMDB',\n    'IMMO',\n    'IMMOBILIEN',\n    'IN',\n    'INC',\n    'INDUSTRIES',\n    'INFINITI',\n    'INFO',\n    'ING',\n    'INK',\n    'INSTITUTE',\n    'INSURANCE',\n    'INSURE',\n    'INT',\n    'INTERNATIONAL',\n    'INTUIT',\n    'INVESTMENTS',\n    'IO',\n    'IPIRANGA',\n    'IQ',\n    'IR',\n    'IRISH',\n    'IS',\n    'ISMAILI',\n    'IST',\n    'ISTANBUL',\n    'IT',\n    'ITAU',\n    'ITV',\n    'JAGUAR',\n    'JAVA',\n    'JCB',\n    'JE',\n    'JEEP',\n    'JETZT',\n    'JEWELRY',\n    'JIO',\n    'JLL',\n    'JM',\n    'JMP',\n    'JNJ',\n    'JO',\n    'JOBS',\n    'JOBURG',\n    'JOT',\n    'JOY',\n    'JP',\n    'JPMORGAN',\n    'JPRS',\n    'JUEGOS',\n    'JUNIPER',\n    'KAUFEN',\n    'KDDI',\n    'KE',\n    'KERRYHOTELS',\n    'KERRYLOGISTICS',\n    'KERRYPROPERTIES',\n    'KFH',\n    'KG',\n    'KH',\n    'KI',\n    'KIA',\n    'KIDS',\n    'KIM',\n    'KINDLE',\n    'KITCHEN',\n    'KIWI',\n    'KM',\n    'KN',\n    'KOELN',\n    'KOMATSU',\n    'KOSHER',\n    'KP',\n    'KPMG',\n    'KPN',\n    'KR',\n    'KRD',\n    'KRED',\n    'KUOKGROUP',\n    'KW',\n    'KY',\n    'KYOTO',\n    'KZ',\n    'LA',\n    'LACAIXA',\n    'LAMBORGHINI',\n    'LAMER',\n    'LANCASTER',\n    'LAND',\n    'LANDROVER',\n    'LANXESS',\n    'LASALLE',\n    'LAT',\n    'LATINO',\n    'LATROBE',\n    'LAW',\n    'LAWYER',\n    'LB',\n    'LC',\n    'LDS',\n    'LEASE',\n    'LECLERC',\n    'LEFRAK',\n    'LEGAL',\n    'LEGO',\n    'LEXUS',\n    'LGBT',\n    'LI',\n    'LIDL',\n    'LIFE',\n    'LIFEINSURANCE',\n    'LIFESTYLE',\n    'LIGHTING',\n    'LIKE',\n    'LILLY',\n    'LIMITED',\n    'LIMO',\n    'LINCOLN',\n    'LINK',\n    'LIPSY',\n    'LIVE',\n    'LIVING',\n    'LK',\n    'LLC',\n    'LLP',\n    'LOAN',\n    'LOANS',\n    'LOCKER',\n    'LOCUS',\n    'LOL',\n    'LONDON',\n    'LOTTE',\n    'LOTTO',\n    'LOVE',\n    'LPL',\n    'LPLFINANCIAL',\n    'LR',\n    'LS',\n    'LT',\n    'LTD',\n    'LTDA',\n    'LU',\n    'LUNDBECK',\n    'LUXE',\n    'LUXURY',\n    'LV',\n    'LY',\n    'MA',\n    'MADRID',\n    'MAIF',\n    'MAISON',\n    'MAKEUP',\n    'MAN',\n    'MANAGEMENT',\n    'MANGO',\n    'MAP',\n    'MARKET',\n    'MARKETING',\n    'MARKETS',\n    'MARRIOTT',\n    'MARSHALLS',\n    'MATTEL',\n    'MBA',\n    'MC',\n    'MCKINSEY',\n    'MD',\n    'ME',\n    'MED',\n    'MEDIA',\n    'MEET',\n    'MELBOURNE',\n    'MEME',\n    'MEMORIAL',\n    'MEN',\n    'MENU',\n    'MERCKMSD',\n    'MG',\n    'MH',\n    'MIAMI',\n    'MICROSOFT',\n    'MIL',\n    'MINI',\n    'MINT',\n    'MIT',\n    'MITSUBISHI',\n    'MK',\n    'ML',\n    'MLB',\n    'MLS',\n    'MM',\n    'MMA',\n    'MN',\n    'MO',\n    'MOBI',\n    'MOBILE',\n    'MODA',\n    'MOE',\n    'MOI',\n    'MOM',\n    'MONASH',\n    'MONEY',\n    'MONSTER',\n    'MORMON',\n    'MORTGAGE',\n    'MOSCOW',\n    'MOTO',\n    'MOTORCYCLES',\n    'MOV',\n    'MOVIE',\n    'MP',\n    'MQ',\n    'MR',\n    'MS',\n    'MSD',\n    'MT',\n    'MTN',\n    'MTR',\n    'MU',\n    'MUSEUM',\n    'MUSIC',\n    'MV',\n    'MW',\n    'MX',\n    'MY',\n    'MZ',\n    'NA',\n    'NAB',\n    'NAGOYA',\n    'NAME',\n    'NATURA',\n    'NAVY',\n    'NBA',\n    'NC',\n    'NE',\n    'NEC',\n    'NET',\n    'NETBANK',\n    'NETFLIX',\n    'NETWORK',\n    'NEUSTAR',\n    'NEW',\n    'NEWS',\n    'NEXT',\n    'NEXTDIRECT',\n    'NEXUS',\n    'NF',\n    'NFL',\n    'NG',\n    'NGO',\n    'NHK',\n    'NI',\n    'NICO',\n    'NIKE',\n    'NIKON',\n    'NINJA',\n    'NISSAN',\n    'NISSAY',\n    'NL',\n    'NO',\n    'NOKIA',\n    'NORTON',\n    'NOW',\n    'NOWRUZ',\n    'NOWTV',\n    'NP',\n    'NR',\n    'NRA',\n    'NRW',\n    'NTT',\n    'NU',\n    'NYC',\n    'NZ',\n    'OBI',\n    'OBSERVER',\n    'OFFICE',\n    'OKINAWA',\n    'OLAYAN',\n    'OLAYANGROUP',\n    'OLLO',\n    'OM',\n    'OMEGA',\n    'ONE',\n    'ONG',\n    'ONL',\n    'ONLINE',\n    'OOO',\n    'OPEN',\n    'ORACLE',\n    'ORANGE',\n    'ORG',\n    'ORGANIC',\n    'ORIGINS',\n    'OSAKA',\n    'OTSUKA',\n    'OTT',\n    'OVH',\n    'PA',\n    'PAGE',\n    'PANASONIC',\n    'PARIS',\n    'PARS',\n    'PARTNERS',\n    'PARTS',\n    'PARTY',\n    'PAY',\n    'PCCW',\n    'PE',\n    'PET',\n    'PF',\n    'PFIZER',\n    'PG',\n    'PH',\n    'PHARMACY',\n    'PHD',\n    'PHILIPS',\n    'PHONE',\n    'PHOTO',\n    'PHOTOGRAPHY',\n    'PHOTOS',\n    'PHYSIO',\n    'PICS',\n    'PICTET',\n    'PICTURES',\n    'PID',\n    'PIN',\n    'PING',\n    'PINK',\n    'PIONEER',\n    'PIZZA',\n    'PK',\n    'PL',\n    'PLACE',\n    'PLAY',\n    'PLAYSTATION',\n    'PLUMBING',\n    'PLUS',\n    'PM',\n    'PN',\n    'PNC',\n    'POHL',\n    'POKER',\n    'POLITIE',\n    'PORN',\n    'POST',\n    'PR',\n    'PRAMERICA',\n    'PRAXI',\n    'PRESS',\n    'PRIME',\n    'PRO',\n    'PROD',\n    'PRODUCTIONS',\n    'PROF',\n    'PROGRESSIVE',\n    'PROMO',\n    'PROPERTIES',\n    'PROPERTY',\n    'PROTECTION',\n    'PRU',\n    'PRUDENTIAL',\n    'PS',\n    'PT',\n    'PUB',\n    'PW',\n    'PWC',\n    'PY',\n    'QA',\n    'QPON',\n    'QUEBEC',\n    'QUEST',\n    'RACING',\n    'RADIO',\n    'RE',\n    'READ',\n    'REALESTATE',\n    'REALTOR',\n    'REALTY',\n    'RECIPES',\n    'RED',\n    'REDSTONE',\n    'REDUMBRELLA',\n    'REHAB',\n    'REISE',\n    'REISEN',\n    'REIT',\n    'RELIANCE',\n    'REN',\n    'RENT',\n    'RENTALS',\n    'REPAIR',\n    'REPORT',\n    'REPUBLICAN',\n    'REST',\n    'RESTAURANT',\n    'REVIEW',\n    'REVIEWS',\n    'REXROTH',\n    'RICH',\n    'RICHARDLI',\n    'RICOH',\n    'RIL',\n    'RIO',\n    'RIP',\n    'RO',\n    'ROCKS',\n    'RODEO',\n    'ROGERS',\n    'ROOM',\n    'RS',\n    'RSVP',\n    'RU',\n    'RUGBY',\n    'RUHR',\n    'RUN',\n    'RW',\n    'RWE',\n    'RYUKYU',\n    'SA',\n    'SAARLAND',\n    'SAFE',\n    'SAFETY',\n    'SAKURA',\n    'SALE',\n    'SALON',\n    'SAMSCLUB',\n    'SAMSUNG',\n    'SANDVIK',\n    'SANDVIKCOROMANT',\n    'SANOFI',\n    'SAP',\n    'SARL',\n    'SAS',\n    'SAVE',\n    'SAXO',\n    'SB',\n    'SBI',\n    'SBS',\n    'SC',\n    'SCB',\n    'SCHAEFFLER',\n    'SCHMIDT',\n    'SCHOLARSHIPS',\n    'SCHOOL',\n    'SCHULE',\n    'SCHWARZ',\n    'SCIENCE',\n    'SCOT',\n    'SD',\n    'SE',\n    'SEARCH',\n    'SEAT',\n    'SECURE',\n    'SECURITY',\n    'SEEK',\n    'SELECT',\n    'SENER',\n    'SERVICES',\n    'SEVEN',\n    'SEW',\n    'SEX',\n    'SEXY',\n    'SFR',\n    'SG',\n    'SH',\n    'SHANGRILA',\n    'SHARP',\n    'SHAW',\n    'SHELL',\n    'SHIA',\n    'SHIKSHA',\n    'SHOES',\n    'SHOP',\n    'SHOPPING',\n    'SHOUJI',\n    'SHOW',\n    'SI',\n    'SILK',\n    'SINA',\n    'SINGLES',\n    'SITE',\n    'SJ',\n    'SK',\n    'SKI',\n    'SKIN',\n    'SKY',\n    'SKYPE',\n    'SL',\n    'SLING',\n    'SM',\n    'SMART',\n    'SMILE',\n    'SN',\n    'SNCF',\n    'SO',\n    'SOCCER',\n    'SOCIAL',\n    'SOFTBANK',\n    'SOFTWARE',\n    'SOHU',\n    'SOLAR',\n    'SOLUTIONS',\n    'SONG',\n    'SONY',\n    'SOY',\n    'SPA',\n    'SPACE',\n    'SPORT',\n    'SPOT',\n    'SR',\n    'SRL',\n    'SS',\n    'ST',\n    'STADA',\n    'STAPLES',\n    'STAR',\n    'STATEBANK',\n    'STATEFARM',\n    'STC',\n    'STCGROUP',\n    'STOCKHOLM',\n    'STORAGE',\n    'STORE',\n    'STREAM',\n    'STUDIO',\n    'STUDY',\n    'STYLE',\n    'SU',\n    'SUCKS',\n    'SUPPLIES',\n    'SUPPLY',\n    'SUPPORT',\n    'SURF',\n    'SURGERY',\n    'SUZUKI',\n    'SV',\n    'SWATCH',\n    'SWISS',\n    'SX',\n    'SY',\n    'SYDNEY',\n    'SYSTEMS',\n    'SZ',\n    'TAB',\n    'TAIPEI',\n    'TALK',\n    'TAOBAO',\n    'TARGET',\n    'TATAMOTORS',\n    'TATAR',\n    'TATTOO',\n    'TAX',\n    'TAXI',\n    'TC',\n    'TCI',\n    'TD',\n    'TDK',\n    'TEAM',\n    'TECH',\n    'TECHNOLOGY',\n    'TEL',\n    'TEMASEK',\n    'TENNIS',\n    'TEVA',\n    'TF',\n    'TG',\n    'TH',\n    'THD',\n    'THEATER',\n    'THEATRE',\n    'TIAA',\n    'TICKETS',\n    'TIENDA',\n    'TIPS',\n    'TIRES',\n    'TIROL',\n    'TJ',\n    'TJMAXX',\n    'TJX',\n    'TK',\n    'TKMAXX',\n    'TL',\n    'TM',\n    'TMALL',\n    'TN',\n    'TO',\n    'TODAY',\n    'TOKYO',\n    'TOOLS',\n    'TOP',\n    'TORAY',\n    'TOSHIBA',\n    'TOTAL',\n    'TOURS',\n    'TOWN',\n    'TOYOTA',\n    'TOYS',\n    'TR',\n    'TRADE',\n    'TRADING',\n    'TRAINING',\n    'TRAVEL',\n    'TRAVELERS',\n    'TRAVELERSINSURANCE',\n    'TRUST',\n    'TRV',\n    'TT',\n    'TUBE',\n    'TUI',\n    'TUNES',\n    'TUSHU',\n    'TV',\n    'TVS',\n    'TW',\n    'TZ',\n    'UA',\n    'UBANK',\n    'UBS',\n    'UG',\n    'UK',\n    'UNICOM',\n    'UNIVERSITY',\n    'UNO',\n    'UOL',\n    'UPS',\n    'US',\n    'UY',\n    'UZ',\n    'VA',\n    'VACATIONS',\n    'VANA',\n    'VANGUARD',\n    'VC',\n    'VE',\n    'VEGAS',\n    'VENTURES',\n    'VERISIGN',\n    'VERSICHERUNG',\n    'VET',\n    'VG',\n    'VI',\n    'VIAJES',\n    'VIDEO',\n    'VIG',\n    'VIKING',\n    'VILLAS',\n    'VIN',\n    'VIP',\n    'VIRGIN',\n    'VISA',\n    'VISION',\n    'VIVA',\n    'VIVO',\n    'VLAANDEREN',\n    'VN',\n    'VODKA',\n    'VOLVO',\n    'VOTE',\n    'VOTING',\n    'VOTO',\n    'VOYAGE',\n    'VU',\n    'WALES',\n    'WALMART',\n    'WALTER',\n    'WANG',\n    'WANGGOU',\n    'WATCH',\n    'WATCHES',\n    'WEATHER',\n    'WEATHERCHANNEL',\n    'WEBCAM',\n    'WEBER',\n    'WEBSITE',\n    'WED',\n    'WEDDING',\n    'WEIBO',\n    'WEIR',\n    'WF',\n    'WHOSWHO',\n    'WIEN',\n    'WIKI',\n    'WILLIAMHILL',\n    'WIN',\n    'WINDOWS',\n    'WINE',\n    'WINNERS',\n    'WME',\n    'WOLTERSKLUWER',\n    'WOODSIDE',\n    'WORK',\n    'WORKS',\n    'WORLD',\n    'WOW',\n    'WS',\n    'WTC',\n    'WTF',\n    'XBOX',\n    'XEROX',\n    'XFINITY',\n    'XIHUAN',\n    'XIN',\n    'XN--11B4C3D',\n    'XN--1CK2E1B',\n    'XN--1QQW23A',\n    'XN--2SCRJ9C',\n    'XN--30RR7Y',\n    'XN--3BST00M',\n    'XN--3DS443G',\n    'XN--3E0B707E',\n    'XN--3HCRJ9C',\n    'XN--3PXU8K',\n    'XN--42C2D9A',\n    'XN--45BR5CYL',\n    'XN--45BRJ9C',\n    'XN--45Q11C',\n    'XN--4DBRK0CE',\n    'XN--4GBRIM',\n    'XN--54B7FTA0CC',\n    'XN--55QW42G',\n    'XN--55QX5D',\n    'XN--5SU34J936BGSG',\n    'XN--5TZM5G',\n    'XN--6FRZ82G',\n    'XN--6QQ986B3XL',\n    'XN--80ADXHKS',\n    'XN--80AO21A',\n    'XN--80AQECDR1A',\n    'XN--80ASEHDB',\n    'XN--80ASWG',\n    'XN--8Y0A063A',\n    'XN--90A3AC',\n    'XN--90AE',\n    'XN--90AIS',\n    'XN--9DBQ2A',\n    'XN--9ET52U',\n    'XN--9KRT00A',\n    'XN--B4W605FERD',\n    'XN--BCK1B9A5DRE4C',\n    'XN--C1AVG',\n    'XN--C2BR7G',\n    'XN--CCK2B3B',\n    'XN--CCKWCXETD',\n    'XN--CG4BKI',\n    'XN--CLCHC0EA0B2G2A9GCD',\n    'XN--CZR694B',\n    'XN--CZRS0T',\n    'XN--CZRU2D',\n    'XN--D1ACJ3B',\n    'XN--D1ALF',\n    'XN--E1A4C',\n    'XN--ECKVDTC9D',\n    'XN--EFVY88H',\n    'XN--FCT429K',\n    'XN--FHBEI',\n    'XN--FIQ228C5HS',\n    'XN--FIQ64B',\n    'XN--FIQS8S',\n    'XN--FIQZ9S',\n    'XN--FJQ720A',\n    'XN--FLW351E',\n    'XN--FPCRJ9C3D',\n    'XN--FZC2C9E2C',\n    'XN--FZYS8D69UVGM',\n    'XN--G2XX48C',\n    'XN--GCKR3F0F',\n    'XN--GECRJ9C',\n    'XN--GK3AT1E',\n    'XN--H2BREG3EVE',\n    'XN--H2BRJ9C',\n    'XN--H2BRJ9C8C',\n    'XN--HXT814E',\n    'XN--I1B6B1A6A2E',\n    'XN--IMR513N',\n    'XN--IO0A7I',\n    'XN--J1AEF',\n    'XN--J1AMH',\n    'XN--J6W193G',\n    'XN--JLQ480N2RG',\n    'XN--JVR189M',\n    'XN--KCRX77D1X4A',\n    'XN--KPRW13D',\n    'XN--KPRY57D',\n    'XN--KPUT3I',\n    'XN--L1ACC',\n    'XN--LGBBAT1AD8J',\n    'XN--MGB9AWBF',\n    'XN--MGBA3A3EJT',\n    'XN--MGBA3A4F16A',\n    'XN--MGBA7C0BBN0A',\n    'XN--MGBAAM7A8H',\n    'XN--MGBAB2BD',\n    'XN--MGBAH1A3HJKRD',\n    'XN--MGBAI9AZGQP6J',\n    'XN--MGBAYH7GPA',\n    'XN--MGBBH1A',\n    'XN--MGBBH1A71E',\n    'XN--MGBC0A9AZCG',\n    'XN--MGBCA7DZDO',\n    'XN--MGBCPQ6GPA1A',\n    'XN--MGBERP4A5D4AR',\n    'XN--MGBGU82A',\n    'XN--MGBI4ECEXP',\n    'XN--MGBPL2FH',\n    'XN--MGBT3DHD',\n    'XN--MGBTX2B',\n    'XN--MGBX4CD0AB',\n    'XN--MIX891F',\n    'XN--MK1BU44C',\n    'XN--MXTQ1M',\n    'XN--NGBC5AZD',\n    'XN--NGBE9E0A',\n    'XN--NGBRX',\n    'XN--NODE',\n    'XN--NQV7F',\n    'XN--NQV7FS00EMA',\n    'XN--NYQY26A',\n    'XN--O3CW4H',\n    'XN--OGBPF8FL',\n    'XN--OTU796D',\n    'XN--P1ACF',\n    'XN--P1AI',\n    'XN--PGBS0DH',\n    'XN--PSSY2U',\n    'XN--Q7CE6A',\n    'XN--Q9JYB4C',\n    'XN--QCKA1PMC',\n    'XN--QXA6A',\n    'XN--QXAM',\n    'XN--RHQV96G',\n    'XN--ROVU88B',\n    'XN--RVC1E0AM3E',\n    'XN--S9BRJ9C',\n    'XN--SES554G',\n    'XN--T60B56A',\n    'XN--TCKWE',\n    'XN--TIQ49XQYJ',\n    'XN--UNUP4Y',\n    'XN--VERMGENSBERATER-CTB',\n    'XN--VERMGENSBERATUNG-PWB',\n    'XN--VHQUV',\n    'XN--VUQ861B',\n    'XN--W4R85EL8FHU5DNRA',\n    'XN--W4RS40L',\n    'XN--WGBH1C',\n    'XN--WGBL6A',\n    'XN--XHQ521B',\n    'XN--XKC2AL3HYE2A',\n    'XN--XKC2DL3A5EE0H',\n    'XN--Y9A3AQ',\n    'XN--YFRO4I67O',\n    'XN--YGBI2AMMX',\n    'XN--ZFR164B',\n    'XXX',\n    'XYZ',\n    'YACHTS',\n    'YAHOO',\n    'YAMAXUN',\n    'YANDEX',\n    'YE',\n    'YODOBASHI',\n    'YOGA',\n    'YOKOHAMA',\n    'YOU',\n    'YOUTUBE',\n    'YT',\n    'YUN',\n    'ZA',\n    'ZAPPOS',\n    'ZARA',\n    'ZERO',\n    'ZIP',\n    'ZM',\n    'ZONE',\n    'ZUERICH',\n    'ZW'\n];\n\n\n// Keep as upper-case to make updating from source easier\n\nmodule.exports = new Set(internals.tlds.map((tld) => tld.toLowerCase()));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@sideway/address/lib/tlds.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@sideway/address/lib/uri.js":
/*!**************************************************!*\
  !*** ./node_modules/@sideway/address/lib/uri.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nconst Assert = __webpack_require__(/*! @hapi/hoek/lib/assert */ \"(rsc)/./node_modules/@hapi/hoek/lib/assert.js\");\nconst EscapeRegex = __webpack_require__(/*! @hapi/hoek/lib/escapeRegex */ \"(rsc)/./node_modules/@hapi/hoek/lib/escapeRegex.js\");\n\n\nconst internals = {};\n\n\ninternals.generate = function () {\n\n    const rfc3986 = {};\n\n    const hexDigit = '\\\\dA-Fa-f';                                               // HEXDIG = DIGIT / \"A\" / \"B\" / \"C\" / \"D\" / \"E\" / \"F\"\n    const hexDigitOnly = '[' + hexDigit + ']';\n\n    const unreserved = '\\\\w-\\\\.~';                                              // unreserved = ALPHA / DIGIT / \"-\" / \".\" / \"_\" / \"~\"\n    const subDelims = '!\\\\$&\\'\\\\(\\\\)\\\\*\\\\+,;=';                                 // sub-delims = \"!\" / \"$\" / \"&\" / \"'\" / \"(\" / \")\" / \"*\" / \"+\" / \",\" / \";\" / \"=\"\n    const pctEncoded = '%' + hexDigit;                                          // pct-encoded = \"%\" HEXDIG HEXDIG\n    const pchar = unreserved + pctEncoded + subDelims + ':@';                   // pchar = unreserved / pct-encoded / sub-delims / \":\" / \"@\"\n    const pcharOnly = '[' + pchar + ']';\n    const decOctect = '(?:0{0,2}\\\\d|0?[1-9]\\\\d|1\\\\d\\\\d|2[0-4]\\\\d|25[0-5])';     // dec-octet = DIGIT / %x31-39 DIGIT / \"1\" 2DIGIT / \"2\" %x30-34 DIGIT / \"25\" %x30-35  ; 0-9 / 10-99 / 100-199 / 200-249 / 250-255\n\n    rfc3986.ipv4address = '(?:' + decOctect + '\\\\.){3}' + decOctect;            // IPv4address = dec-octet \".\" dec-octet \".\" dec-octet \".\" dec-octet\n\n    /*\n        h16 = 1*4HEXDIG ; 16 bits of address represented in hexadecimal\n        ls32 = ( h16 \":\" h16 ) / IPv4address ; least-significant 32 bits of address\n        IPv6address =                            6( h16 \":\" ) ls32\n                    /                       \"::\" 5( h16 \":\" ) ls32\n                    / [               h16 ] \"::\" 4( h16 \":\" ) ls32\n                    / [ *1( h16 \":\" ) h16 ] \"::\" 3( h16 \":\" ) ls32\n                    / [ *2( h16 \":\" ) h16 ] \"::\" 2( h16 \":\" ) ls32\n                    / [ *3( h16 \":\" ) h16 ] \"::\"    h16 \":\"   ls32\n                    / [ *4( h16 \":\" ) h16 ] \"::\"              ls32\n                    / [ *5( h16 \":\" ) h16 ] \"::\"              h16\n                    / [ *6( h16 \":\" ) h16 ] \"::\"\n    */\n\n    const h16 = hexDigitOnly + '{1,4}';\n    const ls32 = '(?:' + h16 + ':' + h16 + '|' + rfc3986.ipv4address + ')';\n    const IPv6SixHex = '(?:' + h16 + ':){6}' + ls32;\n    const IPv6FiveHex = '::(?:' + h16 + ':){5}' + ls32;\n    const IPv6FourHex = '(?:' + h16 + ')?::(?:' + h16 + ':){4}' + ls32;\n    const IPv6ThreeHex = '(?:(?:' + h16 + ':){0,1}' + h16 + ')?::(?:' + h16 + ':){3}' + ls32;\n    const IPv6TwoHex = '(?:(?:' + h16 + ':){0,2}' + h16 + ')?::(?:' + h16 + ':){2}' + ls32;\n    const IPv6OneHex = '(?:(?:' + h16 + ':){0,3}' + h16 + ')?::' + h16 + ':' + ls32;\n    const IPv6NoneHex = '(?:(?:' + h16 + ':){0,4}' + h16 + ')?::' + ls32;\n    const IPv6NoneHex2 = '(?:(?:' + h16 + ':){0,5}' + h16 + ')?::' + h16;\n    const IPv6NoneHex3 = '(?:(?:' + h16 + ':){0,6}' + h16 + ')?::';\n\n    rfc3986.ipv4Cidr = '(?:\\\\d|[1-2]\\\\d|3[0-2])';                                           // IPv4 cidr = DIGIT / %x31-32 DIGIT / \"3\" %x30-32  ; 0-9 / 10-29 / 30-32\n    rfc3986.ipv6Cidr = '(?:0{0,2}\\\\d|0?[1-9]\\\\d|1[01]\\\\d|12[0-8])';                         // IPv6 cidr = DIGIT / %x31-39 DIGIT / \"1\" %x0-1 DIGIT / \"12\" %x0-8;   0-9 / 10-99 / 100-119 / 120-128\n    rfc3986.ipv6address = '(?:' + IPv6SixHex + '|' + IPv6FiveHex + '|' + IPv6FourHex + '|' + IPv6ThreeHex + '|' + IPv6TwoHex + '|' + IPv6OneHex + '|' + IPv6NoneHex + '|' + IPv6NoneHex2 + '|' + IPv6NoneHex3 + ')';\n    rfc3986.ipvFuture = 'v' + hexDigitOnly + '+\\\\.[' + unreserved + subDelims + ':]+';      // IPvFuture = \"v\" 1*HEXDIG \".\" 1*( unreserved / sub-delims / \":\" )\n\n    rfc3986.scheme = '[a-zA-Z][a-zA-Z\\\\d+-\\\\.]*';                                           // scheme = ALPHA *( ALPHA / DIGIT / \"+\" / \"-\" / \".\" )\n    rfc3986.schemeRegex = new RegExp(rfc3986.scheme);\n\n    const userinfo = '[' + unreserved + pctEncoded + subDelims + ':]*';                     // userinfo = *( unreserved / pct-encoded / sub-delims / \":\" )\n    const IPLiteral = '\\\\[(?:' + rfc3986.ipv6address + '|' + rfc3986.ipvFuture + ')\\\\]';    // IP-literal = \"[\" ( IPv6address / IPvFuture  ) \"]\"\n    const regName = '[' + unreserved + pctEncoded + subDelims + ']{1,255}';                 // reg-name = *( unreserved / pct-encoded / sub-delims )\n    const host = '(?:' + IPLiteral + '|' + rfc3986.ipv4address + '|' + regName + ')';       // host = IP-literal / IPv4address / reg-name\n    const port = '\\\\d*';                                                                    // port = *DIGIT\n    const authority = '(?:' + userinfo + '@)?' + host + '(?::' + port + ')?';               // authority   = [ userinfo \"@\" ] host [ \":\" port ]\n    const authorityCapture = '(?:' + userinfo + '@)?(' + host + ')(?::' + port + ')?';\n\n    /*\n        segment       = *pchar\n        segment-nz    = 1*pchar\n        path          = path-abempty    ; begins with \"/\" '|' is empty\n                    / path-absolute   ; begins with \"/\" but not \"//\"\n                    / path-noscheme   ; begins with a non-colon segment\n                    / path-rootless   ; begins with a segment\n                    / path-empty      ; zero characters\n        path-abempty  = *( \"/\" segment )\n        path-absolute = \"/\" [ segment-nz *( \"/\" segment ) ]\n        path-rootless = segment-nz *( \"/\" segment )\n    */\n\n    const segment = pcharOnly + '*';\n    const segmentNz = pcharOnly + '+';\n    const segmentNzNc = '[' + unreserved + pctEncoded + subDelims + '@' + ']+';\n    const pathEmpty = '';\n    const pathAbEmpty = '(?:\\\\/' + segment + ')*';\n    const pathAbsolute = '\\\\/(?:' + segmentNz + pathAbEmpty + ')?';\n    const pathRootless = segmentNz + pathAbEmpty;\n    const pathNoScheme = segmentNzNc + pathAbEmpty;\n    const pathAbNoAuthority = '(?:\\\\/\\\\/\\\\/' + segment + pathAbEmpty + ')';     // Used by file:///\n\n    // hier-part = \"//\" authority path\n\n    rfc3986.hierPart = '(?:' + '(?:\\\\/\\\\/' + authority + pathAbEmpty + ')' + '|' + pathAbsolute + '|' + pathRootless + '|' + pathAbNoAuthority + ')';\n    rfc3986.hierPartCapture = '(?:' + '(?:\\\\/\\\\/' + authorityCapture + pathAbEmpty + ')' + '|' + pathAbsolute + '|' + pathRootless + ')';\n\n    // relative-part = \"//\" authority path-abempty / path-absolute / path-noscheme / path-empty\n\n    rfc3986.relativeRef = '(?:' + '(?:\\\\/\\\\/' + authority + pathAbEmpty + ')' + '|' + pathAbsolute + '|' + pathNoScheme + '|' + pathEmpty + ')';\n    rfc3986.relativeRefCapture = '(?:' + '(?:\\\\/\\\\/' + authorityCapture + pathAbEmpty + ')' + '|' + pathAbsolute + '|' + pathNoScheme + '|' + pathEmpty + ')';\n\n    // query = *( pchar / \"/\" / \"?\" )\n    // query = *( pchar / \"[\" / \"]\" / \"/\" / \"?\" )\n\n    rfc3986.query = '[' + pchar + '\\\\/\\\\?]*(?=#|$)';                            //Finish matching either at the fragment part '|' end of the line.\n    rfc3986.queryWithSquareBrackets = '[' + pchar + '\\\\[\\\\]\\\\/\\\\?]*(?=#|$)';\n\n    // fragment = *( pchar / \"/\" / \"?\" )\n\n    rfc3986.fragment = '[' + pchar + '\\\\/\\\\?]*';\n\n    return rfc3986;\n};\n\ninternals.rfc3986 = internals.generate();\n\n\nexports.ip = {\n    v4Cidr: internals.rfc3986.ipv4Cidr,\n    v6Cidr: internals.rfc3986.ipv6Cidr,\n    ipv4: internals.rfc3986.ipv4address,\n    ipv6: internals.rfc3986.ipv6address,\n    ipvfuture: internals.rfc3986.ipvFuture\n};\n\n\ninternals.createRegex = function (options) {\n\n    const rfc = internals.rfc3986;\n\n    // Construct expression\n\n    const query = options.allowQuerySquareBrackets ? rfc.queryWithSquareBrackets : rfc.query;\n    const suffix = '(?:\\\\?' + query + ')?' + '(?:#' + rfc.fragment + ')?';\n\n    // relative-ref = relative-part [ \"?\" query ] [ \"#\" fragment ]\n\n    const relative = options.domain ? rfc.relativeRefCapture : rfc.relativeRef;\n\n    if (options.relativeOnly) {\n        return internals.wrap(relative + suffix);\n    }\n\n    // Custom schemes\n\n    let customScheme = '';\n    if (options.scheme) {\n        Assert(options.scheme instanceof RegExp || typeof options.scheme === 'string' || Array.isArray(options.scheme), 'scheme must be a RegExp, String, or Array');\n\n        const schemes = [].concat(options.scheme);\n        Assert(schemes.length >= 1, 'scheme must have at least 1 scheme specified');\n\n        // Flatten the array into a string to be used to match the schemes\n\n        const selections = [];\n        for (let i = 0; i < schemes.length; ++i) {\n            const scheme = schemes[i];\n            Assert(scheme instanceof RegExp || typeof scheme === 'string', 'scheme at position ' + i + ' must be a RegExp or String');\n\n            if (scheme instanceof RegExp) {\n                selections.push(scheme.source.toString());\n            }\n            else {\n                Assert(rfc.schemeRegex.test(scheme), 'scheme at position ' + i + ' must be a valid scheme');\n                selections.push(EscapeRegex(scheme));\n            }\n        }\n\n        customScheme = selections.join('|');\n    }\n\n    // URI = scheme \":\" hier-part [ \"?\" query ] [ \"#\" fragment ]\n\n    const scheme = customScheme ? '(?:' + customScheme + ')' : rfc.scheme;\n    const absolute = '(?:' + scheme + ':' + (options.domain ? rfc.hierPartCapture : rfc.hierPart) + ')';\n    const prefix = options.allowRelative ? '(?:' + absolute + '|' + relative + ')' : absolute;\n    return internals.wrap(prefix + suffix, customScheme);\n};\n\n\ninternals.wrap = function (raw, scheme) {\n\n    raw = `(?=.)(?!https?\\:/(?:$|[^/]))(?!https?\\:///)(?!https?\\:[^/])${raw}`;     // Require at least one character and explicitly forbid 'http:/' or HTTP with empty domain\n\n    return {\n        raw,\n        regex: new RegExp(`^${raw}$`),\n        scheme\n    };\n};\n\n\ninternals.uriRegex = internals.createRegex({});\n\n\nexports.regex = function (options = {}) {\n\n    if (options.scheme ||\n        options.allowRelative ||\n        options.relativeOnly ||\n        options.allowQuerySquareBrackets ||\n        options.domain) {\n\n        return internals.createRegex(options);\n    }\n\n    return internals.uriRegex;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@sideway/address/lib/uri.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@sideway/formula/lib/index.js":
/*!****************************************************!*\
  !*** ./node_modules/@sideway/formula/lib/index.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n\nconst internals = {\n    operators: ['!', '^', '*', '/', '%', '+', '-', '<', '<=', '>', '>=', '==', '!=', '&&', '||', '??'],\n    operatorCharacters: ['!', '^', '*', '/', '%', '+', '-', '<', '=', '>', '&', '|', '?'],\n    operatorsOrder: [['^'], ['*', '/', '%'], ['+', '-'], ['<', '<=', '>', '>='], ['==', '!='], ['&&'], ['||', '??']],\n    operatorsPrefix: ['!', 'n'],\n\n    literals: {\n        '\"': '\"',\n        '`': '`',\n        '\\'': '\\'',\n        '[': ']'\n    },\n\n    numberRx: /^(?:[0-9]*(\\.[0-9]*)?){1}$/,\n    tokenRx: /^[\\w\\$\\#\\.\\@\\:\\{\\}]+$/,\n\n    symbol: Symbol('formula'),\n    settings: Symbol('settings')\n};\n\n\nexports.Parser = class {\n\n    constructor(string, options = {}) {\n\n        if (!options[internals.settings] &&\n            options.constants) {\n\n            for (const constant in options.constants) {\n                const value = options.constants[constant];\n                if (value !== null &&\n                    !['boolean', 'number', 'string'].includes(typeof value)) {\n\n                    throw new Error(`Formula constant ${constant} contains invalid ${typeof value} value type`);\n                }\n            }\n        }\n\n        this.settings = options[internals.settings] ? options : Object.assign({ [internals.settings]: true, constants: {}, functions: {} }, options);\n        this.single = null;\n\n        this._parts = null;\n        this._parse(string);\n    }\n\n    _parse(string) {\n\n        let parts = [];\n        let current = '';\n        let parenthesis = 0;\n        let literal = false;\n\n        const flush = (inner) => {\n\n            if (parenthesis) {\n                throw new Error('Formula missing closing parenthesis');\n            }\n\n            const last = parts.length ? parts[parts.length - 1] : null;\n\n            if (!literal &&\n                !current &&\n                !inner) {\n\n                return;\n            }\n\n            if (last &&\n                last.type === 'reference' &&\n                inner === ')') {                                                                // Function\n\n                last.type = 'function';\n                last.value = this._subFormula(current, last.value);\n                current = '';\n                return;\n            }\n\n            if (inner === ')') {                                                                // Segment\n                const sub = new exports.Parser(current, this.settings);\n                parts.push({ type: 'segment', value: sub });\n            }\n            else if (literal) {\n                if (literal === ']') {                                                          // Reference\n                    parts.push({ type: 'reference', value: current });\n                    current = '';\n                    return;\n                }\n\n                parts.push({ type: 'literal', value: current });                                // Literal\n            }\n            else if (internals.operatorCharacters.includes(current)) {                          // Operator\n                if (last &&\n                    last.type === 'operator' &&\n                    internals.operators.includes(last.value + current)) {                       // 2 characters operator\n\n                    last.value += current;\n                }\n                else {\n                    parts.push({ type: 'operator', value: current });\n                }\n            }\n            else if (current.match(internals.numberRx)) {                                       // Number\n                parts.push({ type: 'constant', value: parseFloat(current) });\n            }\n            else if (this.settings.constants[current] !== undefined) {                          // Constant\n                parts.push({ type: 'constant', value: this.settings.constants[current] });\n            }\n            else {                                                                              // Reference\n                if (!current.match(internals.tokenRx)) {\n                    throw new Error(`Formula contains invalid token: ${current}`);\n                }\n\n                parts.push({ type: 'reference', value: current });\n            }\n\n            current = '';\n        };\n\n        for (const c of string) {\n            if (literal) {\n                if (c === literal) {\n                    flush();\n                    literal = false;\n                }\n                else {\n                    current += c;\n                }\n            }\n            else if (parenthesis) {\n                if (c === '(') {\n                    current += c;\n                    ++parenthesis;\n                }\n                else if (c === ')') {\n                    --parenthesis;\n                    if (!parenthesis) {\n                        flush(c);\n                    }\n                    else {\n                        current += c;\n                    }\n                }\n                else {\n                    current += c;\n                }\n            }\n            else if (c in internals.literals) {\n                literal = internals.literals[c];\n            }\n            else if (c === '(') {\n                flush();\n                ++parenthesis;\n            }\n            else if (internals.operatorCharacters.includes(c)) {\n                flush();\n                current = c;\n                flush();\n            }\n            else if (c !== ' ') {\n                current += c;\n            }\n            else {\n                flush();\n            }\n        }\n\n        flush();\n\n        // Replace prefix - to internal negative operator\n\n        parts = parts.map((part, i) => {\n\n            if (part.type !== 'operator' ||\n                part.value !== '-' ||\n                i && parts[i - 1].type !== 'operator') {\n\n                return part;\n            }\n\n            return { type: 'operator', value: 'n' };\n        });\n\n        // Validate tokens order\n\n        let operator = false;\n        for (const part of parts) {\n            if (part.type === 'operator') {\n                if (internals.operatorsPrefix.includes(part.value)) {\n                    continue;\n                }\n\n                if (!operator) {\n                    throw new Error('Formula contains an operator in invalid position');\n                }\n\n                if (!internals.operators.includes(part.value)) {\n                    throw new Error(`Formula contains an unknown operator ${part.value}`);\n                }\n            }\n            else if (operator) {\n                throw new Error('Formula missing expected operator');\n            }\n\n            operator = !operator;\n        }\n\n        if (!operator) {\n            throw new Error('Formula contains invalid trailing operator');\n        }\n\n        // Identify single part\n\n        if (parts.length === 1 &&\n            ['reference', 'literal', 'constant'].includes(parts[0].type)) {\n\n            this.single = { type: parts[0].type === 'reference' ? 'reference' : 'value', value: parts[0].value };\n        }\n\n        // Process parts\n\n        this._parts = parts.map((part) => {\n\n            // Operators\n\n            if (part.type === 'operator') {\n                return internals.operatorsPrefix.includes(part.value) ? part : part.value;\n            }\n\n            // Literals, constants, segments\n\n            if (part.type !== 'reference') {\n                return part.value;\n            }\n\n            // References\n\n            if (this.settings.tokenRx &&\n                !this.settings.tokenRx.test(part.value)) {\n\n                throw new Error(`Formula contains invalid reference ${part.value}`);\n            }\n\n            if (this.settings.reference) {\n                return this.settings.reference(part.value);\n            }\n\n            return internals.reference(part.value);\n        });\n    }\n\n    _subFormula(string, name) {\n\n        const method = this.settings.functions[name];\n        if (typeof method !== 'function') {\n            throw new Error(`Formula contains unknown function ${name}`);\n        }\n\n        let args = [];\n        if (string) {\n            let current = '';\n            let parenthesis = 0;\n            let literal = false;\n\n            const flush = () => {\n\n                if (!current) {\n                    throw new Error(`Formula contains function ${name} with invalid arguments ${string}`);\n                }\n\n                args.push(current);\n                current = '';\n            };\n\n            for (let i = 0; i < string.length; ++i) {\n                const c = string[i];\n                if (literal) {\n                    current += c;\n                    if (c === literal) {\n                        literal = false;\n                    }\n                }\n                else if (c in internals.literals &&\n                    !parenthesis) {\n\n                    current += c;\n                    literal = internals.literals[c];\n                }\n                else if (c === ',' &&\n                    !parenthesis) {\n\n                    flush();\n                }\n                else {\n                    current += c;\n                    if (c === '(') {\n                        ++parenthesis;\n                    }\n                    else if (c === ')') {\n                        --parenthesis;\n                    }\n                }\n            }\n\n            flush();\n        }\n\n        args = args.map((arg) => new exports.Parser(arg, this.settings));\n\n        return function (context) {\n\n            const innerValues = [];\n            for (const arg of args) {\n                innerValues.push(arg.evaluate(context));\n            }\n\n            return method.call(context, ...innerValues);\n        };\n    }\n\n    evaluate(context) {\n\n        const parts = this._parts.slice();\n\n        // Prefix operators\n\n        for (let i = parts.length - 2; i >= 0; --i) {\n            const part = parts[i];\n            if (part &&\n                part.type === 'operator') {\n\n                const current = parts[i + 1];\n                parts.splice(i + 1, 1);\n                const value = internals.evaluate(current, context);\n                parts[i] = internals.single(part.value, value);\n            }\n        }\n\n        // Left-right operators\n\n        internals.operatorsOrder.forEach((set) => {\n\n            for (let i = 1; i < parts.length - 1;) {\n                if (set.includes(parts[i])) {\n                    const operator = parts[i];\n                    const left = internals.evaluate(parts[i - 1], context);\n                    const right = internals.evaluate(parts[i + 1], context);\n\n                    parts.splice(i, 2);\n                    const result = internals.calculate(operator, left, right);\n                    parts[i - 1] = result === 0 ? 0 : result;                               // Convert -0\n                }\n                else {\n                    i += 2;\n                }\n            }\n        });\n\n        return internals.evaluate(parts[0], context);\n    }\n};\n\n\nexports.Parser.prototype[internals.symbol] = true;\n\n\ninternals.reference = function (name) {\n\n    return function (context) {\n\n        return context && context[name] !== undefined ? context[name] : null;\n    };\n};\n\n\ninternals.evaluate = function (part, context) {\n\n    if (part === null) {\n        return null;\n    }\n\n    if (typeof part === 'function') {\n        return part(context);\n    }\n\n    if (part[internals.symbol]) {\n        return part.evaluate(context);\n    }\n\n    return part;\n};\n\n\ninternals.single = function (operator, value) {\n\n    if (operator === '!') {\n        return value ? false : true;\n    }\n\n    // operator === 'n'\n\n    const negative = -value;\n    if (negative === 0) {       // Override -0\n        return 0;\n    }\n\n    return negative;\n};\n\n\ninternals.calculate = function (operator, left, right) {\n\n    if (operator === '??') {\n        return internals.exists(left) ? left : right;\n    }\n\n    if (typeof left === 'string' ||\n        typeof right === 'string') {\n\n        if (operator === '+') {\n            left = internals.exists(left) ? left : '';\n            right = internals.exists(right) ? right : '';\n            return left + right;\n        }\n    }\n    else {\n        switch (operator) {\n            case '^': return Math.pow(left, right);\n            case '*': return left * right;\n            case '/': return left / right;\n            case '%': return left % right;\n            case '+': return left + right;\n            case '-': return left - right;\n        }\n    }\n\n    switch (operator) {\n        case '<': return left < right;\n        case '<=': return left <= right;\n        case '>': return left > right;\n        case '>=': return left >= right;\n        case '==': return left === right;\n        case '!=': return left !== right;\n        case '&&': return left && right;\n        case '||': return left || right;\n    }\n\n    return null;\n};\n\n\ninternals.exists = function (value) {\n\n    return value !== null && value !== undefined;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@sideway/formula/lib/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@sideway/pinpoint/lib/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/@sideway/pinpoint/lib/index.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n\nconst internals = {};\n\n\nexports.location = function (depth = 0) {\n\n    const orig = Error.prepareStackTrace;\n    Error.prepareStackTrace = (ignore, stack) => stack;\n\n    const capture = {};\n    Error.captureStackTrace(capture, this);\n    const line = capture.stack[depth + 1];\n\n    Error.prepareStackTrace = orig;\n\n    return {\n        filename: line.getFileName(),\n        line: line.getLineNumber()\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQHNpZGV3YXkvcGlucG9pbnQvbGliL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViOzs7QUFHQSxnQkFBZ0I7O0FBRWhCO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zcnNyLXByb3BlcnR5LW1hbmFnZW1lbnQtYmFja2VuZC8uL25vZGVfbW9kdWxlcy9Ac2lkZXdheS9waW5wb2ludC9saWIvaW5kZXguanM/NDlmOCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmNvbnN0IGludGVybmFscyA9IHt9O1xuXG5cbmV4cG9ydHMubG9jYXRpb24gPSBmdW5jdGlvbiAoZGVwdGggPSAwKSB7XG5cbiAgICBjb25zdCBvcmlnID0gRXJyb3IucHJlcGFyZVN0YWNrVHJhY2U7XG4gICAgRXJyb3IucHJlcGFyZVN0YWNrVHJhY2UgPSAoaWdub3JlLCBzdGFjaykgPT4gc3RhY2s7XG5cbiAgICBjb25zdCBjYXB0dXJlID0ge307XG4gICAgRXJyb3IuY2FwdHVyZVN0YWNrVHJhY2UoY2FwdHVyZSwgdGhpcyk7XG4gICAgY29uc3QgbGluZSA9IGNhcHR1cmUuc3RhY2tbZGVwdGggKyAxXTtcblxuICAgIEVycm9yLnByZXBhcmVTdGFja1RyYWNlID0gb3JpZztcblxuICAgIHJldHVybiB7XG4gICAgICAgIGZpbGVuYW1lOiBsaW5lLmdldEZpbGVOYW1lKCksXG4gICAgICAgIGxpbmU6IGxpbmUuZ2V0TGluZU51bWJlcigpXG4gICAgfTtcbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@sideway/pinpoint/lib/index.js\n");

/***/ })

};
;