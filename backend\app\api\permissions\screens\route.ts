import { NextRequest } from 'next/server';
import { requireAuth } from '@/lib/auth';
import { createApiResponse, getRequestBody, handleError, corsHeaders } from '@/lib/utils';
import { prisma } from '@/lib/prisma';
import { validateRequest } from '@/lib/validation';
import Joi from 'joi';

const createScreenPermissionSchema = Joi.object({
  screen_name: Joi.string().required(),
  required_permissions: Joi.array().items(Joi.string()).default([]),
  allowed_roles: Joi.array().items(Joi.string()).default([]),
  is_enabled: Joi.boolean().default(true),
});

const updateScreenPermissionSchema = Joi.object({
  required_permissions: Joi.array().items(Joi.string()).optional(),
  allowed_roles: Joi.array().items(Joi.string()).optional(),
  is_enabled: Joi.boolean().optional(),
});

async function getScreenPermissionsHandler(request: NextRequest, context: any, currentUser: any) {
  try {
    // Check if user has admin permissions
    const hasAdminRole = currentUser.userRoles?.some((ur: any) => ur.role.name === 'admin');
    if (!hasAdminRole) {
      return Response.json(
        createApiResponse(null, 'Insufficient permissions', 'FORBIDDEN'),
        { status: 403 }
      );
    }

    const screenPermissions = await prisma.screenPermission.findMany({
      orderBy: { screenName: 'asc' },
    });

    return Response.json(
      createApiResponse({
        permissions: screenPermissions.map(permission => ({
          id: permission.id,
          screen_name: permission.screenName,
          required_permissions: permission.requiredPermissions,
          allowed_roles: permission.allowedRoles,
          is_enabled: permission.isEnabled,
          created_at: permission.createdAt,
          updated_at: permission.updatedAt,
        })),
        count: screenPermissions.length,
      }),
      {
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to get screen permissions');
  }
}

async function createScreenPermissionHandler(request: NextRequest, context: any, currentUser: any) {
  try {
    // Check if user has admin permissions
    const hasAdminRole = currentUser.userRoles?.some((ur: any) => ur.role.name === 'admin');
    if (!hasAdminRole) {
      return Response.json(
        createApiResponse(null, 'Insufficient permissions', 'FORBIDDEN'),
        { status: 403 }
      );
    }

    const body = await getRequestBody(request);
    
    // Validate request body
    const validation = validateRequest(createScreenPermissionSchema, body);
    if (!validation.isValid) {
      return Response.json(
        createApiResponse(null, 'Validation failed', 'VALIDATION_ERROR'),
        { status: 400 }
      );
    }

    const { screen_name, required_permissions, allowed_roles, is_enabled } = validation.data;

    // Check if screen permission already exists
    const existingPermission = await prisma.screenPermission.findUnique({
      where: { screenName: screen_name },
    });

    if (existingPermission) {
      return Response.json(
        createApiResponse(null, 'Screen permission already exists', 'CONFLICT'),
        { status: 409 }
      );
    }

    const screenPermission = await prisma.screenPermission.create({
      data: {
        screenName: screen_name,
        requiredPermissions: required_permissions,
        allowedRoles: allowed_roles,
        isEnabled: is_enabled,
      },
    });

    return Response.json(
      createApiResponse({
        permission: {
          id: screenPermission.id,
          screen_name: screenPermission.screenName,
          required_permissions: screenPermission.requiredPermissions,
          allowed_roles: screenPermission.allowedRoles,
          is_enabled: screenPermission.isEnabled,
          created_at: screenPermission.createdAt,
          updated_at: screenPermission.updatedAt,
        },
      }),
      {
        status: 201,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to create screen permission');
  }
}

// GET /api/permissions/screens - Get all screen permissions
export const GET = requireAuth(getScreenPermissionsHandler);

// POST /api/permissions/screens - Create screen permission
export const POST = requireAuth(createScreenPermissionHandler);

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: corsHeaders(),
  });
}
